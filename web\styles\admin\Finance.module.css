/* Стили для страницы финансов админ-панели */

.container {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title {
  display: flex;
  align-items: center;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.titleIcon {
  margin-right: 12px;
  color: #27ae60;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.actionButton {
  border-color: #27ae60;
  color: #27ae60;
  transition: all 0.3s ease;
}

.actionButton:hover {
  background-color: #27ae60;
  color: white;
  transform: translateY(-2px);
}

.loader {
  margin-bottom: 24px;
  border-radius: 4px;
}

.errorAlert {
  margin-bottom: 24px;
  border-radius: 12px;
}

/* Метрики */
.metricsGrid {
  margin-bottom: 32px;
}

.metricCard {
  height: 100%;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metricCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
}

.metricCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.metricContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.metricIcon {
  font-size: 48px;
  opacity: 0.7;
  color: #27ae60;
}

/* Вкладки */
.tabsCard {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tabsHeader {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.tabs {
  padding: 0 24px;
}

/* Графики */
.chartPaper {
  padding: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.chartPaper:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.chartContainer {
  height: 400px;
  position: relative;
  margin-top: 16px;
}

/* Статистика */
.statsContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 16px;
}

.statItem {
  padding: 16px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  border-left: 4px solid #27ae60;
  transition: all 0.3s ease;
}

.statItem:hover {
  background: rgba(248, 249, 250, 1);
  transform: translateX(4px);
}

/* Фильтры */
.filtersGrid {
  margin-bottom: 24px;
}

/* Таблица */
.tableContainer {
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
}

.tableRow {
  transition: all 0.3s ease;
}

.tableRow:hover {
  background-color: rgba(39, 174, 96, 0.05);
}

/* Подписки */
.subscriptionStats {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 16px;
}

.subscriptionItem {
  padding: 20px;
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.subscriptionItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

/* Методы оплаты */
.paymentMethodsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.paymentMethodItem {
  padding: 16px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(39, 174, 96, 0.2);
  transition: all 0.3s ease;
}

.paymentMethodItem:hover {
  background: rgba(248, 249, 250, 1);
  border-color: #27ae60;
  transform: translateX(4px);
}

/* Адаптивность */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .headerActions {
    flex-direction: column;
    width: 100%;
    gap: 12px;
  }

  .title {
    font-size: 1.5rem;
  }

  .metricContent {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .metricIcon {
    font-size: 36px;
  }

  .chartContainer {
    height: 300px;
  }

  .statsContainer,
  .subscriptionStats,
  .paymentMethodsList {
    gap: 12px;
  }

  .statItem,
  .subscriptionItem,
  .paymentMethodItem {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px;
  }

  .header {
    padding: 16px;
  }

  .title {
    font-size: 1.25rem;
  }

  .chartContainer {
    height: 250px;
  }

  .tabs {
    padding: 0 12px;
  }

  .chartPaper {
    padding: 16px;
  }
}

/* Анимации */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.metricCard,
.chartPaper,
.statItem,
.subscriptionItem,
.paymentMethodItem {
  animation: fadeInUp 0.6s ease-out;
}

.metricCard:nth-child(1) { animation-delay: 0.1s; }
.metricCard:nth-child(2) { animation-delay: 0.2s; }
.metricCard:nth-child(3) { animation-delay: 0.3s; }
.metricCard:nth-child(4) { animation-delay: 0.4s; }

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }

  .header,
  .metricCard,
  .chartPaper,
  .tabsCard {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .title {
    color: #ffffff;
  }

  .statItem,
  .paymentMethodItem {
    background: rgba(40, 40, 40, 0.8);
    border-color: rgba(39, 174, 96, 0.3);
  }

  .statItem:hover,
  .paymentMethodItem:hover {
    background: rgba(40, 40, 40, 1);
  }
}

/* Эффекты при наведении */
.metricCard {
  position: relative;
  overflow: hidden;
}

.metricCard::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(39, 174, 96, 0.1) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.metricCard:hover::after {
  width: 200px;
  height: 200px;
}
