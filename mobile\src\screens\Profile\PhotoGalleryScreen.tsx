import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  Modal,
  ActivityIndicator,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { launchImageLibrary, launchCamera, ImagePickerResponse } from 'react-native-image-picker';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');
const PHOTO_SIZE = (width - 60) / 3;

interface PhotoGalleryScreenProps {
  navigation: any;
}

interface Photo {
  id: string;
  uri: string;
  isMain: boolean;
  isVerified: boolean;
  uploadDate: string;
  likes: number;
  views: number;
}

const PhotoGalleryScreen: React.FC<PhotoGalleryScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { user, uploadPhoto, deletePhoto, setMainPhoto, getPhotos } = useAuth();

  const [photos, setPhotos] = useState<Photo[]>([]);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [showFullScreen, setShowFullScreen] = useState(false);

  useEffect(() => {
    loadPhotos();
  }, []);

  const loadPhotos = async () => {
    setIsLoading(true);
    try {
      const result = await getPhotos();
      if (result.success) {
        setPhotos(result.photos || []);
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось загрузить фотографии');
    } finally {
      setIsLoading(false);
    }
  };

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Разрешение на использование камеры',
            message: 'Приложению нужен доступ к камере для создания фотографий',
            buttonNeutral: 'Спросить позже',
            buttonNegative: 'Отмена',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  const showImagePicker = () => {
    if (photos.length >= 9) {
      Alert.alert('Ограничение', 'Максимальное количество фотографий: 9');
      return;
    }

    Alert.alert(
      'Добавить фото',
      'Выберите источник фотографии',
      [
        { text: 'Отмена', style: 'cancel' },
        { text: 'Галерея', onPress: openImageLibrary },
        { text: 'Камера', onPress: openCamera },
      ]
    );
  };

  const openImageLibrary = () => {
    const options = {
      mediaType: 'photo' as const,
      quality: 0.8,
      maxWidth: 1024,
      maxHeight: 1024,
    };

    launchImageLibrary(options, handleImageResponse);
  };

  const openCamera = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert('Ошибка', 'Нет разрешения на использование камеры');
      return;
    }

    const options = {
      mediaType: 'photo' as const,
      quality: 0.8,
      maxWidth: 1024,
      maxHeight: 1024,
    };

    launchCamera(options, handleImageResponse);
  };

  const handleImageResponse = async (response: ImagePickerResponse) => {
    if (response.didCancel || response.errorMessage) {
      return;
    }

    if (response.assets && response.assets[0]) {
      setIsUploading(true);
      try {
        const asset = response.assets[0];
        const result = await uploadPhoto({
          uri: asset.uri || '',
          type: asset.type,
          fileName: asset.fileName,
        });

        if (result.success) {
          await loadPhotos(); // Перезагружаем список фотографий
          Alert.alert('Успешно', 'Фотография загружена');
        } else {
          Alert.alert('Ошибка', result.message || 'Не удалось загрузить фотографию');
        }
      } catch (error) {
        Alert.alert('Ошибка', 'Произошла ошибка при загрузке фотографии');
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handlePhotoPress = (photo: Photo) => {
    setSelectedPhoto(photo);
    setShowFullScreen(true);
  };

  const handleSetMainPhoto = async (photo: Photo) => {
    try {
      const result = await setMainPhoto(photo.id);
      if (result.success) {
        await loadPhotos();
        Alert.alert('Успешно', 'Главная фотография изменена');
        setShowFullScreen(false);
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось изменить главную фотографию');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при изменении главной фотографии');
    }
  };

  const handleDeletePhoto = async (photo: Photo) => {
    if (photo.isMain) {
      Alert.alert('Ошибка', 'Нельзя удалить главную фотографию. Сначала выберите другую главную фотографию.');
      return;
    }

    Alert.alert(
      'Удалить фотографию',
      'Вы уверены, что хотите удалить эту фотографию?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await deletePhoto(photo.id);
              if (result.success) {
                await loadPhotos();
                Alert.alert('Успешно', 'Фотография удалена');
                setShowFullScreen(false);
              } else {
                Alert.alert('Ошибка', result.message || 'Не удалось удалить фотографию');
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Произошла ошибка при удалении фотографии');
            }
          },
        },
      ]
    );
  };

  const renderPhoto = ({ item, index }: { item: Photo; index: number }) => (
    <TouchableOpacity
      style={styles.photoContainer}
      onPress={() => handlePhotoPress(item)}
    >
      <Image source={{ uri: item.uri }} style={styles.photo} />
      
      {/* Главная фотография */}
      {item.isMain && (
        <View style={styles.mainPhotoBadge}>
          <Icon name="star" size={16} color="#FFFFFF" />
          <Text style={styles.mainPhotoText}>Главная</Text>
        </View>
      )}

      {/* Верификация */}
      {item.isVerified && (
        <View style={styles.verifiedBadge}>
          <Icon name="verified" size={16} color="#4CAF50" />
        </View>
      )}

      {/* Статистика */}
      <View style={styles.photoStats}>
        <View style={styles.statItem}>
          <Icon name="favorite" size={12} color="#FFFFFF" />
          <Text style={styles.statText}>{item.likes}</Text>
        </View>
        <View style={styles.statItem}>
          <Icon name="visibility" size={12} color="#FFFFFF" />
          <Text style={styles.statText}>{item.views}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderAddPhotoButton = () => (
    <TouchableOpacity
      style={styles.addPhotoContainer}
      onPress={showImagePicker}
      disabled={isUploading}
    >
      {isUploading ? (
        <ActivityIndicator color="#FF6B9D" size="large" />
      ) : (
        <>
          <Icon name="add-a-photo" size={40} color="#FF6B9D" />
          <Text style={styles.addPhotoText}>Добавить фото</Text>
        </>
      )}
    </TouchableOpacity>
  );

  const renderFullScreenModal = () => (
    <Modal
      visible={showFullScreen}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowFullScreen(false)}
    >
      <View style={styles.modalContainer}>
        <TouchableOpacity
          style={styles.modalOverlay}
          onPress={() => setShowFullScreen(false)}
        />
        
        {selectedPhoto && (
          <View style={styles.modalContent}>
            <Image source={{ uri: selectedPhoto.uri }} style={styles.fullScreenPhoto} />
            
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowFullScreen(false)}
              >
                <Icon name="close" size={24} color="#FFFFFF" />
              </TouchableOpacity>

              {!selectedPhoto.isMain && (
                <TouchableOpacity
                  style={[styles.modalButton, styles.primaryButton]}
                  onPress={() => handleSetMainPhoto(selectedPhoto)}
                >
                  <Icon name="star" size={24} color="#FFFFFF" />
                  <Text style={styles.modalButtonText}>Сделать главной</Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={[styles.modalButton, styles.dangerButton]}
                onPress={() => handleDeletePhoto(selectedPhoto)}
              >
                <Icon name="delete" size={24} color="#FFFFFF" />
                <Text style={styles.modalButtonText}>Удалить</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.photoInfo}>
              <Text style={styles.photoInfoText}>
                Загружено: {new Date(selectedPhoto.uploadDate).toLocaleDateString('ru-RU')}
              </Text>
              <Text style={styles.photoInfoText}>
                Лайки: {selectedPhoto.likes} • Просмотры: {selectedPhoto.views}
              </Text>
              {selectedPhoto.isVerified && (
                <Text style={styles.verifiedText}>
                  ✓ Фотография прошла модерацию
                </Text>
              )}
            </View>
          </View>
        )}
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Мои фотографии</Text>
          <View style={styles.headerRight}>
            <Text style={styles.photoCount}>{photos.length}/9</Text>
          </View>
        </View>

        {/* Контент */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator color="#FFFFFF" size="large" />
            <Text style={styles.loadingText}>Загрузка фотографий...</Text>
          </View>
        ) : (
          <FlatList
            data={[...photos, { id: 'add-button' }]}
            renderItem={({ item, index }) => {
              if (item.id === 'add-button') {
                return renderAddPhotoButton();
              }
              return renderPhoto({ item: item as Photo, index });
            }}
            numColumns={3}
            contentContainerStyle={styles.photosContainer}
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={
              <View style={styles.tipsContainer}>
                <Text style={styles.tipsTitle}>Советы для лучших фото:</Text>
                <Text style={styles.tipText}>• Добавьте минимум 3 фотографии</Text>
                <Text style={styles.tipText}>• Используйте хорошее освещение</Text>
                <Text style={styles.tipText}>• Покажите свои увлечения</Text>
                <Text style={styles.tipText}>• Избегайте групповых фото</Text>
              </View>
            }
          />
        )}

        {renderFullScreenModal()}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  photoCount: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginTop: 10,
  },
  photosContainer: {
    padding: 20,
  },
  tipsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 20,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 10,
  },
  tipText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 4,
  },
  photoContainer: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    margin: 5,
    borderRadius: 15,
    overflow: 'hidden',
    position: 'relative',
  },
  photo: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  mainPhotoBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#FF6B9D',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  mainPhotoText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  verifiedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(76, 175, 80, 0.9)',
    borderRadius: 12,
    padding: 4,
  },
  photoStats: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  statText: {
    color: '#FFFFFF',
    fontSize: 10,
    marginLeft: 2,
  },
  addPhotoContainer: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    margin: 5,
    borderRadius: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addPhotoText: {
    color: '#FF6B9D',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    width: width * 0.9,
    maxHeight: height * 0.8,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    overflow: 'hidden',
  },
  fullScreenPhoto: {
    width: '100%',
    height: width * 0.9,
    resizeMode: 'cover',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 20,
    backgroundColor: '#FF6B9D',
  },
  modalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  primaryButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.8)',
  },
  dangerButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.8)',
  },
  modalButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  photoInfo: {
    padding: 20,
    backgroundColor: '#F5F5F5',
  },
  photoInfoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  verifiedText: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '600',
    marginTop: 8,
  },
});

export default PhotoGalleryScreen;
