import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  Dimensions,
  Share,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { placesService } from '../../services/placesService';
import { reviewsService } from '../../services/reviewsService';
import { useAuth } from '../../hooks/useAuth';
import { Place, Review, PlaceHours } from '../../types/places.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface PlaceDetailScreenProps {}

const PlaceDetailScreen: React.FC<PlaceDetailScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const { placeId } = route.params as { placeId: string };
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showAllReviews, setShowAllReviews] = useState(false);

  // Загрузка деталей места
  const {
    data: place,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['place', placeId],
    queryFn: () => placesService.getPlaceDetails(placeId),
  });

  // Загрузка отзывов
  const {
    data: reviews,
    isLoading: reviewsLoading,
  } = useQuery({
    queryKey: ['reviews', placeId],
    queryFn: () => reviewsService.getPlaceReviews(placeId),
    enabled: !!place,
  });

  // Мутация для добавления в избранное
  const toggleFavoriteMutation = useMutation({
    mutationFn: () => placesService.toggleFavorite(placeId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['place', placeId] });
      queryClient.invalidateQueries({ queryKey: ['places'] });
    },
  });

  const handleToggleFavorite = () => {
    toggleFavoriteMutation.mutate();
  };

  const handleShare = async () => {
    if (!place) return;

    try {
      await Share.share({
        message: `Посмотрите это место: ${place.name}\n\n${place.address}\nРейтинг: ${place.rating}/5\n\nПодробности в приложении Likes & Love`,
        title: place.name,
      });
    } catch (error) {
      console.error('Error sharing place:', error);
    }
  };

  const handleCall = () => {
    if (place?.phone) {
      Linking.openURL(`tel:${place.phone}`);
    }
  };

  const handleDirections = () => {
    if (place?.coordinates) {
      const url = `https://maps.google.com/?q=${place.coordinates.latitude},${place.coordinates.longitude}`;
      Linking.openURL(url);
    }
  };

  const handleWebsite = () => {
    if (place?.website) {
      Linking.openURL(place.website);
    }
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}м`;
    }
    return `${distance.toFixed(1)}км`;
  };

  const formatPrice = (priceRange: number) => {
    return '₽'.repeat(priceRange);
  };

  const formatHours = (hours: PlaceHours) => {
    const today = new Date().getDay();
    const todayHours = hours.schedule[today];
    
    if (!todayHours || todayHours.closed) {
      return 'Закрыто';
    }
    
    return `${todayHours.open} - ${todayHours.close}`;
  };

  const isOpenNow = (hours: PlaceHours) => {
    const now = new Date();
    const today = now.getDay();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const todayHours = hours.schedule[today];
    if (!todayHours || todayHours.closed) return false;
    
    const openTime = parseInt(todayHours.open.split(':')[0]) * 60 + parseInt(todayHours.open.split(':')[1]);
    const closeTime = parseInt(todayHours.close.split(':')[0]) * 60 + parseInt(todayHours.close.split(':')[1]);
    
    return currentTime >= openTime && currentTime <= closeTime;
  };

  const renderReview = (review: Review, index: number) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateY: interpolate(animatedValue.value, [0, 1], [20, 0]),
          },
        ],
      };
    });

    return (
      <Animated.View key={review.id} style={[styles.reviewCard, animatedStyle]}>
        <View style={styles.reviewHeader}>
          <Image source={{ uri: review.user.avatar }} style={styles.reviewAvatar} />
          <View style={styles.reviewUserInfo}>
            <Text style={styles.reviewUserName}>{review.user.name}</Text>
            <View style={styles.reviewRating}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Ionicons
                  key={star}
                  name={star <= review.rating ? 'star' : 'star-outline'}
                  size={14}
                  color={colors.warning}
                />
              ))}
            </View>
          </View>
          <Text style={styles.reviewDate}>
            {new Date(review.createdAt).toLocaleDateString('ru-RU')}
          </Text>
        </View>
        <Text style={styles.reviewText}>{review.text}</Text>
        {review.photos && review.photos.length > 0 && (
          <ScrollView horizontal style={styles.reviewPhotos}>
            {review.photos.map((photo, idx) => (
              <Image key={idx} source={{ uri: photo }} style={styles.reviewPhoto} />
            ))}
          </ScrollView>
        )}
      </Animated.View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Загружаем информацию о месте...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !place) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить информацию о месте
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Детали места</Text>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleShare}
          >
            <Ionicons name="share-outline" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleToggleFavorite}
            disabled={toggleFavoriteMutation.isPending}
          >
            <Ionicons
              name={place.isFavorite ? 'heart' : 'heart-outline'}
              size={24}
              color={place.isFavorite ? colors.error : colors.text}
            />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Галерея изображений */}
        <View style={styles.imageGallery}>
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={(event) => {
              const index = Math.round(event.nativeEvent.contentOffset.x / screenWidth);
              setSelectedImageIndex(index);
            }}
          >
            {place.photos.map((photo, index) => (
              <Image key={index} source={{ uri: photo }} style={styles.placeImage} />
            ))}
          </ScrollView>
          
          {/* Индикаторы изображений */}
          <View style={styles.imageIndicators}>
            {place.photos.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.imageIndicator,
                  selectedImageIndex === index && styles.imageIndicatorActive,
                ]}
              />
            ))}
          </View>
        </View>

        {/* Основная информация */}
        <View style={styles.mainInfo}>
          <View style={styles.titleSection}>
            <Text style={styles.placeName}>{place.name}</Text>
            <View style={styles.ratingSection}>
              <View style={styles.rating}>
                <Ionicons name="star" size={20} color={colors.warning} />
                <Text style={styles.ratingText}>{place.rating}</Text>
              </View>
              <Text style={styles.reviewsCount}>({place.reviewsCount} отзывов)</Text>
            </View>
          </View>
          
          <Text style={styles.placeCategory}>{place.category}</Text>
          <Text style={styles.placeAddress}>{place.address}</Text>
          
          <View style={styles.placeDetails}>
            <View style={styles.detailItem}>
              <Ionicons name="location-outline" size={16} color={colors.textSecondary} />
              <Text style={styles.detailText}>{formatDistance(place.distance)}</Text>
            </View>
            
            {place.priceRange && (
              <View style={styles.detailItem}>
                <Ionicons name="card-outline" size={16} color={colors.textSecondary} />
                <Text style={styles.detailText}>{formatPrice(place.priceRange)}</Text>
              </View>
            )}
            
            {place.hours && (
              <View style={styles.detailItem}>
                <Ionicons
                  name="time-outline"
                  size={16}
                  color={isOpenNow(place.hours) ? colors.success : colors.error}
                />
                <Text
                  style={[
                    styles.detailText,
                    { color: isOpenNow(place.hours) ? colors.success : colors.error },
                  ]}
                >
                  {formatHours(place.hours)}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Описание */}
        {place.description && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Описание</Text>
            <Text style={styles.descriptionText}>{place.description}</Text>
          </View>
        )}

        {/* Особенности */}
        {place.features && place.features.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Особенности</Text>
            <View style={styles.featuresContainer}>
              {place.features.map((feature, index) => (
                <View key={index} style={styles.featureTag}>
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Карта */}
        {place.coordinates && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Расположение</Text>
            <View style={styles.mapContainer}>
              <MapView
                provider={PROVIDER_GOOGLE}
                style={styles.map}
                region={{
                  latitude: place.coordinates.latitude,
                  longitude: place.coordinates.longitude,
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.01,
                }}
                scrollEnabled={false}
                zoomEnabled={false}
              >
                <Marker
                  coordinate={{
                    latitude: place.coordinates.latitude,
                    longitude: place.coordinates.longitude,
                  }}
                  title={place.name}
                />
              </MapView>
              <TouchableOpacity
                style={styles.mapOverlay}
                onPress={handleDirections}
              >
                <Text style={styles.mapOverlayText}>Построить маршрут</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Отзывы */}
        <View style={styles.section}>
          <View style={styles.reviewsHeader}>
            <Text style={styles.sectionTitle}>
              Отзывы ({reviews?.length || 0})
            </Text>
            <TouchableOpacity
              style={styles.writeReviewButton}
              onPress={() => navigation.navigate('WriteReview', { placeId })}
            >
              <Text style={styles.writeReviewText}>Написать отзыв</Text>
            </TouchableOpacity>
          </View>
          
          {reviewsLoading ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : reviews && reviews.length > 0 ? (
            <View style={styles.reviewsList}>
              {(showAllReviews ? reviews : reviews.slice(0, 3)).map(renderReview)}
              
              {reviews.length > 3 && (
                <TouchableOpacity
                  style={styles.showMoreReviews}
                  onPress={() => setShowAllReviews(!showAllReviews)}
                >
                  <Text style={styles.showMoreText}>
                    {showAllReviews ? 'Скрыть' : `Показать все ${reviews.length} отзывов`}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <Text style={styles.noReviewsText}>Пока нет отзывов</Text>
          )}
        </View>
      </ScrollView>

      {/* Нижние действия */}
      <View style={styles.bottomActions}>
        {place.phone && (
          <TouchableOpacity
            style={[styles.actionButton, styles.callButton]}
            onPress={handleCall}
          >
            <Ionicons name="call" size={20} color={colors.white} />
            <Text style={styles.actionButtonText}>Позвонить</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, styles.directionsButton]}
          onPress={handleDirections}
        >
          <Ionicons name="navigate" size={20} color={colors.white} />
          <Text style={styles.actionButtonText}>Маршрут</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.meetingButton]}
          onPress={() => navigation.navigate('CreateMeeting', { placeId })}
        >
          <Ionicons name="calendar" size={20} color={colors.white} />
          <Text style={styles.actionButtonText}>Встреча</Text>
        </TouchableOpacity>
        
        {place.website && (
          <TouchableOpacity
            style={[styles.actionButton, styles.websiteButton]}
            onPress={handleWebsite}
          >
            <Ionicons name="globe" size={20} color={colors.white} />
            <Text style={styles.actionButtonText}>Сайт</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Индикатор загрузки */}
      {toggleFavoriteMutation.isPending && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  imageGallery: {
    position: 'relative',
  },
  placeImage: {
    width: screenWidth,
    height: 250,
    resizeMode: 'cover',
  },
  imageIndicators: {
    position: 'absolute',
    bottom: spacing.md,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.xs,
  },
  imageIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.5)',
  },
  imageIndicatorActive: {
    backgroundColor: colors.white,
  },
  mainInfo: {
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  titleSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  placeName: {
    flex: 1,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginRight: spacing.md,
  },
  ratingSection: {
    alignItems: 'flex-end',
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginBottom: spacing.xs,
  },
  ratingText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  reviewsCount: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  placeCategory: {
    fontSize: typography.sizes.md,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    marginBottom: spacing.xs,
  },
  placeAddress: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  placeDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  detailText: {
    fontSize: typography.sizes.sm,
    color: colors.text,
  },
  section: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  descriptionText: {
    fontSize: typography.sizes.md,
    color: colors.text,
    lineHeight: 22,
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  featureTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: 16,
  },
  featureText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  mapContainer: {
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  mapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapOverlayText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  reviewsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  writeReviewButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 12,
  },
  writeReviewText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  reviewsList: {
    gap: spacing.md,
  },
  reviewCard: {
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  reviewAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: spacing.md,
  },
  reviewUserInfo: {
    flex: 1,
  },
  reviewUserName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  reviewRating: {
    flexDirection: 'row',
    gap: spacing.xs,
  },
  reviewDate: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  reviewText: {
    fontSize: typography.sizes.md,
    color: colors.text,
    lineHeight: 20,
    marginBottom: spacing.sm,
  },
  reviewPhotos: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  reviewPhoto: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: spacing.sm,
  },
  showMoreReviews: {
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  showMoreText: {
    fontSize: typography.sizes.md,
    color: colors.primary,
    fontWeight: typography.weights.medium,
  },
  noReviewsText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    borderRadius: 12,
    gap: spacing.xs,
  },
  callButton: {
    backgroundColor: colors.success,
  },
  directionsButton: {
    backgroundColor: colors.primary,
  },
  meetingButton: {
    backgroundColor: colors.secondary,
  },
  websiteButton: {
    backgroundColor: colors.textSecondary,
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PlaceDetailScreen;
