import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Animated,
  ScrollView,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LottieView from 'lottie-react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface CompleteScreenProps {
  navigation: any;
  route: any;
}

const CompleteScreen: React.FC<CompleteScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { completeOnboarding } = useAuth();
  const { basicInfo, interests, photos, preferences } = route.params || {};

  // Анимации
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const confettiAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Последовательная анимация
    Animated.sequence([
      // Прогресс до 100%
      Animated.timing(progressAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: false,
      }),
      // Появление контента
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      // Конфетти
      Animated.timing(confettiAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
    ]).start();

    // Автоматическое завершение онбординга
    const timer = setTimeout(() => {
      handleComplete();
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  const handleComplete = async () => {
    try {
      await completeOnboarding({
        basicInfo,
        interests,
        photos,
        preferences,
      });

      // Переход к главному экрану приложения
      navigation.reset({
        index: 0,
        routes: [{ name: 'MainApp' }],
      });
    } catch (error) {
      console.error('Error completing onboarding:', error);
      // В случае ошибки все равно переходим к главному экрану
      navigation.reset({
        index: 0,
        routes: [{ name: 'MainApp' }],
      });
    }
  };

  const getProfileCompleteness = () => {
    let completeness = 0;
    
    if (basicInfo?.firstName && basicInfo?.lastName) completeness += 20;
    if (basicInfo?.birthDate && basicInfo?.gender && basicInfo?.city) completeness += 20;
    if (interests && interests.length >= 3) completeness += 20;
    if (photos && photos.length > 0) completeness += 20;
    if (preferences?.relationshipGoals && preferences.relationshipGoals.length > 0) completeness += 20;

    return completeness;
  };

  const profileStats = [
    {
      icon: 'person',
      label: 'Профиль',
      value: basicInfo ? 'Заполнен' : 'Не заполнен',
      completed: !!basicInfo,
    },
    {
      icon: 'favorite',
      label: 'Интересы',
      value: interests ? `${interests.length} выбрано` : '0 выбрано',
      completed: interests && interests.length >= 3,
    },
    {
      icon: 'photo-camera',
      label: 'Фотографии',
      value: photos ? `${photos.length} загружено` : '0 загружено',
      completed: photos && photos.length > 0,
    },
    {
      icon: 'tune',
      label: 'Предпочтения',
      value: preferences ? 'Настроены' : 'Не настроены',
      completed: !!preferences,
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Прогресс бар */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>Завершено!</Text>
        </View>

        {/* Конфетти анимация */}
        <Animated.View
          style={[
            styles.confettiContainer,
            {
              opacity: confettiAnim,
            },
          ]}
        >
          {/* Здесь можно добавить Lottie анимацию конфетти */}
        </Animated.View>

        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Главная иконка */}
            <Animated.View
              style={[
                styles.successContainer,
                {
                  transform: [{ scale: scaleAnim }],
                },
              ]}
            >
              <View style={styles.successCircle}>
                <Icon name="check" size={80} color="#FFFFFF" />
              </View>
            </Animated.View>

            {/* Заголовок */}
            <Text style={styles.title}>Поздравляем!</Text>
            <Text style={styles.subtitle}>
              Ваш профиль готов к знакомствам
            </Text>

            {/* Статистика профиля */}
            <View style={styles.statsContainer}>
              <Text style={styles.statsTitle}>
                Заполненность профиля: {getProfileCompleteness()}%
              </Text>
              
              {profileStats.map((stat, index) => (
                <View key={index} style={styles.statItem}>
                  <View style={styles.statIcon}>
                    <Icon
                      name={stat.icon}
                      size={24}
                      color={stat.completed ? '#4CAF50' : 'rgba(255, 255, 255, 0.6)'}
                    />
                  </View>
                  <View style={styles.statContent}>
                    <Text style={styles.statLabel}>{stat.label}</Text>
                    <Text style={styles.statValue}>{stat.value}</Text>
                  </View>
                  <View style={styles.statStatus}>
                    <Icon
                      name={stat.completed ? 'check-circle' : 'radio-button-unchecked'}
                      size={20}
                      color={stat.completed ? '#4CAF50' : 'rgba(255, 255, 255, 0.4)'}
                    />
                  </View>
                </View>
              ))}
            </View>

            {/* Советы */}
            <View style={styles.tipsContainer}>
              <Text style={styles.tipsTitle}>Советы для успешных знакомств:</Text>
              
              <View style={styles.tip}>
                <Icon name="photo-camera" size={16} color="#FFFFFF" />
                <Text style={styles.tipText}>
                  Добавьте больше фотографий для увеличения интереса
                </Text>
              </View>

              <View style={styles.tip}>
                <Icon name="chat" size={16} color="#FFFFFF" />
                <Text style={styles.tipText}>
                  Будьте активны в общении и отвечайте на сообщения
                </Text>
              </View>

              <View style={styles.tip}>
                <Icon name="verified-user" size={16} color="#FFFFFF" />
                <Text style={styles.tipText}>
                  Пройдите верификацию для повышения доверия
                </Text>
              </View>

              <View style={styles.tip}>
                <Icon name="favorite" size={16} color="#FFFFFF" />
                <Text style={styles.tipText}>
                  Ставьте лайки и используйте супер-лайки
                </Text>
              </View>
            </View>

            {/* Информация о безопасности */}
            <View style={styles.safetyContainer}>
              <Icon name="security" size={24} color="#FFFFFF" />
              <Text style={styles.safetyTitle}>Ваша безопасность важна</Text>
              <Text style={styles.safetyText}>
                Мы проверяем все профили и следим за безопасностью общения. 
                Сообщайте о подозрительной активности.
              </Text>
            </View>
          </Animated.View>
        </ScrollView>

        {/* Кнопки */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.startButton}
            onPress={handleComplete}
          >
            <LinearGradient
              colors={['#FFFFFF', '#F0F0F0']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.startButtonText}>Начать знакомства</Text>
              <Icon name="favorite" size={24} color="#FF6B9D" />
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.skipButton}
            onPress={handleComplete}
          >
            <Text style={styles.skipButtonText}>Пропустить (5 сек)</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  progressContainer: {
    paddingHorizontal: 30,
    paddingTop: 60,
    paddingBottom: 20,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
  progressText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  confettiContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
    pointerEvents: 'none',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 140,
  },
  content: {
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  successContainer: {
    marginBottom: 30,
  },
  successCircle: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 24,
  },
  statsContainer: {
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 30,
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  statIcon: {
    width: 40,
    alignItems: 'center',
  },
  statContent: {
    flex: 1,
    marginLeft: 15,
  },
  statLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statValue: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  statStatus: {
    width: 30,
    alignItems: 'center',
  },
  tipsContainer: {
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 30,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 15,
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  safetyContainer: {
    width: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
  },
  safetyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 10,
    marginBottom: 10,
  },
  safetyText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 20,
  },
  buttonsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 30,
    paddingBottom: 40,
    paddingTop: 20,
    backgroundColor: 'rgba(255, 107, 157, 0.1)',
  },
  startButton: {
    marginBottom: 15,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 30,
    borderRadius: 15,
  },
  startButtonText: {
    color: '#FF6B9D',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 10,
  },
  skipButton: {
    alignItems: 'center',
    paddingVertical: 15,
  },
  skipButtonText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
});

export default CompleteScreen;
