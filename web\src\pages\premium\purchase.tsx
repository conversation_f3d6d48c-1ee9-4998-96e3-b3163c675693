import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Divider,
  Switch,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormControl,
  FormLabel,
  LinearProgress,
} from '@mui/material';
import {
  Star,
  CheckCircle,
  Visibility,
  Favorite,
  Message,
  Security,
  Speed,
  Diamond,
  CreditCard,
  AccountBalance,
  Phone,
  Close,
  Lock,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { premiumService } from '../../services/premiumService';
import { PremiumPlan, PaymentMethod } from '../../types/premium.types';
import { formatCurrency } from '../../utils/formatUtils';
import styles from './purchase.module.css';

interface PremiumPurchaseProps {}

const PremiumPurchase: React.FC<PremiumPurchaseProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [selectedPlan, setSelectedPlan] = useState<PremiumPlan | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [isAnnual, setIsAnnual] = useState(false);
  const [purchaseDialogOpen, setPurchaseDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Загрузка планов Premium
  const {
    data: premiumPlans,
    isLoading: plansLoading,
    error: plansError,
  } = useQuery({
    queryKey: ['premium', 'plans'],
    queryFn: () => premiumService.getPremiumPlans(),
  });

  // Загрузка способов оплаты
  const {
    data: paymentMethods,
    isLoading: paymentMethodsLoading,
  } = useQuery({
    queryKey: ['payment', 'methods'],
    queryFn: () => premiumService.getPaymentMethods(),
  });

  // Мутация для покупки Premium
  const purchasePremiumMutation = useMutation({
    mutationFn: (data: { planId: string; paymentMethodId: string; isAnnual: boolean }) =>
      premiumService.purchasePremium(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['user', 'profile'] });
      setPurchaseDialogOpen(false);
      
      if (data.redirectUrl) {
        window.location.href = data.redirectUrl;
      } else {
        router.push('/premium/success');
      }
    },
    onError: (error: any) => {
      console.error('Purchase error:', error);
    },
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/premium/purchase');
    }
  }, [isAuthenticated, router]);

  const handleSelectPlan = (plan: PremiumPlan) => {
    setSelectedPlan(plan);
    setPurchaseDialogOpen(true);
  };

  const handlePurchase = async () => {
    if (!selectedPlan || !selectedPaymentMethod) return;

    setIsProcessing(true);
    try {
      await purchasePremiumMutation.mutateAsync({
        planId: selectedPlan.id,
        paymentMethodId: selectedPaymentMethod,
        isAnnual,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const getDiscountedPrice = (plan: PremiumPlan) => {
    if (isAnnual && plan.annualDiscount) {
      return plan.monthlyPrice * 12 * (1 - plan.annualDiscount / 100);
    }
    return isAnnual ? plan.monthlyPrice * 12 : plan.monthlyPrice;
  };

  const getSavingsAmount = (plan: PremiumPlan) => {
    if (isAnnual && plan.annualDiscount) {
      return plan.monthlyPrice * 12 * (plan.annualDiscount / 100);
    }
    return 0;
  };

  const premiumFeatures = [
    {
      icon: <Visibility color="primary" />,
      title: 'Безлимитные лайки',
      description: 'Ставьте лайки без ограничений',
    },
    {
      icon: <Star color="warning" />,
      title: 'Суперлайки',
      description: 'Выделяйтесь среди других пользователей',
    },
    {
      icon: <Message color="info" />,
      title: 'Приоритетные сообщения',
      description: 'Ваши сообщения показываются первыми',
    },
    {
      icon: <Security color="success" />,
      title: 'Скрытый режим',
      description: 'Просматривайте профили незаметно',
    },
    {
      icon: <Speed color="error" />,
      title: 'Ускоренные матчи',
      description: 'Находите совпадения быстрее',
    },
    {
      icon: <Diamond color="warning" />,
      title: 'Бонусные алмазы',
      description: 'Получайте алмазы каждый месяц',
    },
  ];

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Premium подписка - Likes & Love</title>
        <meta name="description" content="Получите Premium подписку и откройте все возможности приложения знакомств." />
        <meta name="keywords" content="premium, подписка, знакомства, функции" />
        <meta property="og:title" content="Premium подписка - Likes & Love" />
        <meta property="og:description" content="Откройте все возможности приложения" />
        <meta property="og:type" content="website" />
      </Head>

      <Container maxWidth="lg" className={styles.container}>
        {/* Заголовок */}
        <Box className={styles.header}>
          <Typography variant="h2" component="h1" className={styles.title}>
            Получите Premium
          </Typography>
          <Typography variant="h5" color="text.secondary" className={styles.subtitle}>
            Откройте все возможности для поиска идеального партнера
          </Typography>
        </Box>

        {/* Переключатель периода */}
        <Box className={styles.periodToggle}>
          <FormControlLabel
            control={
              <Switch
                checked={isAnnual}
                onChange={(e) => setIsAnnual(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Box className={styles.periodLabel}>
                <Typography variant="body1">
                  Годовая подписка
                </Typography>
                <Chip
                  label="Скидка до 30%"
                  color="success"
                  size="small"
                  className={styles.discountChip}
                />
              </Box>
            }
          />
        </Box>

        {/* Преимущества Premium */}
        <Card className={styles.featuresCard}>
          <CardContent>
            <Typography variant="h4" className={styles.featuresTitle}>
              Что вы получите с Premium
            </Typography>
            
            <Grid container spacing={3} className={styles.featuresGrid}>
              {premiumFeatures.map((feature, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Box className={styles.featureItem}>
                    <Box className={styles.featureIcon}>
                      {feature.icon}
                    </Box>
                    <Typography variant="h6" className={styles.featureTitle}>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>

        {/* Планы подписки */}
        {plansLoading ? (
          <Box className={styles.loadingContainer}>
            <LinearProgress />
            <Typography>Загрузка планов...</Typography>
          </Box>
        ) : plansError ? (
          <Alert severity="error" className={styles.errorAlert}>
            Ошибка загрузки планов подписки
          </Alert>
        ) : (
          <Grid container spacing={3} className={styles.plansGrid}>
            {premiumPlans?.map((plan) => (
              <Grid item xs={12} md={4} key={plan.id}>
                <Card 
                  className={`${styles.planCard} ${plan.isPopular ? styles.popularPlan : ''}`}
                >
                  {plan.isPopular && (
                    <Chip
                      label="Популярный"
                      color="primary"
                      className={styles.popularBadge}
                    />
                  )}
                  
                  <CardContent className={styles.planContent}>
                    <Typography variant="h4" className={styles.planName}>
                      {plan.name}
                    </Typography>
                    
                    <Box className={styles.planPrice}>
                      <Typography variant="h3" className={styles.price}>
                        {formatCurrency(getDiscountedPrice(plan))}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {isAnnual ? 'в год' : 'в месяц'}
                      </Typography>
                    </Box>

                    {isAnnual && getSavingsAmount(plan) > 0 && (
                      <Typography variant="body2" className={styles.savings}>
                        Экономия: {formatCurrency(getSavingsAmount(plan))}
                      </Typography>
                    )}

                    <Divider sx={{ my: 2 }} />

                    <List className={styles.planFeatures}>
                      {plan.features.map((feature, index) => (
                        <ListItem key={index} className={styles.planFeature}>
                          <ListItemIcon>
                            <CheckCircle color="success" />
                          </ListItemIcon>
                          <ListItemText primary={feature} />
                        </ListItem>
                      ))}
                    </List>

                    <Button
                      variant={plan.isPopular ? 'contained' : 'outlined'}
                      size="large"
                      fullWidth
                      onClick={() => handleSelectPlan(plan)}
                      className={styles.selectButton}
                      startIcon={<Star />}
                    >
                      Выбрать план
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Гарантии */}
        <Card className={styles.guaranteeCard}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={8}>
                <Typography variant="h5" className={styles.guaranteeTitle}>
                  Гарантия возврата средств
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Если вы не найдете совпадения в течение первых 30 дней, 
                  мы вернем вам деньги без вопросов.
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box className={styles.guaranteeIcon}>
                  <Security sx={{ fontSize: 64, color: 'success.main' }} />
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Диалог покупки */}
        <Dialog
          open={purchaseDialogOpen}
          onClose={() => setPurchaseDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              Оформление подписки
              <Button onClick={() => setPurchaseDialogOpen(false)}>
                <Close />
              </Button>
            </Box>
          </DialogTitle>
          
          <DialogContent>
            {selectedPlan && (
              <Box className={styles.purchaseContent}>
                {/* Выбранный план */}
                <Box className={styles.selectedPlan}>
                  <Typography variant="h6">{selectedPlan.name}</Typography>
                  <Typography variant="h4" color="primary">
                    {formatCurrency(getDiscountedPrice(selectedPlan))}
                    <Typography component="span" variant="body2" color="text.secondary">
                      {isAnnual ? '/год' : '/месяц'}
                    </Typography>
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* Способы оплаты */}
                <FormControl component="fieldset" fullWidth>
                  <FormLabel component="legend">Способ оплаты</FormLabel>
                  <RadioGroup
                    value={selectedPaymentMethod}
                    onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                  >
                    {paymentMethods?.map((method) => (
                      <FormControlLabel
                        key={method.id}
                        value={method.id}
                        control={<Radio />}
                        label={
                          <Box className={styles.paymentMethod}>
                            {method.type === 'card' && <CreditCard />}
                            {method.type === 'bank' && <AccountBalance />}
                            {method.type === 'mobile' && <Phone />}
                            <Typography variant="body1">{method.name}</Typography>
                          </Box>
                        }
                      />
                    ))}
                  </RadioGroup>
                </FormControl>

                {/* Безопасность */}
                <Alert severity="info" icon={<Lock />} sx={{ mt: 2 }}>
                  Все платежи защищены SSL-шифрованием. Мы не храним данные ваших карт.
                </Alert>
              </Box>
            )}
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setPurchaseDialogOpen(false)}>
              Отмена
            </Button>
            <Button
              variant="contained"
              onClick={handlePurchase}
              disabled={!selectedPaymentMethod || isProcessing}
              startIcon={isProcessing ? <LinearProgress /> : <Star />}
            >
              {isProcessing ? 'Обработка...' : 'Оплатить'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </>
  );
};

export default PremiumPurchase;
