/* Стили для AdminLayout */

.root {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* AppBar */
.appBar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.toolbar {
  padding: 0 24px;
}

.title {
  flex-grow: 1;
  font-weight: 600;
  color: #ffffff;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.profileButton {
  margin-left: 8px;
}

.profileAvatar {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.profileAvatar:hover {
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.05);
}

.profileMenu {
  margin-top: 8px;
}

/* Drawer */
.drawer {
  height: 100%;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  border-right: 1px solid rgba(0, 0, 0, 0.08);
}

.drawerHeader {
  padding: 24px 20px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.drawerHeader::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(0deg); }
  50% { transform: translateX(0%) translateY(0%) rotate(180deg); }
}

.logo {
  font-weight: 700;
  font-size: 1.5rem;
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.adminLabel {
  opacity: 0.9;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Навигация */
.navigationList {
  padding: 16px 8px;
}

.navItem {
  margin: 4px 0;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.navItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.navItem:hover::before {
  left: 100%;
}

.navItem:hover {
  background: rgba(102, 126, 234, 0.08);
  transform: translateX(4px);
}

.navItemActive {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.navItemActive:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateX(0);
}

.navIcon {
  color: inherit;
  min-width: 40px;
}

.navText {
  font-weight: 500;
}

.navItemActive .navIcon,
.navItemActive .navText {
  color: white;
}

/* Информация о пользователе */
.userInfo {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 12px;
}

.userAvatar {
  width: 40px;
  height: 40px;
  border: 2px solid #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.userDetails {
  flex: 1;
  min-width: 0;
}

.userName {
  font-weight: 600;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userRole {
  color: #667eea;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Основной контент */
.content {
  flex-grow: 1;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
}

/* Адаптивность */
@media (max-width: 768px) {
  .toolbar {
    padding: 0 16px;
  }

  .title {
    font-size: 1.1rem;
  }

  .headerActions {
    gap: 4px;
  }

  .drawerHeader {
    padding: 20px 16px;
  }

  .logo {
    font-size: 1.3rem;
  }

  .navigationList {
    padding: 12px 4px;
  }

  .navItem {
    margin: 2px 0;
  }

  .userInfo {
    padding: 16px;
    gap: 8px;
  }

  .userAvatar {
    width: 36px;
    height: 36px;
  }

  .userName {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .drawerHeader {
    padding: 16px 12px;
  }

  .logo {
    font-size: 1.2rem;
  }

  .adminLabel {
    font-size: 0.7rem;
  }

  .userInfo {
    padding: 12px;
  }

  .userAvatar {
    width: 32px;
    height: 32px;
  }

  .userName {
    font-size: 0.8rem;
  }

  .userRole {
    font-size: 0.7rem;
  }
}

/* Анимации */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.navItem {
  animation: slideInLeft 0.3s ease-out;
}

.navItem:nth-child(1) { animation-delay: 0.1s; }
.navItem:nth-child(2) { animation-delay: 0.15s; }
.navItem:nth-child(3) { animation-delay: 0.2s; }
.navItem:nth-child(4) { animation-delay: 0.25s; }
.navItem:nth-child(5) { animation-delay: 0.3s; }
.navItem:nth-child(6) { animation-delay: 0.35s; }

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .root {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }

  .content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }

  .drawer {
    background: linear-gradient(180deg, #2c2c54 0%, #1a1a2e 100%);
    border-right-color: rgba(255, 255, 255, 0.1);
  }

  .navItem:hover {
    background: rgba(102, 126, 234, 0.15);
  }

  .userInfo {
    background: linear-gradient(135deg, #2c2c54 0%, #1a1a2e 100%);
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  .userName {
    color: #ffffff;
  }

  .userRole {
    color: #a8b3cf;
  }
}

/* Эффекты при наведении */
.navItem {
  position: relative;
  overflow: hidden;
}

.navItem::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.navItem:hover::after {
  width: 200px;
  height: 200px;
}

.navItemActive::after {
  display: none;
}
