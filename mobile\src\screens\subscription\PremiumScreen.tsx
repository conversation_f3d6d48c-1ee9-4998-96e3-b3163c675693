import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { subscriptionService } from '../../services/subscriptionService';
import { useAuth } from '../../hooks/useAuth';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface PremiumScreenProps {}

const PremiumScreen: React.FC<PremiumScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();

  const [currentFeatureIndex, setCurrentFeatureIndex] = useState(0);

  // Загрузка информации о Premium
  const {
    data: premiumInfo,
  } = useQuery({
    queryKey: ['subscription', 'premium'],
    queryFn: () => subscriptionService.getPremiumInfo(),
  });

  // Загрузка текущей подписки
  const {
    data: currentSubscription,
  } = useQuery({
    queryKey: ['subscription', 'current'],
    queryFn: () => subscriptionService.getCurrentSubscription(),
  });

  const features = [
    {
      id: 'unlimited_likes',
      title: 'Безлимитные лайки',
      description: 'Ставьте лайки без ограничений и находите больше совпадений каждый день',
      icon: 'heart',
      color: colors.error,
      image: require('../../assets/images/premium/unlimited-likes.png'),
    },
    {
      id: 'super_likes',
      title: 'Суперлайки',
      description: 'Выделитесь среди других пользователей и увеличьте шансы на взаимность',
      icon: 'star',
      color: colors.warning,
      image: require('../../assets/images/premium/super-likes.png'),
    },
    {
      id: 'who_liked_you',
      title: 'Кто вас лайкнул',
      description: 'Смотрите, кто поставил вам лайк, и отвечайте взаимностью мгновенно',
      icon: 'eye',
      color: colors.primary,
      image: require('../../assets/images/premium/who-liked.png'),
    },
    {
      id: 'boost',
      title: 'Буст профиля',
      description: 'Станьте топ-профилем в вашем регионе на 30 минут',
      icon: 'flash',
      color: colors.secondary,
      image: require('../../assets/images/premium/boost.png'),
    },
    {
      id: 'passport',
      title: 'Паспорт',
      description: 'Знакомьтесь с людьми в любом городе мира еще до поездки',
      icon: 'airplane',
      color: colors.success,
      image: require('../../assets/images/premium/passport.png'),
    },
  ];

  const animatedValue = useSharedValue(0);

  useEffect(() => {
    animatedValue.value = withSpring(1);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeatureIndex((prev) => (prev + 1) % features.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const handleGetPremium = () => {
    if (currentSubscription?.isPremium) {
      Alert.alert('Информация', 'У вас уже есть Premium подписка');
      return;
    }

    navigation.navigate('PlansScreen');
  };

  const handleManageSubscription = () => {
    navigation.navigate('SubscriptionScreen');
  };

  const renderFeatureCard = (feature: typeof features[0], index: number) => {
    const isActive = index === currentFeatureIndex;
    const cardAnimatedValue = useSharedValue(isActive ? 1 : 0.8);

    useEffect(() => {
      cardAnimatedValue.value = withTiming(isActive ? 1 : 0.8, {
        duration: 300,
      });
    }, [isActive]);

    const cardAnimatedStyle = useAnimatedStyle(() => {
      return {
        transform: [
          {
            scale: cardAnimatedValue.value,
          },
        ],
        opacity: interpolate(cardAnimatedValue.value, [0.8, 1], [0.6, 1]),
      };
    });

    return (
      <Animated.View key={feature.id} style={[styles.featureCard, cardAnimatedStyle]}>
        <LinearGradient
          colors={[feature.color, `${feature.color}80`]}
          style={styles.featureGradient}
        >
          <View style={styles.featureIcon}>
            <Ionicons name={feature.icon as any} size={32} color={colors.white} />
          </View>
          
          <Text style={styles.featureTitle}>{feature.title}</Text>
          <Text style={styles.featureDescription}>{feature.description}</Text>
        </LinearGradient>
      </Animated.View>
    );
  };

  const renderBenefit = (benefit: { icon: string; title: string; description: string }, index: number) => {
    const benefitAnimatedValue = useSharedValue(0);

    useEffect(() => {
      benefitAnimatedValue.value = withSpring(1, {
        delay: index * 200,
      });
    }, []);

    const benefitAnimatedStyle = useAnimatedStyle(() => {
      return {
        opacity: benefitAnimatedValue.value,
        transform: [
          {
            translateX: interpolate(benefitAnimatedValue.value, [0, 1], [-50, 0]),
          },
        ],
      };
    });

    return (
      <Animated.View key={index} style={[styles.benefitItem, benefitAnimatedStyle]}>
        <View style={styles.benefitIcon}>
          <Ionicons name={benefit.icon as any} size={24} color={colors.primary} />
        </View>
        <View style={styles.benefitContent}>
          <Text style={styles.benefitTitle}>{benefit.title}</Text>
          <Text style={styles.benefitDescription}>{benefit.description}</Text>
        </View>
      </Animated.View>
    );
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: animatedValue.value,
      transform: [
        {
          translateY: interpolate(animatedValue.value, [0, 1], [50, 0]),
        },
      ],
    };
  });

  const benefits = [
    {
      icon: 'trending-up',
      title: '3x больше совпадений',
      description: 'Premium пользователи получают в 3 раза больше матчей',
    },
    {
      icon: 'time',
      title: 'Экономия времени',
      description: 'Находите подходящих людей быстрее с расширенными фильтрами',
    },
    {
      icon: 'shield-checkmark',
      title: 'Приоритетная поддержка',
      description: 'Быстрая помощь от службы поддержки 24/7',
    },
    {
      icon: 'star',
      title: 'Эксклюзивные функции',
      description: 'Доступ к новым функциям раньше других пользователей',
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Premium</Text>
        
        {currentSubscription?.isPremium && (
          <TouchableOpacity
            style={styles.manageButton}
            onPress={handleManageSubscription}
          >
            <Ionicons name="settings-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero секция */}
        <Animated.View style={[styles.heroSection, animatedStyle]}>
          <LinearGradient
            colors={[colors.primary, colors.secondary]}
            style={styles.heroGradient}
          >
            <View style={styles.heroContent}>
              <View style={styles.premiumBadge}>
                <Ionicons name="diamond" size={24} color={colors.warning} />
                <Text style={styles.premiumBadgeText}>PREMIUM</Text>
              </View>
              
              <Text style={styles.heroTitle}>
                Найдите свою любовь быстрее
              </Text>
              
              <Text style={styles.heroSubtitle}>
                Получите доступ ко всем премиум-функциям и увеличьте свои шансы на успех
              </Text>
              
              {!currentSubscription?.isPremium && (
                <TouchableOpacity
                  style={styles.heroButton}
                  onPress={handleGetPremium}
                >
                  <Text style={styles.heroButtonText}>Получить Premium</Text>
                  <Ionicons name="arrow-forward" size={20} color={colors.primary} />
                </TouchableOpacity>
              )}
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Карусель функций */}
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Премиум функции</Text>
          
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            style={styles.featuresCarousel}
            onMomentumScrollEnd={(event) => {
              const index = Math.round(event.nativeEvent.contentOffset.x / (screenWidth - 40));
              setCurrentFeatureIndex(index);
            }}
          >
            {features.map(renderFeatureCard)}
          </ScrollView>
          
          {/* Индикаторы */}
          <View style={styles.featureIndicators}>
            {features.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.featureIndicator,
                  index === currentFeatureIndex && styles.featureIndicatorActive,
                ]}
              />
            ))}
          </View>
        </View>

        {/* Преимущества */}
        <View style={styles.benefitsSection}>
          <Text style={styles.sectionTitle}>Почему Premium?</Text>
          
          <View style={styles.benefitsList}>
            {benefits.map(renderBenefit)}
          </View>
        </View>

        {/* Статистика */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Результаты Premium пользователей</Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>3x</Text>
              <Text style={styles.statLabel}>Больше матчей</Text>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>85%</Text>
              <Text style={styles.statLabel}>Находят пару</Text>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>2 нед</Text>
              <Text style={styles.statLabel}>Среднее время</Text>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>4.9★</Text>
              <Text style={styles.statLabel}>Рейтинг</Text>
            </View>
          </View>
        </View>

        {/* Отзывы */}
        <View style={styles.reviewsSection}>
          <Text style={styles.sectionTitle}>Отзывы пользователей</Text>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.reviewCard}>
              <View style={styles.reviewHeader}>
                <Image
                  source={{ uri: 'https://randomuser.me/api/portraits/women/1.jpg' }}
                  style={styles.reviewAvatar}
                />
                <View>
                  <Text style={styles.reviewName}>Анна, 28</Text>
                  <View style={styles.reviewRating}>
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Ionicons key={star} name="star" size={14} color={colors.warning} />
                    ))}
                  </View>
                </View>
              </View>
              <Text style={styles.reviewText}>
                "Благодаря Premium я нашла свою любовь всего за месяц! Функция 'Кто лайкнул' очень помогла."
              </Text>
            </View>
            
            <View style={styles.reviewCard}>
              <View style={styles.reviewHeader}>
                <Image
                  source={{ uri: 'https://randomuser.me/api/portraits/men/2.jpg' }}
                  style={styles.reviewAvatar}
                />
                <View>
                  <Text style={styles.reviewName}>Михаил, 32</Text>
                  <View style={styles.reviewRating}>
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Ionicons key={star} name="star" size={14} color={colors.warning} />
                    ))}
                  </View>
                </View>
              </View>
              <Text style={styles.reviewText}>
                "Суперлайки действительно работают! Получил в 5 раз больше откликов."
              </Text>
            </View>
          </ScrollView>
        </View>

        {/* CTA секция */}
        {!currentSubscription?.isPremium && (
          <View style={styles.ctaSection}>
            <LinearGradient
              colors={[colors.warning, '#FF8F00']}
              style={styles.ctaGradient}
            >
              <Text style={styles.ctaTitle}>Начните прямо сейчас!</Text>
              <Text style={styles.ctaSubtitle}>
                Первые 7 дней бесплатно, затем от 299₽/месяц
              </Text>
              
              <TouchableOpacity
                style={styles.ctaButton}
                onPress={handleGetPremium}
              >
                <Text style={styles.ctaButtonText}>Попробовать бесплатно</Text>
              </TouchableOpacity>
              
              <Text style={styles.ctaDisclaimer}>
                Отмена в любое время. Без скрытых платежей.
              </Text>
            </LinearGradient>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  manageButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  heroSection: {
    margin: spacing.lg,
    borderRadius: 20,
    overflow: 'hidden',
  },
  heroGradient: {
    padding: spacing.xl,
    alignItems: 'center',
  },
  heroContent: {
    alignItems: 'center',
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    marginBottom: spacing.lg,
    gap: spacing.sm,
  },
  premiumBadgeText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  heroTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.white,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  heroSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.white,
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 22,
  },
  heroButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 25,
    gap: spacing.sm,
  },
  heroButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.primary,
  },
  featuresSection: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  featuresCarousel: {
    marginBottom: spacing.md,
  },
  featureCard: {
    width: screenWidth - 80,
    marginHorizontal: spacing.sm,
    borderRadius: 16,
    overflow: 'hidden',
  },
  featureGradient: {
    padding: spacing.xl,
    alignItems: 'center',
    minHeight: 200,
    justifyContent: 'center',
  },
  featureIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  featureTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  featureDescription: {
    fontSize: typography.sizes.md,
    color: colors.white,
    textAlign: 'center',
    lineHeight: 22,
  },
  featureIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.sm,
  },
  featureIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.border,
  },
  featureIndicatorActive: {
    backgroundColor: colors.primary,
  },
  benefitsSection: {
    padding: spacing.lg,
  },
  benefitsList: {
    gap: spacing.lg,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
  },
  benefitIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  benefitDescription: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  statsSection: {
    padding: spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: colors.surface,
    padding: spacing.lg,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  statNumber: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  reviewsSection: {
    padding: spacing.lg,
  },
  reviewCard: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    borderRadius: 16,
    marginRight: spacing.md,
    width: 280,
    borderWidth: 1,
    borderColor: colors.border,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    gap: spacing.md,
  },
  reviewAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  reviewName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  reviewRating: {
    flexDirection: 'row',
    gap: spacing.xs,
  },
  reviewText: {
    fontSize: typography.sizes.sm,
    color: colors.text,
    lineHeight: 20,
    fontStyle: 'italic',
  },
  ctaSection: {
    margin: spacing.lg,
    borderRadius: 20,
    overflow: 'hidden',
  },
  ctaGradient: {
    padding: spacing.xl,
    alignItems: 'center',
  },
  ctaTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  ctaSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.white,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  ctaButton: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 25,
    marginBottom: spacing.md,
  },
  ctaButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.warning,
  },
  ctaDisclaimer: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    textAlign: 'center',
    opacity: 0.8,
  },
});

export default PremiumScreen;
