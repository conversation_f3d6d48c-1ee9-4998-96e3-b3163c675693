/* Стили для страницы модерации админ-панели */

.container {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title {
  display: flex;
  align-items: center;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.titleIcon {
  margin-right: 12px;
  color: #e74c3c;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.actionButton {
  border-color: #667eea;
  color: #667eea;
  transition: all 0.3s ease;
}

.actionButton:hover {
  background-color: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Статистика */
.statsGrid {
  margin-bottom: 32px;
}

.statCard {
  height: 100%;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
}

.statCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Фильтры */
.filtersCard {
  margin-bottom: 24px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Таблица модерации */
.moderationCard {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.errorAlert {
  margin: 16px;
  border-radius: 12px;
}

.moderationRow {
  transition: all 0.3s ease;
}

.moderationRow:hover {
  background-color: rgba(102, 126, 234, 0.05);
  transform: translateX(2px);
}

/* Контент */
.contentCell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contentPreview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thumbnailImage {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.thumbnailImage:hover {
  transform: scale(1.1);
  border-color: #667eea;
}

/* Действия */
.actionButtons {
  display: flex;
  gap: 4px;
}

.actionButtons .MuiIconButton-root {
  transition: all 0.3s ease;
}

.actionButtons .MuiIconButton-root:hover {
  transform: scale(1.1);
}

/* Предпросмотр */
.previewContent {
  padding: 16px 0;
}

.contentDisplay {
  margin: 16px 0;
  padding: 16px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.imageList {
  max-height: 400px;
  overflow-y: auto;
}

.previewImage {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.previewImage:hover {
  transform: scale(1.02);
}

.textContent {
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  font-family: 'Roboto Mono', monospace;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Флаги */
.flagsSection {
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.flagsList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

/* Адаптивность */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .headerActions {
    flex-direction: column;
    width: 100%;
    gap: 12px;
  }

  .title {
    font-size: 1.5rem;
  }

  .contentCell {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .thumbnailImage {
    width: 50px;
    height: 50px;
  }

  .actionButtons {
    flex-direction: column;
    gap: 2px;
  }

  .imageList {
    max-height: 300px;
  }

  .flagsList {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px;
  }

  .header {
    padding: 16px;
  }

  .title {
    font-size: 1.25rem;
  }

  .thumbnailImage {
    width: 40px;
    height: 40px;
  }

  .contentDisplay {
    padding: 12px;
  }

  .textContent {
    padding: 12px;
    font-size: 0.875rem;
  }

  .imageList {
    max-height: 250px;
  }
}

/* Анимации */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.statCard,
.filtersCard,
.moderationCard {
  animation: fadeInUp 0.6s ease-out;
}

.statCard:nth-child(1) { animation-delay: 0.1s; }
.statCard:nth-child(2) { animation-delay: 0.2s; }
.statCard:nth-child(3) { animation-delay: 0.3s; }
.statCard:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.moderationRow {
  animation: slideInLeft 0.3s ease-out;
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }

  .header,
  .statCard,
  .filtersCard,
  .moderationCard {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .title {
    color: #ffffff;
  }

  .moderationRow:hover {
    background-color: rgba(102, 126, 234, 0.1);
  }

  .contentDisplay {
    background: rgba(40, 40, 40, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .textContent {
    background: rgba(30, 30, 30, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
  }

  .flagsSection {
    background: rgba(255, 193, 7, 0.15);
    border-color: rgba(255, 193, 7, 0.4);
  }
}

/* Эффекты при наведении */
.statCard {
  position: relative;
  overflow: hidden;
}

.statCard::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(231, 76, 60, 0.1) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.statCard:hover::after {
  width: 200px;
  height: 200px;
}

/* Кастомные скроллбары */
.imageList::-webkit-scrollbar {
  width: 6px;
}

.imageList::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.imageList::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  border-radius: 3px;
}

.imageList::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #d62c1a 0%, #a93226 100%);
}

/* Специальные эффекты для контента */
.thumbnailImage {
  position: relative;
  overflow: hidden;
}

.thumbnailImage::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.thumbnailImage:hover::before {
  left: 100%;
}

/* Статусы и приоритеты */
.MuiChip-colorWarning {
  background-color: rgba(255, 193, 7, 0.2);
  color: #f57c00;
}

.MuiChip-colorError {
  background-color: rgba(244, 67, 54, 0.2);
  color: #d32f2f;
}

.MuiChip-colorSuccess {
  background-color: rgba(76, 175, 80, 0.2);
  color: #388e3c;
}
