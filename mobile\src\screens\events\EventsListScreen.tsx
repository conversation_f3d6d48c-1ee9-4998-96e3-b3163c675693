import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { eventsService } from '../../services/eventsService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { Event, EventCategory, EventFilters } from '../../types/events.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface EventsListScreenProps {}

const EventsListScreen: React.FC<EventsListScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location } = useLocation();
  const queryClient = useQueryClient();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [filters, setFilters] = useState<EventFilters>({
    category: undefined,
    dateFrom: undefined,
    dateTo: undefined,
    location: undefined,
    priceRange: undefined,
  });

  // Загрузка событий
  const {
    data: events,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['events', selectedCategory, filters, location],
    queryFn: () => eventsService.getEvents(selectedCategory, filters, location),
    enabled: !!location,
  });

  // Загрузка категорий
  const {
    data: categories,
  } = useQuery({
    queryKey: ['events', 'categories'],
    queryFn: () => eventsService.getCategories(),
  });

  // Мутация для участия в событии
  const joinEventMutation = useMutation({
    mutationFn: (eventId: string) => eventsService.joinEvent(eventId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['myEvents'] });
    },
  });

  // Мутация для выхода из события
  const leaveEventMutation = useMutation({
    mutationFn: (eventId: string) => eventsService.leaveEvent(eventId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['events'] });
      queryClient.invalidateQueries({ queryKey: ['myEvents'] });
    },
  });

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      if (location) {
        refetch();
      }
    }, [location, refetch])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleJoinEvent = (eventId: string) => {
    Alert.alert(
      'Присоединиться к событию',
      'Вы уверены, что хотите присоединиться к этому событию?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Присоединиться',
          onPress: () => joinEventMutation.mutate(eventId),
        },
      ]
    );
  };

  const handleLeaveEvent = (eventId: string) => {
    Alert.alert(
      'Покинуть событие',
      'Вы уверены, что хотите покинуть это событие?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Покинуть',
          style: 'destructive',
          onPress: () => leaveEventMutation.mutate(eventId),
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Сегодня';
    if (diffInDays === 1) return 'Завтра';
    if (diffInDays === -1) return 'Вчера';
    if (diffInDays > 1 && diffInDays <= 7) return `Через ${diffInDays} дн.`;

    return date.toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'short',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}м`;
    }
    return `${distance.toFixed(1)}км`;
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'party':
        return 'musical-notes-outline';
      case 'sport':
        return 'fitness-outline';
      case 'culture':
        return 'library-outline';
      case 'food':
        return 'restaurant-outline';
      case 'outdoor':
        return 'leaf-outline';
      default:
        return 'calendar-outline';
    }
  };

  const renderCategoryButton = (category: EventCategory) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryButton,
        selectedCategory === category.id && styles.categoryButtonActive,
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <Ionicons
        name={category.icon as any}
        size={20}
        color={selectedCategory === category.id ? colors.white : colors.text}
      />
      <Text
        style={[
          styles.categoryButtonText,
          selectedCategory === category.id && styles.categoryButtonTextActive,
        ]}
      >
        {category.name}
      </Text>
    </TouchableOpacity>
  );

  const renderEventCard = ({ item, index }: { item: Event; index: number }) => {
    const animatedValue = useSharedValue(0);
    const isParticipant = item.participants.some(p => p.id === user?.id);
    const isOrganizer = item.organizerId === user?.id;
    const canJoin = !isParticipant && item.participants.length < item.maxParticipants;

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateY: interpolate(animatedValue.value, [0, 1], [30, 0]),
          },
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.95, 1]),
          },
        ],
      };
    });

    return (
      <Animated.View style={[styles.eventCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('EventDetail', { eventId: item.id })}
          activeOpacity={0.8}
        >
          <Image source={{ uri: item.image }} style={styles.eventImage} />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.eventImageGradient}
          />
          
          {/* Тип события */}
          <View style={styles.eventTypeBadge}>
            <Ionicons
              name={getEventTypeIcon(item.type) as any}
              size={16}
              color={colors.white}
            />
            <Text style={styles.eventTypeText}>{item.type}</Text>
          </View>
          
          {/* Цена */}
          {item.price > 0 && (
            <View style={styles.priceBadge}>
              <Text style={styles.priceText}>{item.price}₽</Text>
            </View>
          )}
          
          <View style={styles.eventInfo}>
            <Text style={styles.eventTitle}>{item.title}</Text>
            
            <View style={styles.eventDetails}>
              <View style={styles.eventDateTime}>
                <Ionicons name="calendar-outline" size={16} color={colors.textSecondary} />
                <Text style={styles.dateTimeText}>
                  {formatDate(item.dateTime)} в {formatTime(item.dateTime)}
                </Text>
              </View>
              
              <View style={styles.eventLocation}>
                <Ionicons name="location-outline" size={16} color={colors.textSecondary} />
                <Text style={styles.locationText}>
                  {item.location.name} • {formatDistance(item.distance)}
                </Text>
              </View>
            </View>
            
            <View style={styles.eventParticipants}>
              <View style={styles.participantsAvatars}>
                {item.participants.slice(0, 3).map((participant, idx) => (
                  <Image
                    key={participant.id}
                    source={{ uri: participant.avatar }}
                    style={[
                      styles.participantAvatar,
                      { marginLeft: idx > 0 ? -8 : 0 },
                    ]}
                  />
                ))}
                {item.participants.length > 3 && (
                  <View style={[styles.participantAvatar, styles.moreParticipants]}>
                    <Text style={styles.moreParticipantsText}>
                      +{item.participants.length - 3}
                    </Text>
                  </View>
                )}
              </View>
              
              <Text style={styles.participantsCount}>
                {item.participants.length}/{item.maxParticipants} участников
              </Text>
            </View>
          </View>
        </TouchableOpacity>
        
        <View style={styles.eventActions}>
          {isOrganizer ? (
            <View style={styles.organizerBadge}>
              <Ionicons name="star" size={16} color={colors.warning} />
              <Text style={styles.organizerText}>Организатор</Text>
            </View>
          ) : isParticipant ? (
            <TouchableOpacity
              style={[styles.actionButton, styles.leaveButton]}
              onPress={() => handleLeaveEvent(item.id)}
              disabled={leaveEventMutation.isPending}
            >
              <Ionicons name="exit-outline" size={16} color={colors.error} />
              <Text style={[styles.actionButtonText, { color: colors.error }]}>
                Покинуть
              </Text>
            </TouchableOpacity>
          ) : canJoin ? (
            <TouchableOpacity
              style={[styles.actionButton, styles.joinButton]}
              onPress={() => handleJoinEvent(item.id)}
              disabled={joinEventMutation.isPending}
            >
              <Ionicons name="add" size={16} color={colors.success} />
              <Text style={[styles.actionButtonText, { color: colors.success }]}>
                Участвовать
              </Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.fullBadge}>
              <Text style={styles.fullText}>Мест нет</Text>
            </View>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, styles.detailsButton]}
            onPress={() => navigation.navigate('EventDetail', { eventId: item.id })}
          >
            <Ionicons name="information-circle-outline" size={16} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>
              Подробнее
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  if (isLoading && !events) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Загружаем события...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="calendar-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить список событий
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>События</Text>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('EventFilters')}
          >
            <Ionicons name="options-outline" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.createButton}
            onPress={() => navigation.navigate('CreateEvent')}
          >
            <Ionicons name="add" size={24} color={colors.white} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Категории */}
      {categories && (
        <View style={styles.categoriesContainer}>
          <FlatList
            data={[{ id: 'all', name: 'Все', icon: 'grid-outline' }, ...categories]}
            renderItem={({ item }) => renderCategoryButton(item)}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>
      )}

      {/* Список событий */}
      <FlatList
        data={events}
        renderItem={renderEventCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="calendar-outline" size={64} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>События не найдены</Text>
            <Text style={styles.emptyText}>
              Попробуйте изменить фильтры или создайте свое событие
            </Text>
            <TouchableOpacity
              style={styles.createEventButton}
              onPress={() => navigation.navigate('CreateEvent')}
            >
              <Text style={styles.createEventButtonText}>Создать событие</Text>
            </TouchableOpacity>
          </View>
        }
      />

      {/* Индикатор загрузки действий */}
      {(joinEventMutation.isPending || leaveEventMutation.isPending) && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  createButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoriesContainer: {
    paddingVertical: spacing.sm,
  },
  categoriesList: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    gap: spacing.xs,
  },
  categoryButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  categoryButtonTextActive: {
    color: colors.white,
  },
  listContainer: {
    padding: spacing.md,
  },
  eventCard: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    marginBottom: spacing.md,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  eventImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  eventImageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  eventTypeBadge: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  eventTypeText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
    textTransform: 'capitalize',
  },
  priceBadge: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    backgroundColor: colors.success,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  priceText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.bold,
  },
  eventInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.md,
  },
  eventTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  eventDetails: {
    marginBottom: spacing.sm,
  },
  eventDateTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
    gap: spacing.xs,
  },
  dateTimeText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  eventLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  locationText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  eventParticipants: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  participantsAvatars: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  participantAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.white,
  },
  moreParticipants: {
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: -8,
  },
  moreParticipantsText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  participantsCount: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  eventActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.surface,
  },
  organizerBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  organizerText: {
    fontSize: typography.sizes.sm,
    color: colors.warning,
    fontWeight: typography.weights.medium,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 12,
    backgroundColor: colors.background,
    gap: spacing.xs,
  },
  joinButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  leaveButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  detailsButton: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
  },
  fullBadge: {
    backgroundColor: colors.textSecondary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  fullText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  createEventButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  createEventButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EventsListScreen;
