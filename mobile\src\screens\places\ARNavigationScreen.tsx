import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
  Animated,
  PanResponder,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { Camera } from 'expo-camera';
import * as Location from 'expo-location';
import { Gyroscope, Magnetometer } from 'expo-sensors';
import { placesService } from '../../services/placesService';
import { navigationService } from '../../services/navigationService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { Place, NavigationStep } from '../../types/places.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ARNavigationScreenProps {}

const ARNavigationScreen: React.FC<ARNavigationScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const { location, requestLocation } = useLocation();

  const { placeId } = route.params as { placeId: string };

  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isNavigating, setIsNavigating] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [heading, setHeading] = useState(0);
  const [distance, setDistance] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState(0);
  const [showInstructions, setShowInstructions] = useState(true);

  const cameraRef = useRef<Camera>(null);
  const arElementsOpacity = useRef(new Animated.Value(0)).current;
  const instructionsOpacity = useRef(new Animated.Value(1)).current;

  // Загрузка места назначения
  const {
    data: place,
    isLoading: placeLoading,
  } = useQuery({
    queryKey: ['place', placeId],
    queryFn: () => placesService.getPlaceDetails(placeId),
  });

  // Загрузка маршрута
  const {
    data: navigationData,
    isLoading: navigationLoading,
    refetch: refetchNavigation,
  } = useQuery({
    queryKey: ['navigation', placeId, location],
    queryFn: () => navigationService.getRoute(location, place?.coordinates),
    enabled: !!location && !!place,
  });

  // Запрос разрешений камеры
  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  // Запрос геолокации
  useEffect(() => {
    if (!location) {
      requestLocation();
    }
  }, [location, requestLocation]);

  // Подписка на датчики
  useEffect(() => {
    let magnetometerSubscription: any;
    let gyroscopeSubscription: any;

    if (isNavigating) {
      // Магнитометр для определения направления
      magnetometerSubscription = Magnetometer.addListener(({ x, y, z }) => {
        const angle = Math.atan2(y, x) * (180 / Math.PI);
        setHeading(angle < 0 ? angle + 360 : angle);
      });

      // Гироскоп для плавности движения
      gyroscopeSubscription = Gyroscope.addListener(({ x, y, z }) => {
        // Обработка данных гироскопа для стабилизации AR элементов
      });

      Magnetometer.setUpdateInterval(100);
      Gyroscope.setUpdateInterval(100);
    }

    return () => {
      magnetometerSubscription?.remove();
      gyroscopeSubscription?.remove();
    };
  }, [isNavigating]);

  // Обновление позиции пользователя
  useEffect(() => {
    let locationSubscription: any;

    if (isNavigating && location) {
      locationSubscription = Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: 1000,
          distanceInterval: 1,
        },
        (newLocation) => {
          if (place) {
            const dist = calculateDistance(
              newLocation.coords.latitude,
              newLocation.coords.longitude,
              place.coordinates.latitude,
              place.coordinates.longitude
            );
            setDistance(dist);
            
            // Обновление текущего шага навигации
            if (navigationData?.steps) {
              updateCurrentStep(newLocation.coords, navigationData.steps);
            }
          }
        }
      );
    }

    return () => {
      locationSubscription?.remove();
    };
  }, [isNavigating, location, place, navigationData]);

  // Анимация появления AR элементов
  useEffect(() => {
    if (isNavigating) {
      Animated.timing(arElementsOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }
  }, [isNavigating]);

  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Радиус Земли в км
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  const updateCurrentStep = (currentLocation: any, steps: NavigationStep[]) => {
    // Логика определения текущего шага навигации
    for (let i = 0; i < steps.length; i++) {
      const stepDistance = calculateDistance(
        currentLocation.latitude,
        currentLocation.longitude,
        steps[i].coordinates.latitude,
        steps[i].coordinates.longitude
      );
      
      if (stepDistance < 0.05) { // 50 метров
        setCurrentStep(i + 1);
        break;
      }
    }
  };

  const startNavigation = () => {
    if (!hasPermission) {
      Alert.alert('Ошибка', 'Необходимо разрешение на использование камеры');
      return;
    }

    if (!location) {
      Alert.alert('Ошибка', 'Не удалось определить ваше местоположение');
      return;
    }

    setIsNavigating(true);
    setShowInstructions(false);
    
    Animated.timing(instructionsOpacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const stopNavigation = () => {
    setIsNavigating(false);
    setCurrentStep(0);
    
    Animated.timing(arElementsOpacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const formatDistance = (dist: number) => {
    if (dist < 1) {
      return `${Math.round(dist * 1000)}м`;
    }
    return `${dist.toFixed(1)}км`;
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) {
      return `${Math.round(minutes)} мин`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}ч ${mins}мин`;
  };

  const getDirectionIcon = (direction: string) => {
    switch (direction) {
      case 'straight':
        return 'arrow-up';
      case 'left':
        return 'arrow-back';
      case 'right':
        return 'arrow-forward';
      case 'slight_left':
        return 'arrow-up-outline';
      case 'slight_right':
        return 'arrow-up-outline';
      default:
        return 'navigate';
    }
  };

  const renderARElements = () => {
    if (!isNavigating || !navigationData) return null;

    const currentStepData = navigationData.steps[currentStep];
    
    return (
      <Animated.View style={[styles.arOverlay, { opacity: arElementsOpacity }]}>
        {/* Направляющая стрелка */}
        <View style={styles.arArrow}>
          <Ionicons
            name={getDirectionIcon(currentStepData?.direction || 'straight') as any}
            size={60}
            color={colors.primary}
          />
        </View>
        
        {/* Информация о маршруте */}
        <View style={styles.routeInfo}>
          <Text style={styles.distanceText}>{formatDistance(distance)}</Text>
          <Text style={styles.timeText}>{formatTime(estimatedTime)}</Text>
        </View>
        
        {/* Текущая инструкция */}
        {currentStepData && (
          <View style={styles.instructionCard}>
            <Ionicons
              name={getDirectionIcon(currentStepData.direction) as any}
              size={24}
              color={colors.primary}
            />
            <Text style={styles.instructionText}>{currentStepData.instruction}</Text>
          </View>
        )}
        
        {/* Индикатор цели */}
        <View style={styles.targetIndicator}>
          <View style={styles.targetRing}>
            <View style={styles.targetCenter} />
          </View>
          <Text style={styles.targetText}>{place?.name}</Text>
        </View>
      </Animated.View>
    );
  };

  const renderInstructions = () => {
    if (!showInstructions) return null;

    return (
      <Animated.View style={[styles.instructionsOverlay, { opacity: instructionsOpacity }]}>
        <View style={styles.instructionsCard}>
          <Ionicons name="camera-outline" size={48} color={colors.primary} />
          <Text style={styles.instructionsTitle}>AR Навигация</Text>
          <Text style={styles.instructionsText}>
            Направьте камеру вперед и следуйте указателям для навигации к месту назначения
          </Text>
          
          <View style={styles.instructionsList}>
            <View style={styles.instructionItem}>
              <Ionicons name="phone-portrait-outline" size={20} color={colors.textSecondary} />
              <Text style={styles.instructionItemText}>Держите телефон вертикально</Text>
            </View>
            <View style={styles.instructionItem}>
              <Ionicons name="walk-outline" size={20} color={colors.textSecondary} />
              <Text style={styles.instructionItemText}>Идите в указанном направлении</Text>
            </View>
            <View style={styles.instructionItem}>
              <Ionicons name="eye-outline" size={20} color={colors.textSecondary} />
              <Text style={styles.instructionItemText}>Следите за AR указателями</Text>
            </View>
          </View>
          
          <TouchableOpacity style={styles.startButton} onPress={startNavigation}>
            <Text style={styles.startButtonText}>Начать навигацию</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  if (placeLoading || navigationLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Подготовка навигации...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (hasPermission === null) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.permissionContainer}>
          <Ionicons name="camera-outline" size={64} color={colors.textSecondary} />
          <Text style={styles.permissionText}>Запрос разрешения камеры...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (hasPermission === false) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.permissionContainer}>
          <Ionicons name="camera-off-outline" size={64} color={colors.error} />
          <Text style={styles.permissionTitle}>Нет доступа к камере</Text>
          <Text style={styles.permissionText}>
            Для AR навигации необходимо разрешение на использование камеры
          </Text>
          <TouchableOpacity
            style={styles.permissionButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.permissionButtonText}>Вернуться</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Камера */}
      <Camera
        ref={cameraRef}
        style={styles.camera}
        type={Camera.Constants.Type.back}
        autoFocus={Camera.Constants.AutoFocus.on}
      />

      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.white} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>AR Навигация</Text>
        
        {isNavigating && (
          <TouchableOpacity
            style={styles.stopButton}
            onPress={stopNavigation}
          >
            <Ionicons name="stop" size={24} color={colors.white} />
          </TouchableOpacity>
        )}
      </View>

      {/* AR элементы */}
      {renderARElements()}

      {/* Инструкции */}
      {renderInstructions()}

      {/* Нижняя панель */}
      {isNavigating && (
        <View style={styles.bottomPanel}>
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              Шаг {currentStep + 1} из {navigationData?.steps.length || 0}
            </Text>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${((currentStep + 1) / (navigationData?.steps.length || 1)) * 100}%`,
                  },
                ]}
              />
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.mapButton}
            onPress={() => navigation.navigate('PlaceMap', { placeId })}
          >
            <Ionicons name="map-outline" size={20} color={colors.primary} />
            <Text style={styles.mapButtonText}>Карта</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  camera: {
    flex: 1,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.md,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stopButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(244,67,54,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  arOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  arArrow: {
    position: 'absolute',
    top: screenHeight * 0.3,
    alignSelf: 'center',
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 40,
    padding: spacing.lg,
  },
  routeInfo: {
    position: 'absolute',
    top: spacing.xl * 3,
    alignSelf: 'center',
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 20,
    alignItems: 'center',
  },
  distanceText: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  timeText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
  },
  instructionCard: {
    position: 'absolute',
    bottom: spacing.xl * 4,
    left: spacing.lg,
    right: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 16,
    gap: spacing.md,
  },
  instructionText: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  targetIndicator: {
    position: 'absolute',
    alignSelf: 'center',
    alignItems: 'center',
  },
  targetRing: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(33,150,243,0.2)',
  },
  targetCenter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.primary,
  },
  targetText: {
    marginTop: spacing.sm,
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 8,
  },
  instructionsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  instructionsCard: {
    backgroundColor: colors.surface,
    borderRadius: 20,
    padding: spacing.xl,
    alignItems: 'center',
    maxWidth: 320,
  },
  instructionsTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  instructionsText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: spacing.lg,
  },
  instructionsList: {
    alignSelf: 'stretch',
    marginBottom: spacing.lg,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    gap: spacing.md,
  },
  instructionItemText: {
    flex: 1,
    fontSize: typography.sizes.sm,
    color: colors.text,
  },
  startButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 25,
    alignSelf: 'stretch',
  },
  startButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
    textAlign: 'center',
  },
  bottomPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.lg,
  },
  progressContainer: {
    flex: 1,
  },
  progressText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.9)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    gap: spacing.xs,
  },
  mapButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.white,
    marginTop: spacing.md,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  permissionTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  permissionText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  permissionButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  permissionButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
});

export default ARNavigationScreen;
