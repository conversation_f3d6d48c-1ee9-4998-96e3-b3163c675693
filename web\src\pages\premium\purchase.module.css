.container {
  padding: 3rem 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.title {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
}

.periodToggle {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.periodLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
}

.discountChip {
  background: linear-gradient(45deg, #4caf50, #8bc34a);
  color: white;
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.featuresCard {
  margin-bottom: 3rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.featuresTitle {
  text-align: center;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.featuresGrid {
  margin-top: 2rem;
}

.featureItem {
  text-align: center;
  padding: 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8f9fa, #fff);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.featureItem:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #fff, #f8f9fa);
}

.featureIcon {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.featureIcon svg {
  font-size: 3rem;
}

.featureTitle {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.plansGrid {
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.planCard {
  height: 100%;
  border-radius: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.planCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.popularPlan {
  border: 3px solid #ffd700;
  transform: scale(1.05);
  box-shadow: 0 15px 35px rgba(255, 215, 0, 0.3);
}

.popularPlan:hover {
  transform: scale(1.05) translateY(-8px);
}

.popularBadge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  font-weight: bold;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.planContent {
  padding: 2rem;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.planName {
  margin-bottom: 1rem;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.planPrice {
  margin-bottom: 1rem;
}

.price {
  font-weight: 800;
  background: linear-gradient(45deg, #e91e63, #9c27b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.savings {
  color: #4caf50;
  font-weight: 600;
  background: rgba(76, 175, 80, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 1rem;
}

.planFeatures {
  flex: 1;
  padding: 0;
}

.planFeature {
  padding: 0.5rem 0;
}

.selectButton {
  margin-top: 1rem;
  border-radius: 25px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  text-transform: none;
  font-size: 1.1rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.selectButton:hover {
  background: linear-gradient(45deg, #764ba2, #667eea);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.guaranteeCard {
  border-radius: 20px;
  background: linear-gradient(135deg, #4caf50, #8bc34a);
  color: white;
  box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
  position: relative;
  z-index: 1;
}

.guaranteeTitle {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.guaranteeIcon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loadingContainer {
  text-align: center;
  padding: 3rem;
  color: white;
}

.errorAlert {
  margin: 2rem 0;
  border-radius: 12px;
}

.purchaseContent {
  padding: 1rem 0;
}

.selectedPlan {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa, #fff);
  border-radius: 12px;
  border: 2px solid #e3f2fd;
}

.paymentMethod {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 2rem 0;
  }
  
  .header {
    margin-bottom: 2rem;
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.1rem;
  }
  
  .featuresCard,
  .plansGrid,
  .guaranteeCard {
    margin-bottom: 2rem;
  }
  
  .featureItem {
    padding: 1rem;
  }
  
  .featureIcon svg {
    font-size: 2.5rem;
  }
  
  .planCard {
    margin-bottom: 1.5rem;
  }
  
  .popularPlan {
    transform: none;
  }
  
  .popularPlan:hover {
    transform: translateY(-4px);
  }
  
  .planContent {
    padding: 1.5rem;
  }
  
  .planName {
    font-size: 1.75rem;
  }
  
  .price {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem 0;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .featureItem {
    padding: 0.75rem;
  }
  
  .planContent {
    padding: 1rem;
  }
  
  .selectButton {
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .featureItem,
  .planCard,
  .selectButton {
    transition: none;
  }
  
  .featureItem:hover,
  .planCard:hover,
  .selectButton:hover {
    transform: none;
  }
  
  .discountChip {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .planCard {
    border: 2px solid #000;
  }
  
  .popularPlan {
    border-color: #000;
  }
  
  .featureItem {
    border: 1px solid #000;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .featuresCard {
    background: rgba(45, 45, 45, 0.95);
    color: #fff;
  }
  
  .planCard {
    background: rgba(45, 45, 45, 0.95);
    color: #fff;
  }
  
  .featureItem {
    background: linear-gradient(135deg, #2d2d2d, #3d3d3d);
    border-color: #555;
  }
  
  .selectedPlan {
    background: linear-gradient(135deg, #2d2d2d, #3d3d3d);
    border-color: #555;
  }
}

/* Special effects */
.planCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.planCard:hover::before {
  left: 100%;
}

.popularPlan::after {
  content: '⭐';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  animation: sparkle 2s infinite;
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 0.8;
  }
}

/* Loading animation */
.loadingContainer svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
