import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
  StatusBar,
  Animated,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface VerifyPhoneScreenProps {
  navigation: any;
  route: any;
}

interface RouteParams {
  phone?: string;
  userId?: string;
}

const VerifyPhoneScreen: React.FC<VerifyPhoneScreenProps> = ({ navigation }) => {
  const route = useRoute();
  const { phone, userId } = (route.params as RouteParams) || {};
  const { theme } = useTheme();
  const { verifyPhone, resendPhoneVerification } = useAuth();

  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const [canResend, setCanResend] = useState(false);

  // Анимации
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    // Анимация появления
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Таймер для повторной отправки
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleVerifyPhone = async () => {
    if (!verificationCode.trim()) {
      Alert.alert('Ошибка', 'Пожалуйста, введите код подтверждения');
      return;
    }

    if (verificationCode.length !== 4) {
      Alert.alert('Ошибка', 'Код должен содержать 4 цифры');
      return;
    }

    setIsLoading(true);

    try {
      const result = await verifyPhone({
        phone: phone || '',
        code: verificationCode,
        userId: userId || '',
      });

      if (result.success) {
        Alert.alert(
          'Успешно!',
          'Номер телефона подтвержден!',
          [
            {
              text: 'Продолжить',
              onPress: () => navigation.navigate('MainApp'),
            },
          ]
        );
      } else {
        Alert.alert('Ошибка', result.message || 'Неверный код подтверждения');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при подтверждении номера');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!canResend) return;

    setIsResending(true);

    try {
      const result = await resendPhoneVerification(phone || '');

      if (result.success) {
        Alert.alert('Успешно', 'SMS с кодом отправлено повторно');
        setTimeLeft(60);
        setCanResend(false);

        // Перезапуск таймера
        const timer = setInterval(() => {
          setTimeLeft((prev) => {
            if (prev <= 1) {
              setCanResend(true);
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось отправить SMS');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при отправке SMS');
    } finally {
      setIsResending(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatPhone = (phoneNumber: string) => {
    // Форматирование номера телефона для отображения
    if (phoneNumber.startsWith('+7')) {
      return phoneNumber.replace(/(\+7)(\d{3})(\d{3})(\d{2})(\d{2})/, '$1 ($2) $3-$4-$5');
    }
    return phoneNumber;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Иконка */}
            <View style={styles.iconContainer}>
              <Icon name="phone" size={80} color="#FFFFFF" />
            </View>

            {/* Заголовок */}
            <Text style={styles.title}>Подтверждение телефона</Text>
            <Text style={styles.subtitle}>
              Мы отправили SMS с кодом на номер
            </Text>
            <Text style={styles.phone}>{formatPhone(phone || '')}</Text>

            {/* Поле ввода кода */}
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.codeInput}
                placeholder="Введите 4-значный код"
                placeholderTextColor="rgba(255, 255, 255, 0.7)"
                value={verificationCode}
                onChangeText={setVerificationCode}
                keyboardType="numeric"
                maxLength={4}
                autoFocus
                textAlign="center"
              />
            </View>

            {/* Кнопка подтверждения */}
            <TouchableOpacity
              style={styles.verifyButton}
              onPress={handleVerifyPhone}
              disabled={isLoading}
            >
              <View style={styles.buttonContent}>
                {isLoading ? (
                  <ActivityIndicator color="#FF6B9D" size="small" />
                ) : (
                  <Text style={styles.verifyButtonText}>Подтвердить</Text>
                )}
              </View>
            </TouchableOpacity>

            {/* Повторная отправка */}
            <View style={styles.resendContainer}>
              <Text style={styles.resendText}>Не получили SMS?</Text>
              <TouchableOpacity
                onPress={handleResendCode}
                disabled={!canResend || isResending}
                style={styles.resendButton}
              >
                {isResending ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <Text
                    style={[
                      styles.resendButtonText,
                      { opacity: canResend ? 1 : 0.5 },
                    ]}
                  >
                    {canResend ? 'Отправить повторно' : `Повторить через ${formatTime(timeLeft)}`}
                  </Text>
                )}
              </TouchableOpacity>
            </View>

            {/* Информация о тарифах */}
            <View style={styles.infoContainer}>
              <Icon name="info" size={16} color="rgba(255, 255, 255, 0.7)" />
              <Text style={styles.infoText}>
                SMS бесплатно. Стандартные тарифы оператора не применяются.
              </Text>
            </View>

            {/* Кнопка назад */}
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Icon name="arrow-back" size={24} color="#FFFFFF" />
              <Text style={styles.backButtonText}>Назад</Text>
            </TouchableOpacity>
          </Animated.View>
        </KeyboardAvoidingView>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  iconContainer: {
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 5,
  },
  phone: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 40,
  },
  inputContainer: {
    width: '100%',
    marginBottom: 30,
  },
  codeInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 20,
    paddingHorizontal: 20,
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 16,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  verifyButton: {
    width: '100%',
    marginBottom: 30,
  },
  buttonContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  verifyButtonText: {
    color: '#FF6B9D',
    fontSize: 18,
    fontWeight: 'bold',
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  resendText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginBottom: 10,
  },
  resendButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  resendButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  infoText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginLeft: 8,
    textAlign: 'center',
    flex: 1,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default VerifyPhoneScreen;
