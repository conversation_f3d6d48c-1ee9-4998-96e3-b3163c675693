import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Badge,
  ImageList,
  ImageListItem,
  ImageListItemBar,
} from '@mui/material';
import {
  Security as SecurityIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  Flag as FlagIcon,
  Photo as PhotoIcon,
  Person as PersonIcon,
  Chat as ChatIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AdminLayout } from '../../../components/admin/AdminLayout';
import { useAuth } from '../../../hooks/useAuth';
import { adminService } from '../../../services/adminService';
import { ModerationItem, ModerationFilters, ModerationListResponse } from '../../../types/admin.types';
import styles from '../../../styles/admin/Moderation.module.css';

const ModerationPage: React.FC = () => {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState<ModerationFilters>({
    type: undefined,
    status: 'pending',
    priority: undefined,
    sortBy: 'submittedAt',
    sortOrder: 'desc',
  });

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(20);
  const [selectedItem, setSelectedItem] = useState<ModerationItem | null>(null);
  const [actionDialog, setActionDialog] = useState<{
    open: boolean;
    type: 'approve' | 'reject';
    item: ModerationItem | null;
  }>({
    open: false,
    type: 'approve',
    item: null,
  });
  const [rejectionReason, setRejectionReason] = useState('');
  const [previewDialog, setPreviewDialog] = useState<{
    open: boolean;
    item: ModerationItem | null;
  }>({
    open: false,
    item: null,
  });

  // Проверка прав доступа
  useEffect(() => {
    if (!isAdmin) {
      router.push('/');
    }
  }, [isAdmin, router]);

  // Загрузка элементов модерации
  const {
    data: moderationData,
    isLoading,
    error,
    refetch,
  } = useQuery<ModerationListResponse>({
    queryKey: ['admin', 'moderation', filters, page, rowsPerPage],
    queryFn: () => adminService.getModerationItems(filters, { page: page + 1, limit: rowsPerPage }),
    enabled: isAdmin,
  });

  // Мутации для модерации
  const approveMutation = useMutation({
    mutationFn: (itemId: string) => adminService.approveModerationItem(itemId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'moderation'] });
      setActionDialog({ open: false, type: 'approve', item: null });
    },
  });

  const rejectMutation = useMutation({
    mutationFn: ({ itemId, reason }: { itemId: string; reason: string }) =>
      adminService.rejectModerationItem(itemId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'moderation'] });
      setActionDialog({ open: false, type: 'reject', item: null });
      setRejectionReason('');
    },
  });

  const handleFilterChange = (field: keyof ModerationFilters, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPage(0);
  };

  const handleAction = (type: 'approve' | 'reject', item: ModerationItem) => {
    setActionDialog({ open: true, type, item });
  };

  const handleConfirmAction = () => {
    if (!actionDialog.item) return;

    if (actionDialog.type === 'approve') {
      approveMutation.mutate(actionDialog.item.id);
    } else {
      if (rejectionReason.trim()) {
        rejectMutation.mutate({
          itemId: actionDialog.item.id,
          reason: rejectionReason,
        });
      }
    }
  };

  const getStatusChip = (status: ModerationItem['status']) => {
    const statusConfig = {
      pending: { label: 'Ожидает', color: 'warning' as const, icon: <WarningIcon /> },
      approved: { label: 'Одобрено', color: 'success' as const, icon: <CheckCircle /> },
      rejected: { label: 'Отклонено', color: 'error' as const, icon: <Cancel /> },
    };

    const config = statusConfig[status];
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        icon={config.icon}
      />
    );
  };

  const getPriorityChip = (priority: ModerationItem['priority']) => {
    const priorityConfig = {
      low: { label: 'Низкий', color: 'default' as const },
      medium: { label: 'Средний', color: 'warning' as const },
      high: { label: 'Высокий', color: 'error' as const },
    };

    const config = priorityConfig[priority];
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  const getTypeIcon = (type: ModerationItem['type']) => {
    switch (type) {
      case 'photo':
        return <PhotoIcon />;
      case 'profile':
        return <PersonIcon />;
      case 'message':
        return <ChatIcon />;
      default:
        return <FlagIcon />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ru-RU');
  };

  const renderContentPreview = (item: ModerationItem) => {
    if (item.type === 'photo') {
      const photos = Array.isArray(item.content) ? item.content : [item.content];
      return (
        <ImageList cols={2} rowHeight={200} className={styles.imageList}>
          {photos.map((photo, index) => (
            <ImageListItem key={index}>
              <img
                src={photo}
                alt={`Фото ${index + 1}`}
                loading="lazy"
                className={styles.previewImage}
              />
              <ImageListItemBar
                title={`Фото ${index + 1}`}
                subtitle={`Оценка AI: ${item.autoModerationScore?.toFixed(2) || 'N/A'}`}
              />
            </ImageListItem>
          ))}
        </ImageList>
      );
    }

    return (
      <Typography variant="body2" className={styles.textContent}>
        {typeof item.content === 'string' ? item.content : JSON.stringify(item.content)}
      </Typography>
    );
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Модерация контента - Админ-панель</title>
        <meta name="description" content="Модерация пользовательского контента" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <AdminLayout>
        <Box className={styles.container}>
          {/* Заголовок */}
          <Box className={styles.header}>
            <Box>
              <Typography variant="h4" component="h1" className={styles.title}>
                <SecurityIcon className={styles.titleIcon} />
                Модерация контента
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Всего элементов: {moderationData?.total || 0}
              </Typography>
            </Box>
            <Box className={styles.headerActions}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => refetch()}
                className={styles.actionButton}
              >
                Обновить
              </Button>
            </Box>
          </Box>

          {/* Статистика */}
          <Grid container spacing={3} className={styles.statsGrid}>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Ожидают модерации
                  </Typography>
                  <Typography variant="h4" color="warning.main">
                    {moderationData?.items.filter(item => item.status === 'pending').length || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Высокий приоритет
                  </Typography>
                  <Typography variant="h4" color="error.main">
                    {moderationData?.items.filter(item => item.priority === 'high').length || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Одобрено сегодня
                  </Typography>
                  <Typography variant="h4" color="success.main">
                    {moderationData?.items.filter(item => 
                      item.status === 'approved' && 
                      new Date(item.reviewedAt || '').toDateString() === new Date().toDateString()
                    ).length || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Отклонено сегодня
                  </Typography>
                  <Typography variant="h4" color="error.main">
                    {moderationData?.items.filter(item => 
                      item.status === 'rejected' && 
                      new Date(item.reviewedAt || '').toDateString() === new Date().toDateString()
                    ).length || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Фильтры */}
          <Card className={styles.filtersCard}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Тип контента</InputLabel>
                    <Select
                      value={filters.type || ''}
                      onChange={(e) => handleFilterChange('type', e.target.value || undefined)}
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="photo">Фотографии</MenuItem>
                      <MenuItem value="profile">Профили</MenuItem>
                      <MenuItem value="message">Сообщения</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Статус</InputLabel>
                    <Select
                      value={filters.status || ''}
                      onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="pending">Ожидает</MenuItem>
                      <MenuItem value="approved">Одобрено</MenuItem>
                      <MenuItem value="rejected">Отклонено</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Приоритет</InputLabel>
                    <Select
                      value={filters.priority || ''}
                      onChange={(e) => handleFilterChange('priority', e.target.value || undefined)}
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="low">Низкий</MenuItem>
                      <MenuItem value="medium">Средний</MenuItem>
                      <MenuItem value="high">Высокий</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Сортировка</InputLabel>
                    <Select
                      value={filters.sortBy || 'submittedAt'}
                      onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    >
                      <MenuItem value="submittedAt">По дате подачи</MenuItem>
                      <MenuItem value="priority">По приоритету</MenuItem>
                      <MenuItem value="autoModerationScore">По оценке AI</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Таблица элементов модерации */}
          <Card className={styles.moderationCard}>
            {isLoading && <LinearProgress />}
            
            {error && (
              <Alert severity="error" className={styles.errorAlert}>
                Ошибка загрузки элементов модерации: {error.message}
              </Alert>
            )}

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Контент</TableCell>
                    <TableCell>Пользователь</TableCell>
                    <TableCell>Тип</TableCell>
                    <TableCell>Приоритет</TableCell>
                    <TableCell>Статус</TableCell>
                    <TableCell>AI Оценка</TableCell>
                    <TableCell>Дата подачи</TableCell>
                    <TableCell>Действия</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {moderationData?.items.map((item) => (
                    <TableRow key={item.id} className={styles.moderationRow}>
                      <TableCell>
                        <Box className={styles.contentCell}>
                          {getTypeIcon(item.type)}
                          <Box className={styles.contentPreview}>
                            {item.type === 'photo' ? (
                              <img
                                src={Array.isArray(item.content) ? item.content[0] : item.content}
                                alt="Превью"
                                className={styles.thumbnailImage}
                              />
                            ) : (
                              <Typography variant="body2" noWrap>
                                {typeof item.content === 'string' 
                                  ? item.content.substring(0, 50) + '...'
                                  : 'Контент для модерации'
                                }
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {item.userName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {item.userId}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={item.type === 'photo' ? 'Фото' : item.type === 'profile' ? 'Профиль' : 'Сообщение'}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>{getPriorityChip(item.priority)}</TableCell>
                      <TableCell>{getStatusChip(item.status)}</TableCell>
                      <TableCell>
                        {item.autoModerationScore ? (
                          <Chip
                            label={item.autoModerationScore.toFixed(2)}
                            color={item.autoModerationScore > 0.7 ? 'error' : item.autoModerationScore > 0.4 ? 'warning' : 'success'}
                            size="small"
                          />
                        ) : (
                          'N/A'
                        )}
                      </TableCell>
                      <TableCell>{formatDate(item.submittedAt)}</TableCell>
                      <TableCell>
                        <Box className={styles.actionButtons}>
                          <Tooltip title="Просмотр">
                            <IconButton
                              size="small"
                              onClick={() => setPreviewDialog({ open: true, item })}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          
                          {item.status === 'pending' && (
                            <>
                              <Tooltip title="Одобрить">
                                <IconButton
                                  size="small"
                                  onClick={() => handleAction('approve', item)}
                                  color="success"
                                >
                                  <ApproveIcon />
                                </IconButton>
                              </Tooltip>
                              
                              <Tooltip title="Отклонить">
                                <IconButton
                                  size="small"
                                  onClick={() => handleAction('reject', item)}
                                  color="error"
                                >
                                  <RejectIcon />
                                </IconButton>
                              </Tooltip>
                            </>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            {moderationData && (
              <TablePagination
                component="div"
                count={moderationData.total}
                page={page}
                onPageChange={(_, newPage) => setPage(newPage)}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={(e) => {
                  setRowsPerPage(parseInt(e.target.value, 10));
                  setPage(0);
                }}
                rowsPerPageOptions={[10, 20, 50]}
                labelRowsPerPage="Элементов на странице:"
                labelDisplayedRows={({ from, to, count }) => 
                  `${from}-${to} из ${count !== -1 ? count : `более ${to}`}`
                }
              />
            )}
          </Card>

          {/* Диалог действий */}
          <Dialog
            open={actionDialog.open}
            onClose={() => setActionDialog({ open: false, type: 'approve', item: null })}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              {actionDialog.type === 'approve' ? 'Одобрить контент' : 'Отклонить контент'}
            </DialogTitle>
            <DialogContent>
              {actionDialog.item && (
                <Box>
                  <Typography variant="body1" gutterBottom>
                    {actionDialog.type === 'approve' 
                      ? 'Вы уверены, что хотите одобрить этот контент?'
                      : 'Вы уверены, что хотите отклонить этот контент?'
                    }
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Пользователь: {actionDialog.item.userName}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Тип: {actionDialog.item.type}
                  </Typography>

                  {actionDialog.type === 'reject' && (
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="Причина отклонения"
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      margin="normal"
                      required
                    />
                  )}
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setActionDialog({ open: false, type: 'approve', item: null })}
              >
                Отмена
              </Button>
              <Button
                onClick={handleConfirmAction}
                color={actionDialog.type === 'approve' ? 'success' : 'error'}
                variant="contained"
                disabled={
                  (actionDialog.type === 'reject' && !rejectionReason.trim()) ||
                  approveMutation.isPending ||
                  rejectMutation.isPending
                }
              >
                {actionDialog.type === 'approve' ? 'Одобрить' : 'Отклонить'}
              </Button>
            </DialogActions>
          </Dialog>

          {/* Диалог предпросмотра */}
          <Dialog
            open={previewDialog.open}
            onClose={() => setPreviewDialog({ open: false, item: null })}
            maxWidth="md"
            fullWidth
          >
            <DialogTitle>
              Предпросмотр контента
            </DialogTitle>
            <DialogContent>
              {previewDialog.item && (
                <Box className={styles.previewContent}>
                  <Typography variant="h6" gutterBottom>
                    {previewDialog.item.type === 'photo' ? 'Фотография' : 
                     previewDialog.item.type === 'profile' ? 'Профиль' : 'Сообщение'}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Пользователь: {previewDialog.item.userName}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Дата подачи: {formatDate(previewDialog.item.submittedAt)}
                  </Typography>

                  {previewDialog.item.autoModerationScore && (
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Оценка AI: {previewDialog.item.autoModerationScore.toFixed(2)}
                    </Typography>
                  )}

                  <Box className={styles.contentDisplay}>
                    {renderContentPreview(previewDialog.item)}
                  </Box>

                  {previewDialog.item.flags.length > 0 && (
                    <Box className={styles.flagsSection}>
                      <Typography variant="subtitle2" gutterBottom>
                        Флаги:
                      </Typography>
                      <Box className={styles.flagsList}>
                        {previewDialog.item.flags.map((flag, index) => (
                          <Chip key={index} label={flag} size="small" color="warning" />
                        ))}
                      </Box>
                    </Box>
                  )}
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setPreviewDialog({ open: false, item: null })}>
                Закрыть
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </AdminLayout>
    </>
  );
};

export default ModerationPage;
