import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  TextInput,
  Alert,
  Modal,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface GroupChatScreenProps {
  navigation: any;
  route: any;
}

interface GroupMember {
  id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  isOnline: boolean;
  isAdmin: boolean;
  joinedAt: string;
  lastSeen?: string;
}

interface GroupMessage {
  id: string;
  senderId: string;
  senderName: string;
  senderAvatar: string;
  text: string;
  timestamp: string;
  messageType: 'text' | 'image' | 'system';
  isRead: boolean;
  reactions?: { emoji: string; count: number; userIds: string[] }[];
}

interface GroupInfo {
  id: string;
  name: string;
  description: string;
  avatar: string;
  createdAt: string;
  createdBy: string;
  membersCount: number;
  isPrivate: boolean;
  settings: {
    allowMembersToInvite: boolean;
    allowMembersToEditInfo: boolean;
    muteNotifications: boolean;
  };
}

const GroupChatScreen: React.FC<GroupChatScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { 
    getGroupInfo, 
    getGroupMembers, 
    getGroupMessages, 
    sendGroupMessage,
    addGroupMember,
    removeGroupMember,
    updateGroupInfo,
    leaveGroup 
  } = useAuth();
  
  const { groupId } = route.params;

  const [groupInfo, setGroupInfo] = useState<GroupInfo | null>(null);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [messages, setMessages] = useState<GroupMessage[]>([]);
  const [messageText, setMessageText] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [showMembersModal, setShowMembersModal] = useState(false);
  const [showGroupInfoModal, setShowGroupInfoModal] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    loadGroupData();
  }, [groupId]);

  const loadGroupData = async () => {
    setIsLoading(true);
    try {
      const [infoResult, membersResult, messagesResult] = await Promise.all([
        getGroupInfo(groupId),
        getGroupMembers(groupId),
        getGroupMessages(groupId),
      ]);

      if (infoResult.success) {
        setGroupInfo(infoResult.groupInfo);
      }

      if (membersResult.success) {
        setMembers(membersResult.members || []);
      }

      if (messagesResult.success) {
        setMessages(messagesResult.messages || []);
      }
    } catch (error) {
      console.error('Error loading group data:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить данные группы');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!messageText.trim()) return;

    const tempMessage: GroupMessage = {
      id: Date.now().toString(),
      senderId: 'current_user_id',
      senderName: 'Вы',
      senderAvatar: '',
      text: messageText.trim(),
      timestamp: new Date().toISOString(),
      messageType: 'text',
      isRead: false,
    };

    setMessages(prev => [...prev, tempMessage]);
    setMessageText('');

    try {
      const result = await sendGroupMessage(groupId, messageText.trim());
      if (result.success) {
        // Обновляем сообщение с реальными данными
        setMessages(prev =>
          prev.map(msg =>
            msg.id === tempMessage.id ? { ...msg, id: result.messageId } : msg
          )
        );
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отправить сообщение');
      // Удаляем временное сообщение при ошибке
      setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));
    }
  };

  const handleAddMember = () => {
    navigation.navigate('AddGroupMemberScreen', { groupId });
  };

  const handleRemoveMember = async (memberId: string) => {
    Alert.alert(
      'Удалить участника',
      'Вы уверены, что хотите удалить этого участника из группы?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await removeGroupMember(groupId, memberId);
              if (result.success) {
                setMembers(prev => prev.filter(m => m.id !== memberId));
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось удалить участника');
            }
          },
        },
      ]
    );
  };

  const handleLeaveGroup = () => {
    Alert.alert(
      'Покинуть группу',
      'Вы уверены, что хотите покинуть эту группу?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Покинуть',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await leaveGroup(groupId);
              if (result.success) {
                navigation.goBack();
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось покинуть группу');
            }
          },
        },
      ]
    );
  };

  const formatMessageTime = (timestamp: string) => {
    const messageTime = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Только что';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} мин`;
    } else if (diffInMinutes < 24 * 60) {
      const diffInHours = Math.floor(diffInMinutes / 60);
      return `${diffInHours} ч`;
    } else {
      return messageTime.toLocaleDateString();
    }
  };

  const renderMessage = ({ item, index }: { item: GroupMessage; index: number }) => {
    const isCurrentUser = item.senderId === 'current_user_id';
    const showAvatar = !isCurrentUser && (index === 0 || messages[index - 1].senderId !== item.senderId);

    if (item.messageType === 'system') {
      return (
        <View style={styles.systemMessage}>
          <Text style={styles.systemMessageText}>{item.text}</Text>
        </View>
      );
    }

    return (
      <View style={[styles.messageContainer, isCurrentUser && styles.currentUserMessage]}>
        {showAvatar && !isCurrentUser && (
          <Image source={{ uri: item.senderAvatar }} style={styles.messageAvatar} />
        )}
        
        <View style={[styles.messageBubble, isCurrentUser && styles.currentUserBubble]}>
          {!isCurrentUser && showAvatar && (
            <Text style={styles.senderName}>{item.senderName}</Text>
          )}
          
          <Text style={[styles.messageText, isCurrentUser && styles.currentUserText]}>
            {item.text}
          </Text>
          
          <Text style={[styles.messageTime, isCurrentUser && styles.currentUserTime]}>
            {formatMessageTime(item.timestamp)}
          </Text>

          {/* Реакции */}
          {item.reactions && item.reactions.length > 0 && (
            <View style={styles.reactionsContainer}>
              {item.reactions.map((reaction, idx) => (
                <View key={idx} style={styles.reactionBubble}>
                  <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>
                  <Text style={styles.reactionCount}>{reaction.count}</Text>
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderMembersModal = () => (
    <Modal
      visible={showMembersModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowMembersModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Участники ({members.length})</Text>
            <TouchableOpacity onPress={() => setShowMembersModal(false)}>
              <Icon name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <FlatList
            data={members}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={styles.memberItem}>
                <Image source={{ uri: item.avatar }} style={styles.memberAvatar} />
                
                <View style={styles.memberInfo}>
                  <View style={styles.memberNameContainer}>
                    <Text style={styles.memberName}>
                      {item.firstName} {item.lastName}
                    </Text>
                    {item.isAdmin && (
                      <View style={styles.adminBadge}>
                        <Text style={styles.adminBadgeText}>Админ</Text>
                      </View>
                    )}
                  </View>
                  
                  <Text style={styles.memberStatus}>
                    {item.isOnline ? 'В сети' : `Был(а) в сети ${formatMessageTime(item.lastSeen || '')}`}
                  </Text>
                </View>

                <TouchableOpacity
                  style={styles.memberAction}
                  onPress={() => handleRemoveMember(item.id)}
                >
                  <Icon name="more-vert" size={20} color="#666" />
                </TouchableOpacity>
              </View>
            )}
            style={styles.membersList}
          />

          <TouchableOpacity style={styles.addMemberButton} onPress={handleAddMember}>
            <Icon name="person-add" size={20} color="#FFFFFF" />
            <Text style={styles.addMemberText}>Добавить участника</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const renderGroupInfoModal = () => (
    <Modal
      visible={showGroupInfoModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowGroupInfoModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Информация о группе</Text>
            <TouchableOpacity onPress={() => setShowGroupInfoModal(false)}>
              <Icon name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.groupInfoContent}>
            {groupInfo && (
              <>
                <View style={styles.groupInfoSection}>
                  <Image source={{ uri: groupInfo.avatar }} style={styles.groupInfoAvatar} />
                  <Text style={styles.groupInfoName}>{groupInfo.name}</Text>
                  <Text style={styles.groupInfoDescription}>{groupInfo.description}</Text>
                </View>

                <View style={styles.groupInfoSection}>
                  <Text style={styles.groupInfoSectionTitle}>Настройки</Text>
                  
                  <View style={styles.groupInfoItem}>
                    <Icon name="group" size={20} color="#666" />
                    <Text style={styles.groupInfoItemText}>
                      {groupInfo.membersCount} участников
                    </Text>
                  </View>

                  <View style={styles.groupInfoItem}>
                    <Icon name="lock" size={20} color="#666" />
                    <Text style={styles.groupInfoItemText}>
                      {groupInfo.isPrivate ? 'Приватная группа' : 'Публичная группа'}
                    </Text>
                  </View>

                  <View style={styles.groupInfoItem}>
                    <Icon name="schedule" size={20} color="#666" />
                    <Text style={styles.groupInfoItemText}>
                      Создана {new Date(groupInfo.createdAt).toLocaleDateString()}
                    </Text>
                  </View>
                </View>

                <TouchableOpacity style={styles.leaveGroupButton} onPress={handleLeaveGroup}>
                  <Icon name="exit-to-app" size={20} color="#F44336" />
                  <Text style={styles.leaveGroupText}>Покинуть группу</Text>
                </TouchableOpacity>
              </>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF6B9D" />
        <Text style={styles.loadingText}>Загрузка группы...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.groupInfoButton}
            onPress={() => setShowGroupInfoModal(true)}
          >
            <View style={styles.groupHeaderInfo}>
              <Text style={styles.groupName} numberOfLines={1}>
                {groupInfo?.name || 'Группа'}
              </Text>
              <Text style={styles.groupMembersCount}>
                {members.length} участников
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.membersButton}
            onPress={() => setShowMembersModal(true)}
          >
            <Icon name="group" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Сообщения */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
        />

        {/* Индикатор печати */}
        {isTyping && (
          <View style={styles.typingIndicator}>
            <Text style={styles.typingText}>Кто-то печатает...</Text>
          </View>
        )}

        {/* Поле ввода */}
        <View style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.textInput}
              placeholder="Написать сообщение..."
              placeholderTextColor="rgba(255, 255, 255, 0.7)"
              value={messageText}
              onChangeText={setMessageText}
              multiline
              maxLength={1000}
            />
            
            <TouchableOpacity
              style={styles.attachButton}
              onPress={() => {/* Открыть меню вложений */}}
            >
              <Icon name="attach-file" size={20} color="rgba(255, 255, 255, 0.8)" />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.sendButton, !messageText.trim() && styles.sendButtonDisabled]}
            onPress={handleSendMessage}
            disabled={!messageText.trim()}
          >
            <Icon name="send" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Модальные окна */}
      {renderMembersModal()}
      {renderGroupInfoModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 15,
  },
  backButton: {
    padding: 8,
  },
  groupInfoButton: {
    flex: 1,
    alignItems: 'center',
  },
  groupHeaderInfo: {
    alignItems: 'center',
  },
  groupName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    maxWidth: width * 0.5,
  },
  groupMembersCount: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  membersButton: {
    padding: 8,
  },
  messagesList: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    marginHorizontal: 10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  messagesContainer: {
    padding: 15,
    paddingBottom: 20,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'flex-end',
  },
  currentUserMessage: {
    justifyContent: 'flex-end',
  },
  messageAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
  },
  messageBubble: {
    backgroundColor: '#F0F0F0',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 8,
    maxWidth: width * 0.7,
  },
  currentUserBubble: {
    backgroundColor: '#FF6B9D',
    alignSelf: 'flex-end',
  },
  senderName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#4ECDC4',
    marginBottom: 2,
  },
  messageText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 20,
  },
  currentUserText: {
    color: '#FFFFFF',
  },
  messageTime: {
    fontSize: 11,
    color: '#999',
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  currentUserTime: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  systemMessage: {
    alignItems: 'center',
    marginVertical: 10,
  },
  systemMessageText: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 10,
  },
  reactionsContainer: {
    flexDirection: 'row',
    marginTop: 4,
  },
  reactionBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 4,
  },
  reactionEmoji: {
    fontSize: 12,
  },
  reactionCount: {
    fontSize: 10,
    marginLeft: 2,
    color: '#666',
  },
  typingIndicator: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    marginHorizontal: 10,
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  typingText: {
    fontSize: 12,
    color: '#4ECDC4',
    fontStyle: 'italic',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
    maxHeight: 100,
    minHeight: 20,
  },
  attachButton: {
    padding: 4,
    marginLeft: 8,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4ECDC4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  membersList: {
    maxHeight: height * 0.5,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  memberInfo: {
    flex: 1,
  },
  memberNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  memberName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  adminBadge: {
    backgroundColor: '#4ECDC4',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  adminBadgeText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  memberStatus: {
    fontSize: 12,
    color: '#666',
  },
  memberAction: {
    padding: 8,
  },
  addMemberButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4ECDC4',
    margin: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  addMemberText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  groupInfoContent: {
    maxHeight: height * 0.6,
  },
  groupInfoSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  groupInfoAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignSelf: 'center',
    marginBottom: 15,
  },
  groupInfoName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  groupInfoDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  groupInfoSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  groupInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  groupInfoItemText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 12,
  },
  leaveGroupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 20,
    paddingVertical: 12,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: '#F44336',
  },
  leaveGroupText: {
    color: '#F44336',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default GroupChatScreen;
