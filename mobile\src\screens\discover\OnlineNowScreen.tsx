import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import { discoverService } from '../../services/discoverService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { OnlineUser } from '../../types/discover.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface OnlineNowScreenProps {}

const OnlineNowScreen: React.FC<OnlineNowScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location } = useLocation();
  const queryClient = useQueryClient();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'nearby' | 'new'>('all');

  // Загрузка пользователей онлайн
  const {
    data: onlineUsers,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['discover', 'online', selectedFilter, location],
    queryFn: () => discoverService.getOnlineUsers(selectedFilter, location),
    refetchInterval: 30000, // Обновляем каждые 30 секунд
  });

  // Мутация для лайка
  const likeMutation = useMutation({
    mutationFn: ({ userId, action }: { userId: string; action: 'like' | 'superlike' }) =>
      discoverService.reactToUser(userId, action),
    onSuccess: (data, variables) => {
      if (data.isMatch) {
        Alert.alert(
          'Это матч! 💕',
          'Вы понравились друг другу!',
          [
            { text: 'Позже', style: 'cancel' },
            { text: 'Написать', onPress: () => navigation.navigate('Chat', { userId: variables.userId }) },
          ]
        );
      }
      queryClient.invalidateQueries({ queryKey: ['matches'] });
    },
  });

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleLike = (userId: string) => {
    likeMutation.mutate({ userId, action: 'like' });
  };

  const handleSuperLike = (userId: string) => {
    likeMutation.mutate({ userId, action: 'superlike' });
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}м`;
    }
    return `${distance.toFixed(1)}км`;
  };

  const formatOnlineTime = (onlineTime: string) => {
    const now = new Date();
    const onlineDate = new Date(onlineTime);
    const diffInMinutes = Math.floor((now.getTime() - onlineDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Только что';
    if (diffInMinutes < 5) return 'Несколько минут назад';
    if (diffInMinutes < 60) return `${diffInMinutes} мин назад`;
    return 'Давно онлайн';
  };

  const renderFilterButton = (filter: typeof selectedFilter, label: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        selectedFilter === filter && styles.filterButtonActive,
      ]}
      onPress={() => setSelectedFilter(filter)}
    >
      <Text
        style={[
          styles.filterButtonText,
          selectedFilter === filter && styles.filterButtonTextActive,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderUserCard = ({ item, index }: { item: OnlineUser; index: number }) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateY: interpolate(animatedValue.value, [0, 1], [50, 0]),
          },
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.9, 1]),
          },
        ],
      };
    });

    return (
      <Animated.View style={[styles.userCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('UserProfile', { userId: item.id })}
          activeOpacity={0.8}
        >
          <Image source={{ uri: item.photos[0] }} style={styles.userImage} />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.userImageGradient}
          />
          
          {/* Индикатор онлайн */}
          <View style={styles.onlineIndicator}>
            <View style={styles.onlineDot} />
            <Text style={styles.onlineText}>Онлайн</Text>
          </View>
          
          {/* Верификация */}
          {item.isVerified && (
            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
            </View>
          )}
          
          <View style={styles.userInfo}>
            <View style={styles.userMainInfo}>
              <Text style={styles.userName}>
                {item.firstName}, {item.age}
              </Text>
            </View>
            
            <View style={styles.userDetails}>
              <Text style={styles.userDistance}>
                <Ionicons name="location-outline" size={14} color={colors.textSecondary} />
                {' '}{formatDistance(item.distance)}
              </Text>
              
              <Text style={styles.onlineTime}>
                {formatOnlineTime(item.lastOnline)}
              </Text>
            </View>
            
            {item.bio && (
              <Text style={styles.userBio} numberOfLines={2}>
                {item.bio}
              </Text>
            )}
            
            {item.interests.length > 0 && (
              <View style={styles.interestsContainer}>
                {item.interests.slice(0, 2).map((interest, idx) => (
                  <View key={idx} style={styles.interestTag}>
                    <Text style={styles.interestText}>{interest}</Text>
                  </View>
                ))}
                {item.interests.length > 2 && (
                  <Text style={styles.moreInterests}>+{item.interests.length - 2}</Text>
                )}
              </View>
            )}
          </View>
        </TouchableOpacity>
        
        <View style={styles.userActions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.superLikeButton]}
            onPress={() => handleSuperLike(item.id)}
            disabled={likeMutation.isPending}
          >
            <Ionicons name="star" size={20} color={colors.warning} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.likeButton]}
            onPress={() => handleLike(item.id)}
            disabled={likeMutation.isPending}
          >
            <Ionicons name="heart" size={20} color={colors.success} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.chatButton]}
            onPress={() => navigation.navigate('Chat', { userId: item.id })}
          >
            <Ionicons name="chatbubble" size={18} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  if (isLoading && !onlineUsers) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Ищем людей онлайн...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="wifi-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка подключения</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить список пользователей онлайн
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Сейчас онлайн</Text>
        
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={() => refetch()}
        >
          <Ionicons name="refresh" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Фильтры */}
      <View style={styles.filtersContainer}>
        {renderFilterButton('all', 'Все')}
        {renderFilterButton('nearby', 'Рядом')}
        {renderFilterButton('new', 'Новые')}
      </View>

      {/* Счетчик онлайн */}
      {onlineUsers && onlineUsers.length > 0 && (
        <View style={styles.counterContainer}>
          <View style={styles.onlineCounter}>
            <View style={styles.onlineCounterDot} />
            <Text style={styles.onlineCounterText}>
              {onlineUsers.length} {onlineUsers.length === 1 ? 'человек' : 'людей'} онлайн
            </Text>
          </View>
        </View>
      )}

      {/* Список пользователей */}
      <FlatList
        data={onlineUsers}
        renderItem={renderUserCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        numColumns={2}
        columnWrapperStyle={styles.row}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="people-outline" size={64} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>Никого нет онлайн</Text>
            <Text style={styles.emptyText}>
              Попробуйте зайти позже или измените фильтры
            </Text>
          </View>
        }
      />

      {/* Индикатор загрузки действий */}
      {likeMutation.isPending && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: spacing.sm,
  },
  filterButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  filterButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  filterButtonTextActive: {
    color: colors.white,
  },
  counterContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
  },
  onlineCounter: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  onlineCounterDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.success,
    marginRight: spacing.sm,
  },
  onlineCounterText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  listContainer: {
    padding: spacing.md,
  },
  row: {
    justifyContent: 'space-between',
  },
  userCard: {
    width: (screenWidth - spacing.md * 3) / 2,
    backgroundColor: colors.surface,
    borderRadius: 16,
    marginBottom: spacing.md,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  userImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  userImageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  onlineIndicator: {
    position: 'absolute',
    top: spacing.sm,
    left: spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  onlineDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.success,
    marginRight: spacing.xs,
  },
  onlineText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  verifiedBadge: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 12,
    padding: 2,
  },
  userInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.sm,
  },
  userMainInfo: {
    marginBottom: spacing.xs,
  },
  userName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  userDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  userDistance: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
  },
  onlineTime: {
    fontSize: typography.sizes.xs,
    color: colors.success,
    fontWeight: typography.weights.medium,
  },
  userBio: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    lineHeight: 16,
    marginBottom: spacing.xs,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  interestTag: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
    marginRight: spacing.xs,
    marginBottom: 2,
  },
  interestText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  moreInterests: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium,
  },
  userActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: spacing.sm,
    backgroundColor: colors.surface,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  superLikeButton: {
    backgroundColor: colors.background,
  },
  likeButton: {
    backgroundColor: colors.background,
  },
  chatButton: {
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default OnlineNowScreen;
