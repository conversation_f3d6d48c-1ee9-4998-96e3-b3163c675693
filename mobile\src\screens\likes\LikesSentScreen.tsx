import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');
const CARD_WIDTH = (width - 60) / 2;

interface LikesSentScreenProps {
  navigation: any;
}

interface SentLike {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  age: number;
  city: string;
  mainPhoto: string;
  isOnline: boolean;
  isVerified: boolean;
  isPremium: boolean;
  likedAt: string;
  mutualFriends: number;
  commonInterests: string[];
  distance: number;
  likeType: 'regular' | 'super';
  message?: string;
  hasLikedBack: boolean;
  hasMatched: boolean;
  isActive: boolean; // активен ли пользователь
  lastSeen?: string;
}

const LikesSentScreen: React.FC<LikesSentScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { getSentLikes, unlikeUser, sendSuperLike } = useAuth();

  const [likes, setLikes] = useState<SentLike[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'matched'>('all');

  useEffect(() => {
    loadSentLikes();
  }, [filter]);

  const loadSentLikes = async (pageNum = 1, refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const result = await getSentLikes({
        page: pageNum,
        limit: 20,
        filter,
      });

      if (result.success) {
        const newLikes = result.likes || [];
        
        if (refresh || pageNum === 1) {
          setLikes(newLikes);
        } else {
          setLikes(prev => [...prev, ...newLikes]);
        }

        setHasMore(newLikes.length === 20);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('Error loading sent likes:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = useCallback(() => {
    loadSentLikes(1, true);
  }, [filter]);

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      loadSentLikes(page + 1);
    }
  };

  const handleUserPress = (like: SentLike) => {
    navigation.navigate('UserProfileScreen', { 
      userId: like.userId,
      source: 'sent_likes'
    });
  };

  const handleUnlike = async (like: SentLike) => {
    Alert.alert(
      'Отменить лайк',
      `Вы уверены, что хотите отменить лайк для ${like.firstName}?`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отменить лайк',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await unlikeUser(like.userId);
              if (result.success) {
                // Удаляем лайк из списка
                setLikes(prev => prev.filter(l => l.id !== like.id));
                Alert.alert('Успешно', 'Лайк отменен');
              } else {
                Alert.alert('Ошибка', result.message || 'Не удалось отменить лайк');
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Произошла ошибка при отмене лайка');
            }
          },
        },
      ]
    );
  };

  const handleSendSuperLike = async (like: SentLike) => {
    if (like.likeType === 'super') {
      Alert.alert('Информация', 'Вы уже отправили супер-лайк этому пользователю');
      return;
    }

    Alert.alert(
      'Супер-лайк',
      `Отправить супер-лайк пользователю ${like.firstName}? Это повысит ваши шансы на взаимность.`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отправить',
          onPress: async () => {
            try {
              const result = await sendSuperLike(like.userId);
              if (result.success) {
                // Обновляем тип лайка
                setLikes(prev =>
                  prev.map(l =>
                    l.id === like.id
                      ? { ...l, likeType: 'super' }
                      : l
                  )
                );
                Alert.alert('Успешно', 'Супер-лайк отправлен!');
              } else {
                Alert.alert('Ошибка', result.message || 'Не удалось отправить супер-лайк');
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Произошла ошибка при отправке супер-лайка');
            }
          },
        },
      ]
    );
  };

  const formatLikeTime = (likedAt: string) => {
    const now = new Date();
    const liked = new Date(likedAt);
    const diffInDays = Math.floor((now.getTime() - liked.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays < 1) {
      return 'Сегодня';
    } else if (diffInDays === 1) {
      return 'Вчера';
    } else if (diffInDays < 7) {
      return `${diffInDays} дн. назад`;
    } else {
      const diffInWeeks = Math.floor(diffInDays / 7);
      return `${diffInWeeks} нед. назад`;
    }
  };

  const getStatusText = (like: SentLike) => {
    if (like.hasMatched) {
      return 'Взаимная симпатия';
    } else if (like.hasLikedBack) {
      return 'Лайк получен';
    } else {
      return 'Ожидает ответа';
    }
  };

  const getStatusColor = (like: SentLike) => {
    if (like.hasMatched) {
      return '#4CAF50';
    } else if (like.hasLikedBack) {
      return '#FF6B9D';
    } else {
      return '#FF9800';
    }
  };

  const renderLike = ({ item }: { item: SentLike }) => (
    <TouchableOpacity
      style={styles.likeCard}
      onPress={() => handleUserPress(item)}
    >
      <View style={styles.imageContainer}>
        <Image source={{ uri: item.mainPhoto }} style={styles.userImage} />
        
        {/* Онлайн статус */}
        {item.isOnline && (
          <View style={styles.onlineIndicator} />
        )}

        {/* Верификация */}
        {item.isVerified && (
          <View style={styles.verifiedBadge}>
            <Icon name="verified" size={14} color="#4CAF50" />
          </View>
        )}

        {/* Премиум */}
        {item.isPremium && (
          <View style={styles.premiumBadge}>
            <Icon name="star" size={14} color="#FFD700" />
          </View>
        )}

        {/* Тип лайка */}
        {item.likeType === 'super' && (
          <View style={styles.superLikeBadge}>
            <Icon name="star" size={16} color="#FFFFFF" />
          </View>
        )}

        {/* Статус ответа */}
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item) }]}>
          <Icon 
            name={
              item.hasMatched ? 'favorite' : 
              item.hasLikedBack ? 'thumb-up' : 
              'schedule'
            } 
            size={12} 
            color="#FFFFFF" 
          />
        </View>

        {/* Градиент снизу */}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.imageGradient}
        />

        {/* Информация о пользователе */}
        <View style={styles.userInfo}>
          <Text style={styles.userName}>
            {item.firstName}, {item.age}
          </Text>
          <View style={styles.userLocation}>
            <Icon name="location-on" size={12} color="#FFFFFF" />
            <Text style={styles.locationText}>{item.distance} км</Text>
          </View>
        </View>
      </View>

      {/* Дополнительная информация */}
      <View style={styles.cardContent}>
        <View style={styles.statusContainer}>
          <Text style={[styles.statusText, { color: getStatusColor(item) }]}>
            {getStatusText(item)}
          </Text>
          <Text style={styles.likeTime}>{formatLikeTime(item.likedAt)}</Text>
        </View>
        
        {/* Общие интересы */}
        {item.commonInterests.length > 0 && (
          <View style={styles.commonInterests}>
            <Icon name="favorite" size={12} color="#FF6B9D" />
            <Text style={styles.commonInterestsText}>
              {item.commonInterests.length} общих
            </Text>
          </View>
        )}

        {/* Сообщение с лайком */}
        {item.message && (
          <Text style={styles.likeMessage} numberOfLines={2}>
            "{item.message}"
          </Text>
        )}
      </View>

      {/* Действия */}
      <View style={styles.cardActions}>
        {item.hasMatched ? (
          <TouchableOpacity
            style={styles.chatButton}
            onPress={() => navigation.navigate('ChatScreen', { userId: item.userId })}
          >
            <Icon name="chat" size={16} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>Написать</Text>
          </TouchableOpacity>
        ) : (
          <>
            <TouchableOpacity
              style={styles.unlikeButton}
              onPress={() => handleUnlike(item)}
            >
              <Icon name="close" size={16} color="#666" />
            </TouchableOpacity>

            {item.likeType !== 'super' && (
              <TouchableOpacity
                style={styles.superLikeButton}
                onPress={() => handleSendSuperLike(item)}
              >
                <Icon name="star" size={16} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="favorite-border" size={80} color="rgba(255, 255, 255, 0.5)" />
      <Text style={styles.emptyTitle}>Вы еще никому не ставили лайки</Text>
      <Text style={styles.emptySubtitle}>
        Начните ставить лайки понравившимся людям, и они появятся здесь
      </Text>
      <TouchableOpacity
        style={styles.startLikingButton}
        onPress={() => navigation.navigate('SearchScreen')}
      >
        <Text style={styles.startLikingText}>Начать знакомства</Text>
      </TouchableOpacity>
    </View>
  );

  const renderFooter = () => {
    if (!isLoading || isRefreshing) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator color="#FFFFFF" size="small" />
      </View>
    );
  };

  const renderFilterTabs = () => (
    <View style={styles.filterContainer}>
      {[
        { key: 'all', label: 'Все', count: likes.length },
        { key: 'pending', label: 'Ожидают', count: likes.filter(l => !l.hasLikedBack && !l.hasMatched).length },
        { key: 'matched', label: 'Матчи', count: likes.filter(l => l.hasMatched).length },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.filterTab,
            filter === tab.key && styles.filterTabActive,
          ]}
          onPress={() => setFilter(tab.key as any)}
        >
          <Text
            style={[
              styles.filterTabText,
              filter === tab.key && styles.filterTabTextActive,
            ]}
          >
            {tab.label}
          </Text>
          {tab.count > 0 && (
            <View style={styles.filterTabBadge}>
              <Text style={styles.filterTabBadgeText}>{tab.count}</Text>
            </View>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Мои лайки</Text>
          <TouchableOpacity
            style={styles.statsButton}
            onPress={() => navigation.navigate('LikesStatsScreen')}
          >
            <Icon name="bar-chart" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Фильтры */}
        {renderFilterTabs()}

        {/* Список лайков */}
        <FlatList
          data={likes}
          renderItem={renderLike}
          keyExtractor={(item) => item.id}
          numColumns={2}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
              colors={['#FFFFFF']}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={!isLoading ? renderEmptyState : null}
        />
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  statsButton: {
    padding: 8,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  filterTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginHorizontal: 5,
  },
  filterTabActive: {
    backgroundColor: '#FFFFFF',
  },
  filterTabText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    fontWeight: '600',
  },
  filterTabTextActive: {
    color: '#FF6B9D',
  },
  filterTabBadge: {
    backgroundColor: '#FF6B9D',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 6,
  },
  filterTabBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 20,
  },
  likeCard: {
    width: CARD_WIDTH,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    margin: 5,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  imageContainer: {
    position: 'relative',
    height: CARD_WIDTH * 1.2,
  },
  userImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  onlineIndicator: {
    position: 'absolute',
    top: 8,
    left: 8,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  verifiedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    padding: 2,
  },
  premiumBadge: {
    position: 'absolute',
    top: 30,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    padding: 2,
  },
  superLikeBadge: {
    position: 'absolute',
    top: 8,
    left: 30,
    backgroundColor: '#4ECDC4',
    borderRadius: 12,
    padding: 4,
  },
  statusBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    borderRadius: 10,
    padding: 4,
  },
  imageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  userInfo: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 40,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  userLocation: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 12,
    color: '#FFFFFF',
    marginLeft: 2,
  },
  cardContent: {
    padding: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  likeTime: {
    fontSize: 11,
    color: '#666',
  },
  commonInterests: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  commonInterestsText: {
    fontSize: 11,
    color: '#FF6B9D',
    marginLeft: 4,
  },
  likeMessage: {
    fontSize: 12,
    color: '#333',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 12,
    paddingBottom: 12,
  },
  unlikeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  superLikeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#4ECDC4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  startLikingButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
  },
  startLikingText: {
    color: '#FF6B9D',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

export default LikesSentScreen;
