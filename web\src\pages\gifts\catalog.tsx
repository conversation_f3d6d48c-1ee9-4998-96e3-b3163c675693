import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  TextField,
  MenuItem,
  InputAdornment,
  Pagination,
  Skeleton,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Avatar,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search,
  FilterList,
  Favorite,
  Diamond,
  Star,
  LocalOffer,
  Send,
  Close,
  ShoppingCart,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { giftsService } from '../../services/giftsService';
import { Gift, GiftCategory, SendGiftData } from '../../types/gifts.types';
import { User } from '../../types/user.types';
import styles from './catalog.module.css';

interface GiftCatalogProps {}

const GiftCatalog: React.FC<GiftCatalogProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState({
    category: 'all',
    priceRange: 'all',
    search: '',
    sortBy: 'popular',
  });
  const [page, setPage] = useState(1);
  const [selectedGift, setSelectedGift] = useState<Gift | null>(null);
  const [sendGiftDialogOpen, setSendGiftDialogOpen] = useState(false);
  const [selectedRecipient, setSelectedRecipient] = useState<User | null>(null);

  // Загрузка подарков
  const {
    data: giftsData,
    isLoading: giftsLoading,
    error: giftsError,
  } = useQuery({
    queryKey: ['gifts', 'catalog', filters, page],
    queryFn: () => giftsService.getGifts({ ...filters, page, limit: 20 }),
  });

  // Загрузка категорий
  const {
    data: categories,
  } = useQuery({
    queryKey: ['gifts', 'categories'],
    queryFn: () => giftsService.getCategories(),
  });

  // Загрузка пользователей для отправки подарка
  const {
    data: users,
  } = useQuery({
    queryKey: ['users', 'for-gifts'],
    queryFn: () => giftsService.getUsersForGifts(),
    enabled: sendGiftDialogOpen,
  });

  // Мутация для отправки подарка
  const sendGiftMutation = useMutation({
    mutationFn: (data: SendGiftData) => giftsService.sendGift(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gifts', 'sent'] });
      setSendGiftDialogOpen(false);
      setSelectedGift(null);
      setSelectedRecipient(null);
    },
  });

  // Мутация для добавления в избранное
  const toggleFavoriteMutation = useMutation({
    mutationFn: (giftId: string) => giftsService.toggleFavorite(giftId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gifts'] });
    },
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/gifts/catalog');
    }
  }, [isAuthenticated, router]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleSendGift = (gift: Gift) => {
    setSelectedGift(gift);
    setSendGiftDialogOpen(true);
  };

  const handleConfirmSendGift = () => {
    if (selectedGift && selectedRecipient) {
      sendGiftMutation.mutate({
        giftId: selectedGift.id,
        recipientId: selectedRecipient.id,
        message: '',
      });
    }
  };

  const priceRangeOptions = [
    { value: 'all', label: 'Все цены' },
    { value: '1-50', label: '1-50 💎' },
    { value: '51-100', label: '51-100 💎' },
    { value: '101-500', label: '101-500 💎' },
    { value: '500+', label: '500+ 💎' },
  ];

  const sortOptions = [
    { value: 'popular', label: 'Популярные' },
    { value: 'price_asc', label: 'Цена: по возрастанию' },
    { value: 'price_desc', label: 'Цена: по убыванию' },
    { value: 'newest', label: 'Новые' },
  ];

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Каталог подарков - Likes & Love</title>
        <meta name="description" content="Выберите идеальный подарок для особенного человека. Большой каталог виртуальных подарков." />
        <meta name="keywords" content="подарки, виртуальные подарки, знакомства, романтика" />
        <meta property="og:title" content="Каталог подарков - Likes & Love" />
        <meta property="og:description" content="Выберите идеальный подарок для особенного человека" />
        <meta property="og:type" content="website" />
      </Head>

      <Container maxWidth="xl" className={styles.container}>
        {/* Заголовок */}
        <Box className={styles.header}>
          <Typography variant="h3" component="h1" className={styles.title}>
            Каталог подарков
          </Typography>
          <Typography variant="h6" color="text.secondary" className={styles.subtitle}>
            Выберите идеальный подарок для особенного человека
          </Typography>
        </Box>

        {/* Фильтры */}
        <Card className={styles.filtersCard}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  placeholder="Поиск подарков..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                  size="small"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  select
                  fullWidth
                  label="Категория"
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  size="small"
                >
                  <MenuItem value="all">Все категории</MenuItem>
                  {categories?.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  select
                  fullWidth
                  label="Цена"
                  value={filters.priceRange}
                  onChange={(e) => handleFilterChange('priceRange', e.target.value)}
                  size="small"
                >
                  {priceRangeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  select
                  fullWidth
                  label="Сортировка"
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  size="small"
                >
                  {sortOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box className={styles.balanceInfo}>
                  <Diamond sx={{ color: 'warning.main', mr: 1 }} />
                  <Typography variant="body2">
                    Баланс: {user?.balance || 0} 💎
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => router.push('/premium/purchase')}
                    sx={{ ml: 1 }}
                  >
                    Пополнить
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Каталог подарков */}
        {giftsLoading ? (
          <Grid container spacing={3} className={styles.giftsGrid}>
            {Array.from({ length: 12 }).map((_, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Card>
                  <Skeleton variant="rectangular" height={200} />
                  <CardContent>
                    <Skeleton variant="text" height={24} />
                    <Skeleton variant="text" height={20} width="60%" />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : giftsError ? (
          <Alert severity="error" className={styles.errorAlert}>
            Ошибка загрузки подарков. Попробуйте обновить страницу.
          </Alert>
        ) : giftsData?.gifts?.length === 0 ? (
          <Alert severity="info" className={styles.emptyAlert}>
            Подарки не найдены. Попробуйте изменить фильтры поиска.
          </Alert>
        ) : (
          <>
            <Grid container spacing={3} className={styles.giftsGrid}>
              {giftsData?.gifts?.map((gift) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={gift.id}>
                  <Card className={styles.giftCard}>
                    <Box className={styles.giftImageContainer}>
                      <CardMedia
                        component="img"
                        height="200"
                        image={gift.image}
                        alt={gift.name}
                        className={styles.giftImage}
                      />
                      
                      {gift.isPopular && (
                        <Chip
                          icon={<Star />}
                          label="Популярный"
                          color="warning"
                          size="small"
                          className={styles.popularBadge}
                        />
                      )}
                      
                      {gift.isNew && (
                        <Chip
                          label="NEW"
                          color="error"
                          size="small"
                          className={styles.newBadge}
                        />
                      )}

                      <IconButton
                        className={styles.favoriteButton}
                        onClick={() => toggleFavoriteMutation.mutate(gift.id)}
                        color={gift.isFavorite ? 'error' : 'default'}
                      >
                        <Favorite />
                      </IconButton>
                    </Box>

                    <CardContent className={styles.giftContent}>
                      <Typography variant="h6" className={styles.giftName}>
                        {gift.name}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" className={styles.giftDescription}>
                        {gift.description}
                      </Typography>

                      <Box className={styles.giftFooter}>
                        <Box className={styles.giftPrice}>
                          <Diamond sx={{ color: 'warning.main', fontSize: 20 }} />
                          <Typography variant="h6" color="warning.main">
                            {gift.price}
                          </Typography>
                        </Box>

                        <Button
                          variant="contained"
                          startIcon={<Send />}
                          onClick={() => handleSendGift(gift)}
                          disabled={user?.balance < gift.price}
                          className={styles.sendButton}
                        >
                          Подарить
                        </Button>
                      </Box>

                      {gift.category && (
                        <Chip
                          label={gift.category.name}
                          size="small"
                          className={styles.categoryChip}
                        />
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* Пагинация */}
            {giftsData?.totalPages > 1 && (
              <Box className={styles.paginationContainer}>
                <Pagination
                  count={giftsData.totalPages}
                  page={page}
                  onChange={(_, newPage) => setPage(newPage)}
                  color="primary"
                  size="large"
                />
              </Box>
            )}
          </>
        )}

        {/* Диалог отправки подарка */}
        <Dialog
          open={sendGiftDialogOpen}
          onClose={() => setSendGiftDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              Отправить подарок
              <IconButton onClick={() => setSendGiftDialogOpen(false)}>
                <Close />
              </IconButton>
            </Box>
          </DialogTitle>
          
          <DialogContent>
            {selectedGift && (
              <Box className={styles.sendGiftContent}>
                <Box className={styles.selectedGift}>
                  <img
                    src={selectedGift.image}
                    alt={selectedGift.name}
                    className={styles.selectedGiftImage}
                  />
                  <Box>
                    <Typography variant="h6">{selectedGift.name}</Typography>
                    <Box className={styles.giftPrice}>
                      <Diamond sx={{ color: 'warning.main', mr: 0.5 }} />
                      <Typography variant="h6" color="warning.main">
                        {selectedGift.price}
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
                  Выберите получателя:
                </Typography>

                <Grid container spacing={2}>
                  {users?.map((user) => (
                    <Grid item xs={12} sm={6} key={user.id}>
                      <Card
                        className={`${styles.recipientCard} ${
                          selectedRecipient?.id === user.id ? styles.selectedRecipient : ''
                        }`}
                        onClick={() => setSelectedRecipient(user)}
                      >
                        <CardContent className={styles.recipientContent}>
                          <Avatar src={user.avatar} sx={{ width: 48, height: 48 }}>
                            {user.firstName.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="body1" fontWeight="bold">
                              {user.firstName} {user.lastName}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {user.age} лет, {user.location}
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setSendGiftDialogOpen(false)}>
              Отмена
            </Button>
            <Button
              variant="contained"
              onClick={handleConfirmSendGift}
              disabled={!selectedRecipient || sendGiftMutation.isPending}
              startIcon={<Send />}
            >
              {sendGiftMutation.isPending ? 'Отправка...' : 'Отправить подарок'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </>
  );
};

export default GiftCatalog;
