import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface MatchDetailScreenProps {
  navigation: any;
  route: any;
}

interface MatchDetail {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  age: number;
  city: string;
  bio: string;
  photos: string[];
  isOnline: boolean;
  isVerified: boolean;
  isPremium: boolean;
  matchedAt: string;
  mutualFriends: number;
  commonInterests: string[];
  distance: number;
  compatibility: number;
  education: string;
  work: string;
  height: number;
  zodiacSign: string;
  interests: string[];
  lifestyle: {
    smoking: string;
    drinking: string;
    pets: string;
    children: string;
  };
  lastMessage?: {
    text: string;
    timestamp: string;
    isRead: boolean;
    senderId: string;
  };
  unreadCount: number;
  isActive: boolean;
  lastSeen?: string;
  myLikeType: 'regular' | 'super';
  theirLikeType: 'regular' | 'super';
}

const MatchDetailScreen: React.FC<MatchDetailScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { getMatchDetail, unmatchUser, reportUser } = useAuth();
  const { matchId, userId } = route.params;

  const [matchDetail, setMatchDetail] = useState<MatchDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [showPhotoModal, setShowPhotoModal] = useState(false);

  useEffect(() => {
    loadMatchDetail();
  }, [matchId]);

  const loadMatchDetail = async () => {
    setIsLoading(true);
    try {
      const result = await getMatchDetail(matchId);
      if (result.success) {
        setMatchDetail(result.matchDetail);
      } else {
        Alert.alert('Ошибка', 'Не удалось загрузить информацию о матче');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Error loading match detail:', error);
      Alert.alert('Ошибка', 'Произошла ошибка при загрузке данных');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  const handleChatPress = () => {
    if (!matchDetail) return;
    
    navigation.navigate('ChatScreen', { 
      userId: matchDetail.userId,
      userName: `${matchDetail.firstName} ${matchDetail.lastName}`,
      userPhoto: matchDetail.photos[0],
    });
  };

  const handleUnmatch = () => {
    if (!matchDetail) return;

    Alert.alert(
      'Отменить матч',
      `Вы уверены, что хотите отменить матч с ${matchDetail.firstName}? Это действие нельзя отменить.`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отменить матч',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await unmatchUser(matchDetail.userId);
              if (result.success) {
                Alert.alert('Успешно', 'Матч отменен', [
                  { text: 'OK', onPress: () => navigation.goBack() }
                ]);
              } else {
                Alert.alert('Ошибка', result.message || 'Не удалось отменить матч');
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Произошла ошибка при отмене матча');
            }
          },
        },
      ]
    );
  };

  const handleReport = () => {
    if (!matchDetail) return;

    Alert.alert(
      'Пожаловаться',
      'Выберите причину жалобы:',
      [
        { text: 'Отмена', style: 'cancel' },
        { text: 'Спам', onPress: () => submitReport('spam') },
        { text: 'Неподобающее поведение', onPress: () => submitReport('inappropriate') },
        { text: 'Фейковый профиль', onPress: () => submitReport('fake') },
        { text: 'Другое', onPress: () => submitReport('other') },
      ]
    );
  };

  const submitReport = async (reason: string) => {
    if (!matchDetail) return;

    try {
      const result = await reportUser(matchDetail.userId, reason);
      if (result.success) {
        Alert.alert('Спасибо', 'Ваша жалоба отправлена на рассмотрение');
      } else {
        Alert.alert('Ошибка', 'Не удалось отправить жалобу');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при отправке жалобы');
    }
  };

  const formatMatchTime = (matchedAt: string) => {
    const now = new Date();
    const matched = new Date(matchedAt);
    const diffInDays = Math.floor((now.getTime() - matched.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays < 1) {
      return 'Сегодня';
    } else if (diffInDays === 1) {
      return 'Вчера';
    } else if (diffInDays < 7) {
      return `${diffInDays} дн. назад`;
    } else {
      const diffInWeeks = Math.floor(diffInDays / 7);
      return `${diffInWeeks} нед. назад`;
    }
  };

  const getCompatibilityColor = (compatibility: number) => {
    if (compatibility >= 80) return '#4CAF50';
    if (compatibility >= 60) return '#FF9800';
    return '#F44336';
  };

  const getLifestyleIcon = (category: string, value: string) => {
    switch (category) {
      case 'smoking':
        return value === 'yes' ? 'smoking-rooms' : 'smoke-free';
      case 'drinking':
        return 'local-bar';
      case 'pets':
        return 'pets';
      case 'children':
        return 'child-care';
      default:
        return 'info';
    }
  };

  const getLifestyleText = (category: string, value: string) => {
    switch (category) {
      case 'smoking':
        return value === 'yes' ? 'Курит' : value === 'no' ? 'Не курит' : 'Иногда курит';
      case 'drinking':
        return value === 'yes' ? 'Пьет' : value === 'no' ? 'Не пьет' : 'Иногда пьет';
      case 'pets':
        return value === 'yes' ? 'Есть питомцы' : value === 'no' ? 'Нет питомцев' : 'Хочет питомцев';
      case 'children':
        return value === 'yes' ? 'Есть дети' : value === 'no' ? 'Нет детей' : 'Хочет детей';
      default:
        return value;
    }
  };

  const renderPhotoGallery = () => {
    if (!matchDetail || !matchDetail.photos.length) return null;

    return (
      <View style={styles.photoGallery}>
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / width);
            setCurrentPhotoIndex(index);
          }}
        >
          {matchDetail.photos.map((photo, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => setShowPhotoModal(true)}
            >
              <Image source={{ uri: photo }} style={styles.photo} />
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Индикаторы фото */}
        <View style={styles.photoIndicators}>
          {matchDetail.photos.map((_, index) => (
            <View
              key={index}
              style={[
                styles.photoIndicator,
                index === currentPhotoIndex && styles.activePhotoIndicator,
              ]}
            />
          ))}
        </View>

        {/* Статусы и бейджи */}
        <View style={styles.photoOverlay}>
          {matchDetail.isOnline && (
            <View style={styles.onlineIndicator}>
              <Text style={styles.onlineText}>В сети</Text>
            </View>
          )}

          {matchDetail.isVerified && (
            <View style={styles.verifiedBadge}>
              <Icon name="verified" size={16} color="#4CAF50" />
              <Text style={styles.verifiedText}>Верифицирован</Text>
            </View>
          )}

          {matchDetail.isPremium && (
            <View style={styles.premiumBadge}>
              <Icon name="star" size={16} color="#FFD700" />
              <Text style={styles.premiumText}>Premium</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderBasicInfo = () => {
    if (!matchDetail) return null;

    return (
      <View style={styles.basicInfo}>
        <View style={styles.nameContainer}>
          <Text style={styles.name}>
            {matchDetail.firstName} {matchDetail.lastName}, {matchDetail.age}
          </Text>
          <View style={[styles.compatibilityBadge, { backgroundColor: getCompatibilityColor(matchDetail.compatibility) }]}>
            <Text style={styles.compatibilityText}>{matchDetail.compatibility}% совместимость</Text>
          </View>
        </View>

        <View style={styles.locationContainer}>
          <Icon name="location-on" size={16} color="#666" />
          <Text style={styles.locationText}>{matchDetail.city} • {matchDetail.distance} км</Text>
        </View>

        <View style={styles.matchInfo}>
          <View style={styles.matchTimeContainer}>
            <Icon name="favorite" size={16} color="#FF6B9D" />
            <Text style={styles.matchTimeText}>Матч {formatMatchTime(matchDetail.matchedAt)}</Text>
          </View>

          {(matchDetail.myLikeType === 'super' || matchDetail.theirLikeType === 'super') && (
            <View style={styles.superLikeContainer}>
              <Icon name="star" size={16} color="#4ECDC4" />
              <Text style={styles.superLikeText}>
                {matchDetail.myLikeType === 'super' && matchDetail.theirLikeType === 'super'
                  ? 'Взаимные супер-лайки'
                  : matchDetail.myLikeType === 'super'
                  ? 'Вы поставили супер-лайк'
                  : 'Вам поставили супер-лайк'}
              </Text>
            </View>
          )}
        </View>

        {matchDetail.bio && (
          <View style={styles.bioContainer}>
            <Text style={styles.bioText}>{matchDetail.bio}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderDetailedInfo = () => {
    if (!matchDetail) return null;

    return (
      <View style={styles.detailedInfo}>
        {/* Основная информация */}
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Основная информация</Text>
          
          {matchDetail.education && (
            <View style={styles.infoItem}>
              <Icon name="school" size={20} color="#4ECDC4" />
              <Text style={styles.infoText}>{matchDetail.education}</Text>
            </View>
          )}

          {matchDetail.work && (
            <View style={styles.infoItem}>
              <Icon name="work" size={20} color="#4ECDC4" />
              <Text style={styles.infoText}>{matchDetail.work}</Text>
            </View>
          )}

          {matchDetail.height && (
            <View style={styles.infoItem}>
              <Icon name="height" size={20} color="#4ECDC4" />
              <Text style={styles.infoText}>{matchDetail.height} см</Text>
            </View>
          )}

          {matchDetail.zodiacSign && (
            <View style={styles.infoItem}>
              <Icon name="stars" size={20} color="#4ECDC4" />
              <Text style={styles.infoText}>{matchDetail.zodiacSign}</Text>
            </View>
          )}
        </View>

        {/* Образ жизни */}
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Образ жизни</Text>
          
          {Object.entries(matchDetail.lifestyle).map(([key, value]) => (
            <View key={key} style={styles.infoItem}>
              <Icon name={getLifestyleIcon(key, value)} size={20} color="#4ECDC4" />
              <Text style={styles.infoText}>{getLifestyleText(key, value)}</Text>
            </View>
          ))}
        </View>

        {/* Общие интересы */}
        {matchDetail.commonInterests.length > 0 && (
          <View style={styles.infoSection}>
            <Text style={styles.sectionTitle}>Общие интересы ({matchDetail.commonInterests.length})</Text>
            <View style={styles.interestsContainer}>
              {matchDetail.commonInterests.map((interest, index) => (
                <View key={index} style={styles.interestTag}>
                  <Text style={styles.interestText}>{interest}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Все интересы */}
        {matchDetail.interests.length > 0 && (
          <View style={styles.infoSection}>
            <Text style={styles.sectionTitle}>Интересы</Text>
            <View style={styles.interestsContainer}>
              {matchDetail.interests.map((interest, index) => (
                <View key={index} style={[
                  styles.interestTag,
                  matchDetail.commonInterests.includes(interest) && styles.commonInterestTag
                ]}>
                  <Text style={[
                    styles.interestText,
                    matchDetail.commonInterests.includes(interest) && styles.commonInterestText
                  ]}>{interest}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Взаимные друзья */}
        {matchDetail.mutualFriends > 0 && (
          <View style={styles.infoSection}>
            <Text style={styles.sectionTitle}>Взаимные знакомые</Text>
            <View style={styles.infoItem}>
              <Icon name="group" size={20} color="#4ECDC4" />
              <Text style={styles.infoText}>{matchDetail.mutualFriends} общих знакомых</Text>
            </View>
          </View>
        )}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF6B9D" />
        <Text style={styles.loadingText}>Загрузка...</Text>
      </View>
    );
  }

  if (!matchDetail) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="error" size={60} color="#F44336" />
        <Text style={styles.errorText}>Матч не найден</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Назад</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Матч</Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleReport}
        >
          <Icon name="more-vert" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderPhotoGallery()}
        {renderBasicInfo()}
        {renderDetailedInfo()}
      </ScrollView>

      {/* Кнопки действий */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={styles.unmatchButton}
          onPress={handleUnmatch}
        >
          <Icon name="close" size={24} color="#F44336" />
          <Text style={styles.unmatchButtonText}>Отменить матч</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.chatButton}
          onPress={handleChatPress}
        >
          <Icon name="chat" size={24} color="#FFFFFF" />
          <Text style={styles.chatButtonText}>Написать сообщение</Text>
        </TouchableOpacity>
      </View>

      {/* Модальное окно с фото */}
      <Modal
        visible={showPhotoModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowPhotoModal(false)}
      >
        <View style={styles.photoModalContainer}>
          <TouchableOpacity
            style={styles.photoModalClose}
            onPress={() => setShowPhotoModal(false)}
          >
            <Icon name="close" size={30} color="#FFFFFF" />
          </TouchableOpacity>
          
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            contentOffset={{ x: currentPhotoIndex * width, y: 0 }}
          >
            {matchDetail.photos.map((photo, index) => (
              <Image key={index} source={{ uri: photo }} style={styles.fullScreenPhoto} />
            ))}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 40,
  },
  errorText: {
    fontSize: 18,
    color: '#F44336',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 30,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    zIndex: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  headerButton: {
    padding: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  photoGallery: {
    height: height * 0.6,
    position: 'relative',
  },
  photo: {
    width: width,
    height: height * 0.6,
    resizeMode: 'cover',
  },
  photoIndicators: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  activePhotoIndicator: {
    backgroundColor: '#FFFFFF',
  },
  photoOverlay: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
  },
  onlineIndicator: {
    backgroundColor: '#4CAF50',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  onlineText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  verifiedText: {
    color: '#4CAF50',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignSelf: 'flex-start',
  },
  premiumText: {
    color: '#FFD700',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  basicInfo: {
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  nameContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  compatibilityBadge: {
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  compatibilityText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  locationText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 4,
  },
  matchInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  matchTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  matchTimeText: {
    fontSize: 14,
    color: '#FF6B9D',
    marginLeft: 4,
    fontWeight: '600',
  },
  superLikeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  superLikeText: {
    fontSize: 14,
    color: '#4ECDC4',
    marginLeft: 4,
    fontWeight: '600',
  },
  bioContainer: {
    backgroundColor: '#F5F5F5',
    borderRadius: 15,
    padding: 15,
  },
  bioText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  detailedInfo: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  infoSection: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 12,
    flex: 1,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  interestTag: {
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    marginBottom: 10,
  },
  commonInterestTag: {
    backgroundColor: '#FF6B9D',
  },
  interestText: {
    fontSize: 14,
    color: '#666',
  },
  commonInterestText: {
    color: '#FFFFFF',
  },
  actionsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  unmatchButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 25,
    paddingVertical: 15,
    marginRight: 10,
  },
  unmatchButtonText: {
    color: '#F44336',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  chatButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 25,
    paddingVertical: 15,
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  backButton: {
    backgroundColor: '#FF6B9D',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  photoModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoModalClose: {
    position: 'absolute',
    top: 60,
    right: 20,
    zIndex: 10,
    padding: 10,
  },
  fullScreenPhoto: {
    width: width,
    height: height,
    resizeMode: 'contain',
  },
});

export default MatchDetailScreen;
