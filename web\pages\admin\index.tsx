import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Container,
  Paper,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Report as ReportIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { useAuth } from '../../hooks/useAuth';
import { adminService } from '../../services/adminService';
import { DashboardStats, SystemHealth, RecentActivity } from '../../types/admin.types';
import styles from '../../styles/admin/Dashboard.module.css';

// Регистрируем компоненты Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement
);

interface AdminDashboardProps {}

const AdminDashboard: React.FC<AdminDashboardProps> = () => {
  const router = useRouter();
  const { user, isAdmin } = useAuth();
  const [refreshKey, setRefreshKey] = useState(0);

  // Проверка прав доступа
  useEffect(() => {
    if (!isAdmin) {
      router.push('/');
    }
  }, [isAdmin, router]);

  // Загрузка данных дашборда
  const {
    data: dashboardStats,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useQuery<DashboardStats>({
    queryKey: ['admin', 'dashboard', 'stats', refreshKey],
    queryFn: () => adminService.getDashboardStats(),
    enabled: isAdmin,
    refetchInterval: 30000, // Обновление каждые 30 секунд
  });

  const {
    data: systemHealth,
    isLoading: healthLoading,
    error: healthError,
  } = useQuery<SystemHealth>({
    queryKey: ['admin', 'system', 'health', refreshKey],
    queryFn: () => adminService.getSystemHealth(),
    enabled: isAdmin,
    refetchInterval: 10000, // Обновление каждые 10 секунд
  });

  const {
    data: recentActivity,
    isLoading: activityLoading,
    error: activityError,
  } = useQuery<RecentActivity[]>({
    queryKey: ['admin', 'activity', 'recent', refreshKey],
    queryFn: () => adminService.getRecentActivity(),
    enabled: isAdmin,
    refetchInterval: 60000, // Обновление каждую минуту
  });

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
    refetchStats();
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'critical':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const userGrowthData = {
    labels: dashboardStats?.userGrowth?.map(item => item.date) || [],
    datasets: [
      {
        label: 'Новые пользователи',
        data: dashboardStats?.userGrowth?.map(item => item.newUsers) || [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1,
      },
      {
        label: 'Активные пользователи',
        data: dashboardStats?.userGrowth?.map(item => item.activeUsers) || [],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1,
      },
    ],
  };

  const matchesData = {
    labels: ['Успешные матчи', 'Отклоненные', 'В ожидании'],
    datasets: [
      {
        data: [
          dashboardStats?.matches?.successful || 0,
          dashboardStats?.matches?.rejected || 0,
          dashboardStats?.matches?.pending || 0,
        ],
        backgroundColor: [
          'rgba(75, 192, 192, 0.8)',
          'rgba(255, 99, 132, 0.8)',
          'rgba(255, 205, 86, 0.8)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 205, 86, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  const revenueData = {
    labels: dashboardStats?.revenue?.map(item => item.month) || [],
    datasets: [
      {
        label: 'Доход (₽)',
        data: dashboardStats?.revenue?.map(item => item.amount) || [],
        backgroundColor: 'rgba(153, 102, 255, 0.8)',
        borderColor: 'rgba(153, 102, 255, 1)',
        borderWidth: 1,
      },
    ],
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Админ-панель - Likes & Love</title>
        <meta name="description" content="Административная панель управления приложением знакомств" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <AdminLayout>
        <Container maxWidth="xl" className={styles.container}>
          {/* Заголовок */}
          <Box className={styles.header}>
            <Box>
              <Typography variant="h4" component="h1" className={styles.title}>
                <DashboardIcon className={styles.titleIcon} />
                Административная панель
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Добро пожаловать, {user?.firstName} {user?.lastName}
              </Typography>
            </Box>
            <Tooltip title="Обновить данные">
              <IconButton onClick={handleRefresh} className={styles.refreshButton}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>

          {/* Системные уведомления */}
          {systemHealth && systemHealth.status !== 'healthy' && (
            <Alert 
              severity={getHealthColor(systemHealth.status)} 
              className={styles.systemAlert}
            >
              Статус системы: {systemHealth.message}
            </Alert>
          )}

          {/* Основная статистика */}
          <Grid container spacing={3} className={styles.statsGrid}>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Box className={styles.statContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Всего пользователей
                      </Typography>
                      <Typography variant="h4">
                        {formatNumber(dashboardStats?.totalUsers || 0)}
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        +{dashboardStats?.newUsersToday || 0} сегодня
                      </Typography>
                    </Box>
                    <PeopleIcon className={styles.statIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Box className={styles.statContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Активные пользователи
                      </Typography>
                      <Typography variant="h4">
                        {formatNumber(dashboardStats?.activeUsers || 0)}
                      </Typography>
                      <Typography variant="body2" color="info.main">
                        {((dashboardStats?.activeUsers || 0) / (dashboardStats?.totalUsers || 1) * 100).toFixed(1)}% от общего числа
                      </Typography>
                    </Box>
                    <TrendingUpIcon className={styles.statIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Box className={styles.statContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Матчи сегодня
                      </Typography>
                      <Typography variant="h4">
                        {formatNumber(dashboardStats?.matchesToday || 0)}
                      </Typography>
                      <Typography variant="body2" color="warning.main">
                        {dashboardStats?.matchesGrowth || 0}% к вчера
                      </Typography>
                    </Box>
                    <ReportIcon className={styles.statIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Box className={styles.statContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Доход за месяц
                      </Typography>
                      <Typography variant="h4">
                        ₽{formatNumber(dashboardStats?.monthlyRevenue || 0)}
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        +{dashboardStats?.revenueGrowth || 0}% к прошлому месяцу
                      </Typography>
                    </Box>
                    <TrendingUpIcon className={styles.statIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Графики и аналитика */}
          <Grid container spacing={3} className={styles.chartsGrid}>
            <Grid item xs={12} lg={8}>
              <Card className={styles.chartCard}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Рост пользователей
                  </Typography>
                  {statsLoading ? (
                    <LinearProgress />
                  ) : (
                    <Box className={styles.chartContainer}>
                      <Line data={userGrowthData} options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'top' as const,
                          },
                        },
                      }} />
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} lg={4}>
              <Card className={styles.chartCard}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Статистика матчей
                  </Typography>
                  {statsLoading ? (
                    <LinearProgress />
                  ) : (
                    <Box className={styles.chartContainer}>
                      <Doughnut data={matchesData} options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'bottom' as const,
                          },
                        },
                      }} />
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card className={styles.chartCard}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Доходы по месяцам
                  </Typography>
                  {statsLoading ? (
                    <LinearProgress />
                  ) : (
                    <Box className={styles.chartContainer}>
                      <Bar data={revenueData} options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            display: false,
                          },
                        },
                        scales: {
                          y: {
                            beginAtZero: true,
                            ticks: {
                              callback: function(value) {
                                return '₽' + formatNumber(Number(value));
                              },
                            },
                          },
                        },
                      }} />
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Системное здоровье */}
          <Grid container spacing={3} className={styles.systemGrid}>
            <Grid item xs={12} md={6}>
              <Card className={styles.systemCard}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Состояние системы
                  </Typography>
                  {healthLoading ? (
                    <LinearProgress />
                  ) : systemHealth ? (
                    <Box className={styles.systemHealth}>
                      <Box className={styles.healthItem}>
                        <Typography variant="body2">Сервер</Typography>
                        <Chip 
                          label={systemHealth.server.status} 
                          color={getHealthColor(systemHealth.server.status)}
                          size="small"
                        />
                      </Box>
                      <Box className={styles.healthItem}>
                        <Typography variant="body2">База данных</Typography>
                        <Chip 
                          label={systemHealth.database.status} 
                          color={getHealthColor(systemHealth.database.status)}
                          size="small"
                        />
                      </Box>
                      <Box className={styles.healthItem}>
                        <Typography variant="body2">Redis</Typography>
                        <Chip 
                          label={systemHealth.redis.status} 
                          color={getHealthColor(systemHealth.redis.status)}
                          size="small"
                        />
                      </Box>
                      <Box className={styles.healthItem}>
                        <Typography variant="body2">Файловое хранилище</Typography>
                        <Chip 
                          label={systemHealth.storage.status} 
                          color={getHealthColor(systemHealth.storage.status)}
                          size="small"
                        />
                      </Box>
                    </Box>
                  ) : null}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card className={styles.systemCard}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Последняя активность
                  </Typography>
                  {activityLoading ? (
                    <LinearProgress />
                  ) : recentActivity ? (
                    <Box className={styles.activityList}>
                      {recentActivity.slice(0, 5).map((activity, index) => (
                        <Box key={index} className={styles.activityItem}>
                          <Typography variant="body2" className={styles.activityText}>
                            {activity.description}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(activity.timestamp).toLocaleString()}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  ) : null}
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Быстрые действия */}
          <Grid container spacing={3} className={styles.actionsGrid}>
            <Grid item xs={12}>
              <Card className={styles.actionsCard}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Быстрые действия
                  </Typography>
                  <Box className={styles.actionButtons}>
                    <Button
                      variant="contained"
                      startIcon={<PeopleIcon />}
                      onClick={() => router.push('/admin/users')}
                      className={styles.actionButton}
                    >
                      Управление пользователями
                    </Button>
                    <Button
                      variant="contained"
                      startIcon={<ReportIcon />}
                      onClick={() => router.push('/admin/reports')}
                      className={styles.actionButton}
                    >
                      Отчеты и жалобы
                    </Button>
                    <Button
                      variant="contained"
                      startIcon={<SecurityIcon />}
                      onClick={() => router.push('/admin/moderation')}
                      className={styles.actionButton}
                    >
                      Модерация контента
                    </Button>
                    <Button
                      variant="contained"
                      startIcon={<SettingsIcon />}
                      onClick={() => router.push('/admin/settings')}
                      className={styles.actionButton}
                    >
                      Настройки системы
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </AdminLayout>
    </>
  );
};

export default AdminDashboard;
