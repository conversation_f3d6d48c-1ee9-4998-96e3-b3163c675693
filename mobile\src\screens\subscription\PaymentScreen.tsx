import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { subscriptionService } from '../../services/subscriptionService';
import { paymentService } from '../../services/paymentService';
import { useAuth } from '../../hooks/useAuth';
import { PaymentMethod, PaymentData, SubscriptionPlan } from '../../types/subscription.types';
import { colors, typography, spacing } from '../../theme';

const paymentSchema = yup.object().shape({
  cardNumber: yup.string().required('Номер карты обязателен').min(16, 'Неверный номер карты'),
  expiryDate: yup.string().required('Срок действия обязателен').matches(/^(0[1-9]|1[0-2])\/\d{2}$/, 'Неверный формат'),
  cvv: yup.string().required('CVV обязателен').min(3, 'CVV должен содержать 3 цифры'),
  cardholderName: yup.string().required('Имя владельца обязательно'),
});

interface PaymentScreenProps {}

const PaymentScreen: React.FC<PaymentScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const { planId, action } = route.params as { planId: string; action: 'upgrade' | 'change' };

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
  } = useForm<PaymentData>({
    resolver: yupResolver(paymentSchema),
    defaultValues: {
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      cardholderName: '',
    },
  });

  // Загрузка плана подписки
  const {
    data: plan,
    isLoading: planLoading,
  } = useQuery({
    queryKey: ['subscription', 'plan', planId],
    queryFn: () => subscriptionService.getPlanDetails(planId),
  });

  // Загрузка сохраненных методов оплаты
  const {
    data: savedPaymentMethods,
    isLoading: methodsLoading,
  } = useQuery({
    queryKey: ['payment', 'methods'],
    queryFn: () => paymentService.getSavedPaymentMethods(),
  });

  // Мутация для обработки платежа
  const processPaymentMutation = useMutation({
    mutationFn: (paymentData: PaymentData) => 
      paymentService.processPayment(planId, paymentData, selectedPaymentMethod),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      
      if (result.success) {
        Alert.alert(
          'Оплата успешна!',
          'Ваша подписка активирована. Наслаждайтесь премиум-функциями!',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('SubscriptionSuccess', { planId }),
            },
          ]
        );
      } else {
        Alert.alert('Ошибка оплаты', result.error || 'Не удалось обработать платеж');
      }
    },
    onError: (error) => {
      Alert.alert('Ошибка', 'Не удалось обработать платеж. Попробуйте снова.');
    },
  });

  const onSubmit = (data: PaymentData) => {
    if (!agreedToTerms) {
      Alert.alert('Ошибка', 'Необходимо согласиться с условиями использования');
      return;
    }

    setIsProcessing(true);
    processPaymentMutation.mutate(data);
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const getCardType = (cardNumber: string) => {
    const number = cardNumber.replace(/\s/g, '');
    if (number.startsWith('4')) return 'visa';
    if (number.startsWith('5')) return 'mastercard';
    if (number.startsWith('2')) return 'mir';
    return 'unknown';
  };

  const renderPaymentMethodButton = (method: PaymentMethod) => (
    <TouchableOpacity
      key={method.id}
      style={[
        styles.paymentMethodButton,
        selectedPaymentMethod === method.id && styles.paymentMethodButtonActive,
      ]}
      onPress={() => setSelectedPaymentMethod(method.id)}
    >
      <View style={styles.paymentMethodIcon}>
        <Ionicons
          name={method.icon as any}
          size={24}
          color={selectedPaymentMethod === method.id ? colors.white : colors.primary}
        />
      </View>
      <Text
        style={[
          styles.paymentMethodText,
          selectedPaymentMethod === method.id && styles.paymentMethodTextActive,
        ]}
      >
        {method.name}
      </Text>
      {selectedPaymentMethod === method.id && (
        <Ionicons name="checkmark-circle" size={20} color={colors.white} />
      )}
    </TouchableOpacity>
  );

  const renderSavedCard = (card: any) => (
    <TouchableOpacity
      key={card.id}
      style={[
        styles.savedCardButton,
        selectedPaymentMethod === card.id && styles.savedCardButtonActive,
      ]}
      onPress={() => setSelectedPaymentMethod(card.id)}
    >
      <View style={styles.savedCardInfo}>
        <Ionicons
          name={card.type === 'visa' ? 'card' : 'card-outline'}
          size={24}
          color={colors.primary}
        />
        <Text style={styles.savedCardNumber}>•••• {card.lastFour}</Text>
        <Text style={styles.savedCardExpiry}>{card.expiryDate}</Text>
      </View>
      {selectedPaymentMethod === card.id && (
        <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  if (planLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Загружаем информацию о плане...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Оплата</Text>
          
          <View style={styles.headerSpacer} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Информация о плане */}
          {plan && (
            <View style={styles.planSection}>
              <LinearGradient
                colors={[colors.primary, colors.secondary]}
                style={styles.planCard}
              >
                <Text style={styles.planName}>{plan.name}</Text>
                <Text style={styles.planDescription}>{plan.description}</Text>
                <View style={styles.planPricing}>
                  <Text style={styles.planPrice}>{plan.price}₽</Text>
                  <Text style={styles.planPeriod}>/{plan.period === 'month' ? 'месяц' : 'год'}</Text>
                </View>
              </LinearGradient>
            </View>
          )}

          {/* Методы оплаты */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Способ оплаты</Text>
            
            <View style={styles.paymentMethods}>
              {[
                { id: 'card', name: 'Банковская карта', icon: 'card-outline' },
                { id: 'apple_pay', name: 'Apple Pay', icon: 'phone-portrait-outline' },
                { id: 'google_pay', name: 'Google Pay', icon: 'logo-google' },
              ].map(renderPaymentMethodButton)}
            </View>
          </View>

          {/* Сохраненные карты */}
          {savedPaymentMethods && savedPaymentMethods.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Сохраненные карты</Text>
              <View style={styles.savedCards}>
                {savedPaymentMethods.map(renderSavedCard)}
              </View>
            </View>
          )}

          {/* Форма карты */}
          {selectedPaymentMethod === 'card' && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Данные карты</Text>
              
              <View style={styles.cardForm}>
                <Controller
                  control={control}
                  name="cardNumber"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Номер карты</Text>
                      <TextInput
                        style={[styles.input, errors.cardNumber && styles.inputError]}
                        placeholder="1234 5678 9012 3456"
                        value={formatCardNumber(value)}
                        onChangeText={(text) => onChange(text.replace(/\s/g, ''))}
                        onBlur={onBlur}
                        keyboardType="numeric"
                        maxLength={19}
                      />
                      {errors.cardNumber && (
                        <Text style={styles.errorText}>{errors.cardNumber.message}</Text>
                      )}
                    </View>
                  )}
                />
                
                <View style={styles.cardRow}>
                  <Controller
                    control={control}
                    name="expiryDate"
                    render={({ field: { onChange, onBlur, value } }) => (
                      <View style={[styles.inputContainer, styles.halfInput]}>
                        <Text style={styles.inputLabel}>Срок действия</Text>
                        <TextInput
                          style={[styles.input, errors.expiryDate && styles.inputError]}
                          placeholder="MM/YY"
                          value={formatExpiryDate(value)}
                          onChangeText={(text) => onChange(text.replace(/[^0-9]/g, ''))}
                          onBlur={onBlur}
                          keyboardType="numeric"
                          maxLength={5}
                        />
                        {errors.expiryDate && (
                          <Text style={styles.errorText}>{errors.expiryDate.message}</Text>
                        )}
                      </View>
                    )}
                  />
                  
                  <Controller
                    control={control}
                    name="cvv"
                    render={({ field: { onChange, onBlur, value } }) => (
                      <View style={[styles.inputContainer, styles.halfInput]}>
                        <Text style={styles.inputLabel}>CVV</Text>
                        <TextInput
                          style={[styles.input, errors.cvv && styles.inputError]}
                          placeholder="123"
                          value={value}
                          onChangeText={onChange}
                          onBlur={onBlur}
                          keyboardType="numeric"
                          maxLength={3}
                          secureTextEntry
                        />
                        {errors.cvv && (
                          <Text style={styles.errorText}>{errors.cvv.message}</Text>
                        )}
                      </View>
                    )}
                  />
                </View>
                
                <Controller
                  control={control}
                  name="cardholderName"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <View style={styles.inputContainer}>
                      <Text style={styles.inputLabel}>Имя владельца</Text>
                      <TextInput
                        style={[styles.input, errors.cardholderName && styles.inputError]}
                        placeholder="IVAN IVANOV"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        autoCapitalize="characters"
                      />
                      {errors.cardholderName && (
                        <Text style={styles.errorText}>{errors.cardholderName.message}</Text>
                      )}
                    </View>
                  )}
                />
              </View>
            </View>
          )}

          {/* Согласие с условиями */}
          <View style={styles.section}>
            <TouchableOpacity
              style={styles.termsContainer}
              onPress={() => setAgreedToTerms(!agreedToTerms)}
            >
              <View style={[styles.checkbox, agreedToTerms && styles.checkboxActive]}>
                {agreedToTerms && (
                  <Ionicons name="checkmark" size={16} color={colors.white} />
                )}
              </View>
              <Text style={styles.termsText}>
                Я согласен с{' '}
                <Text
                  style={styles.termsLink}
                  onPress={() => navigation.navigate('Terms')}
                >
                  условиями использования
                </Text>
                {' '}и{' '}
                <Text
                  style={styles.termsLink}
                  onPress={() => navigation.navigate('Privacy')}
                >
                  политикой конфиденциальности
                </Text>
              </Text>
            </TouchableOpacity>
          </View>

          {/* Информация о безопасности */}
          <View style={styles.securityInfo}>
            <Ionicons name="shield-checkmark" size={24} color={colors.success} />
            <Text style={styles.securityText}>
              Ваши данные защищены 256-битным SSL шифрованием
            </Text>
          </View>
        </ScrollView>

        {/* Кнопка оплаты */}
        <View style={styles.bottomSection}>
          <TouchableOpacity
            style={[
              styles.payButton,
              (!isValid || !agreedToTerms || isProcessing) && styles.payButtonDisabled,
            ]}
            onPress={handleSubmit(onSubmit)}
            disabled={!isValid || !agreedToTerms || isProcessing}
          >
            {isProcessing ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <>
                <Ionicons name="card" size={20} color={colors.white} />
                <Text style={styles.payButtonText}>
                  Оплатить {plan?.price}₽
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  planSection: {
    padding: spacing.lg,
  },
  planCard: {
    padding: spacing.lg,
    borderRadius: 16,
    alignItems: 'center',
  },
  planName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  planDescription: {
    fontSize: typography.sizes.md,
    color: colors.white,
    textAlign: 'center',
    marginBottom: spacing.md,
    opacity: 0.9,
  },
  planPricing: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  planPrice: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  planPeriod: {
    fontSize: typography.sizes.lg,
    color: colors.white,
    opacity: 0.8,
  },
  section: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  paymentMethods: {
    gap: spacing.sm,
  },
  paymentMethodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    backgroundColor: colors.surface,
    gap: spacing.md,
  },
  paymentMethodButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  paymentMethodIcon: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  paymentMethodText: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  paymentMethodTextActive: {
    color: colors.white,
  },
  savedCards: {
    gap: spacing.sm,
  },
  savedCardButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    backgroundColor: colors.surface,
  },
  savedCardButtonActive: {
    borderColor: colors.primary,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  savedCardInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  savedCardNumber: {
    fontSize: typography.sizes.md,
    color: colors.text,
    fontFamily: 'monospace',
  },
  savedCardExpiry: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  cardForm: {
    gap: spacing.md,
  },
  cardRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  inputContainer: {
    flex: 1,
  },
  halfInput: {
    flex: 0.5,
  },
  inputLabel: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.md,
    color: colors.text,
    backgroundColor: colors.surface,
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    fontSize: typography.sizes.sm,
    color: colors.error,
    marginTop: spacing.xs,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: colors.border,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  checkboxActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  termsText: {
    flex: 1,
    fontSize: typography.sizes.sm,
    color: colors.text,
    lineHeight: 20,
  },
  termsLink: {
    color: colors.primary,
    textDecorationLine: 'underline',
  },
  securityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: spacing.sm,
  },
  securityText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  bottomSection: {
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.surface,
  },
  payButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    borderRadius: 25,
    gap: spacing.sm,
  },
  payButtonDisabled: {
    backgroundColor: colors.textSecondary,
  },
  payButtonText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
});

export default PaymentScreen;
