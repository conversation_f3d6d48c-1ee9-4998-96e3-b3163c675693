import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
  StatusBar,
  Animated,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface VerifyEmailScreenProps {
  navigation: any;
  route: any;
}

interface RouteParams {
  email?: string;
  userId?: string;
}

const VerifyEmailScreen: React.FC<VerifyEmailScreenProps> = ({ navigation }) => {
  const route = useRoute();
  const { email, userId } = (route.params as RouteParams) || {};
  const { theme } = useTheme();
  const { verifyEmail, resendEmailVerification } = useAuth();

  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const [canResend, setCanResend] = useState(false);

  // Анимации
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    // Анимация появления
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Таймер для повторной отправки
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleVerifyEmail = async () => {
    if (!verificationCode.trim()) {
      Alert.alert('Ошибка', 'Пожалуйста, введите код подтверждения');
      return;
    }

    if (verificationCode.length !== 6) {
      Alert.alert('Ошибка', 'Код должен содержать 6 цифр');
      return;
    }

    setIsLoading(true);

    try {
      const result = await verifyEmail({
        email: email || '',
        code: verificationCode,
        userId: userId || '',
      });

      if (result.success) {
        Alert.alert(
          'Успешно!',
          'Email подтвержден. Добро пожаловать в Likes Love!',
          [
            {
              text: 'Продолжить',
              onPress: () => navigation.navigate('MainApp'),
            },
          ]
        );
      } else {
        Alert.alert('Ошибка', result.message || 'Неверный код подтверждения');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при подтверждении email');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!canResend) return;

    setIsResending(true);

    try {
      const result = await resendEmailVerification(email || '');

      if (result.success) {
        Alert.alert('Успешно', 'Код подтверждения отправлен повторно');
        setTimeLeft(60);
        setCanResend(false);

        // Перезапуск таймера
        const timer = setInterval(() => {
          setTimeLeft((prev) => {
            if (prev <= 1) {
              setCanResend(true);
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось отправить код');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при отправке кода');
    } finally {
      setIsResending(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Иконка */}
            <View style={styles.iconContainer}>
              <Icon name="email" size={80} color="#FFFFFF" />
            </View>

            {/* Заголовок */}
            <Text style={styles.title}>Подтверждение Email</Text>
            <Text style={styles.subtitle}>
              Мы отправили код подтверждения на
            </Text>
            <Text style={styles.email}>{email}</Text>

            {/* Поле ввода кода */}
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.codeInput}
                placeholder="Введите 6-значный код"
                placeholderTextColor="rgba(255, 255, 255, 0.7)"
                value={verificationCode}
                onChangeText={setVerificationCode}
                keyboardType="numeric"
                maxLength={6}
                autoFocus
                textAlign="center"
              />
            </View>

            {/* Кнопка подтверждения */}
            <TouchableOpacity
              style={styles.verifyButton}
              onPress={handleVerifyEmail}
              disabled={isLoading}
            >
              <View style={styles.buttonContent}>
                {isLoading ? (
                  <ActivityIndicator color="#FF6B9D" size="small" />
                ) : (
                  <Text style={styles.verifyButtonText}>Подтвердить</Text>
                )}
              </View>
            </TouchableOpacity>

            {/* Повторная отправка */}
            <View style={styles.resendContainer}>
              <Text style={styles.resendText}>Не получили код?</Text>
              <TouchableOpacity
                onPress={handleResendCode}
                disabled={!canResend || isResending}
                style={styles.resendButton}
              >
                {isResending ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <Text
                    style={[
                      styles.resendButtonText,
                      { opacity: canResend ? 1 : 0.5 },
                    ]}
                  >
                    {canResend ? 'Отправить повторно' : `Повторить через ${formatTime(timeLeft)}`}
                  </Text>
                )}
              </TouchableOpacity>
            </View>

            {/* Кнопка назад */}
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Icon name="arrow-back" size={24} color="#FFFFFF" />
              <Text style={styles.backButtonText}>Назад</Text>
            </TouchableOpacity>
          </Animated.View>
        </KeyboardAvoidingView>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  iconContainer: {
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 5,
  },
  email: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 40,
  },
  inputContainer: {
    width: '100%',
    marginBottom: 30,
  },
  codeInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 20,
    paddingHorizontal: 20,
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 8,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  verifyButton: {
    width: '100%',
    marginBottom: 30,
  },
  buttonContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  verifyButtonText: {
    color: '#FF6B9D',
    fontSize: 18,
    fontWeight: 'bold',
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  resendText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginBottom: 10,
  },
  resendButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  resendButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default VerifyEmailScreen;
