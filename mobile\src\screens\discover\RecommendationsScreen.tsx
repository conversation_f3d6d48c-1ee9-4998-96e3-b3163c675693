import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { discoverService } from '../../services/discoverService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { RecommendedUser, RecommendationCategory } from '../../types/discover.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface RecommendationsScreenProps {}

const RecommendationsScreen: React.FC<RecommendationsScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location } = useLocation();
  const queryClient = useQueryClient();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Загрузка рекомендаций
  const {
    data: recommendations,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['discover', 'recommendations', selectedCategory, location],
    queryFn: () => discoverService.getRecommendations(selectedCategory, location),
  });

  // Мутация для лайка
  const likeMutation = useMutation({
    mutationFn: ({ userId, action }: { userId: string; action: 'like' | 'superlike' }) =>
      discoverService.reactToUser(userId, action),
    onSuccess: (data, variables) => {
      if (data.isMatch) {
        Alert.alert(
          'Это матч! 💕',
          'Вы понравились друг другу!',
          [
            { text: 'Позже', style: 'cancel' },
            { text: 'Написать', onPress: () => navigation.navigate('Chat', { userId: variables.userId }) },
          ]
        );
      }
      queryClient.invalidateQueries({ queryKey: ['matches'] });
    },
  });

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleLike = (userId: string) => {
    likeMutation.mutate({ userId, action: 'like' });
  };

  const handleSuperLike = (userId: string) => {
    likeMutation.mutate({ userId, action: 'superlike' });
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}м`;
    }
    return `${distance.toFixed(1)}км`;
  };

  const getRecommendationIcon = (reason: string) => {
    switch (reason) {
      case 'common_interests':
        return 'heart-outline';
      case 'location':
        return 'location-outline';
      case 'mutual_friends':
        return 'people-outline';
      case 'similar_age':
        return 'calendar-outline';
      case 'high_compatibility':
        return 'star-outline';
      default:
        return 'sparkles-outline';
    }
  };

  const getRecommendationText = (reason: string) => {
    switch (reason) {
      case 'common_interests':
        return 'Общие интересы';
      case 'location':
        return 'Рядом с вами';
      case 'mutual_friends':
        return 'Общие друзья';
      case 'similar_age':
        return 'Подходящий возраст';
      case 'high_compatibility':
        return 'Высокая совместимость';
      default:
        return 'Рекомендуем';
    }
  };

  const renderCategoryButton = (category: RecommendationCategory) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryButton,
        selectedCategory === category.id && styles.categoryButtonActive,
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <Ionicons
        name={category.icon as any}
        size={20}
        color={selectedCategory === category.id ? colors.white : colors.text}
      />
      <Text
        style={[
          styles.categoryButtonText,
          selectedCategory === category.id && styles.categoryButtonTextActive,
        ]}
      >
        {category.name}
      </Text>
      {category.count > 0 && (
        <View style={styles.categoryBadge}>
          <Text style={styles.categoryBadgeText}>{category.count}</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderUserCard = (user: RecommendedUser, index: number) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 150,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateY: interpolate(animatedValue.value, [0, 1], [30, 0]),
          },
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.95, 1]),
          },
        ],
      };
    });

    return (
      <Animated.View key={user.id} style={[styles.userCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('UserProfile', { userId: user.id })}
          activeOpacity={0.8}
        >
          <Image source={{ uri: user.photos[0] }} style={styles.userImage} />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={styles.userImageGradient}
          />
          
          {/* Причина рекомендации */}
          <View style={styles.recommendationBadge}>
            <Ionicons
              name={getRecommendationIcon(user.recommendationReason) as any}
              size={14}
              color={colors.primary}
            />
            <Text style={styles.recommendationText}>
              {getRecommendationText(user.recommendationReason)}
            </Text>
          </View>
          
          {/* Совместимость */}
          <View style={styles.compatibilityBadge}>
            <Text style={styles.compatibilityText}>
              {user.compatibilityScore}% совместимость
            </Text>
          </View>
          
          {/* Верификация */}
          {user.isVerified && (
            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
            </View>
          )}
          
          <View style={styles.userInfo}>
            <View style={styles.userMainInfo}>
              <Text style={styles.userName}>
                {user.firstName}, {user.age}
              </Text>
            </View>
            
            <View style={styles.userDetails}>
              <Text style={styles.userDistance}>
                <Ionicons name="location-outline" size={14} color={colors.textSecondary} />
                {' '}{formatDistance(user.distance)}
              </Text>
              
              {user.isOnline && (
                <View style={styles.onlineStatus}>
                  <View style={styles.onlineDot} />
                  <Text style={styles.onlineText}>Онлайн</Text>
                </View>
              )}
            </View>
            
            {user.bio && (
              <Text style={styles.userBio} numberOfLines={2}>
                {user.bio}
              </Text>
            )}
            
            {user.commonInterests.length > 0 && (
              <View style={styles.commonInterestsContainer}>
                <Text style={styles.commonInterestsTitle}>Общие интересы:</Text>
                <View style={styles.commonInterestsList}>
                  {user.commonInterests.slice(0, 3).map((interest, idx) => (
                    <View key={idx} style={styles.commonInterestTag}>
                      <Text style={styles.commonInterestText}>{interest}</Text>
                    </View>
                  ))}
                  {user.commonInterests.length > 3 && (
                    <Text style={styles.moreInterests}>+{user.commonInterests.length - 3}</Text>
                  )}
                </View>
              </View>
            )}
          </View>
        </TouchableOpacity>
        
        <View style={styles.userActions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.superLikeButton]}
            onPress={() => handleSuperLike(user.id)}
            disabled={likeMutation.isPending}
          >
            <Ionicons name="star" size={24} color={colors.warning} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.likeButton]}
            onPress={() => handleLike(user.id)}
            disabled={likeMutation.isPending}
          >
            <Ionicons name="heart" size={24} color={colors.success} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.chatButton]}
            onPress={() => navigation.navigate('Chat', { userId: user.id })}
          >
            <Ionicons name="chatbubble" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  if (isLoading && !recommendations) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Подбираем рекомендации...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="bulb-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить рекомендации
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Рекомендации</Text>
        
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={() => navigation.navigate('RecommendationSettings')}
        >
          <Ionicons name="settings-outline" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Категории */}
        {recommendations?.categories && (
          <View style={styles.categoriesContainer}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesList}
            >
              {recommendations.categories.map(renderCategoryButton)}
            </ScrollView>
          </View>
        )}

        {/* Статистика рекомендаций */}
        {recommendations?.stats && (
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{recommendations.stats.totalRecommendations}</Text>
              <Text style={styles.statLabel}>Всего рекомендаций</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{recommendations.stats.averageCompatibility}%</Text>
              <Text style={styles.statLabel}>Средняя совместимость</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{recommendations.stats.newToday}</Text>
              <Text style={styles.statLabel}>Новых сегодня</Text>
            </View>
          </View>
        )}

        {/* Список рекомендованных пользователей */}
        <View style={styles.usersContainer}>
          {recommendations?.users && recommendations.users.length > 0 ? (
            recommendations.users.map((user, index) => renderUserCard(user, index))
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="bulb-outline" size={64} color={colors.textSecondary} />
              <Text style={styles.emptyTitle}>Нет рекомендаций</Text>
              <Text style={styles.emptyText}>
                Попробуйте изменить настройки или зайдите позже
              </Text>
              <TouchableOpacity
                style={styles.settingsButton}
                onPress={() => navigation.navigate('RecommendationSettings')}
              >
                <Text style={styles.settingsButtonText}>Настроить рекомендации</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Индикатор загрузки действий */}
      {likeMutation.isPending && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  scrollView: {
    flex: 1,
  },
  categoriesContainer: {
    paddingVertical: spacing.md,
  },
  categoriesList: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    gap: spacing.xs,
  },
  categoryButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  categoryButtonTextActive: {
    color: colors.white,
  },
  categoryBadge: {
    backgroundColor: colors.error,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  categoryBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    marginHorizontal: spacing.lg,
    borderRadius: 16,
    marginBottom: spacing.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  usersContainer: {
    paddingHorizontal: spacing.lg,
  },
  userCard: {
    backgroundColor: colors.surface,
    borderRadius: 20,
    marginBottom: spacing.lg,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
  },
  userImage: {
    width: '100%',
    height: 300,
    resizeMode: 'cover',
  },
  userImageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 150,
  },
  recommendationBadge: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.9)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  recommendationText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  compatibilityBadge: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  compatibilityText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  verifiedBadge: {
    position: 'absolute',
    top: spacing.md + 40,
    right: spacing.md,
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 12,
    padding: 2,
  },
  userInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.lg,
  },
  userMainInfo: {
    marginBottom: spacing.xs,
  },
  userName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  userDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  userDistance: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  onlineStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.success,
    marginRight: spacing.xs,
  },
  onlineText: {
    fontSize: typography.sizes.sm,
    color: colors.success,
    fontWeight: typography.weights.medium,
  },
  userBio: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    lineHeight: 20,
    marginBottom: spacing.sm,
  },
  commonInterestsContainer: {
    marginBottom: spacing.sm,
  },
  commonInterestsTitle: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  commonInterestsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  commonInterestTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  commonInterestText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  moreInterests: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium,
  },
  userActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.surface,
  },
  actionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  superLikeButton: {
    backgroundColor: colors.background,
  },
  likeButton: {
    backgroundColor: colors.background,
  },
  chatButton: {
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  settingsButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default RecommendationsScreen;
