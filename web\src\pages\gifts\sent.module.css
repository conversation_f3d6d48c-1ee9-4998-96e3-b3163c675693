.container {
  padding: 2rem 0;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  background: linear-gradient(45deg, #e91e63, #9c27b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.subtitle {
  margin-bottom: 2rem;
}

.filtersCard {
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.statsGrid {
  margin-bottom: 2rem;
}

.statCard {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.statContent {
  text-align: center;
  padding: 1.5rem;
}

.statIcon {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.statIcon svg {
  font-size: 2.5rem;
}

.statNumber {
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #2196f3, #9c27b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.giftsGrid {
  margin-bottom: 2rem;
}

.giftCard {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.giftCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.giftHeader {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.giftImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.giftCard:hover .giftImage {
  transform: scale(1.05);
}

.statusChip {
  position: absolute;
  top: 8px;
  right: 8px;
  backdrop-filter: blur(4px);
  background: rgba(255, 255, 255, 0.9);
}

.giftContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.giftName {
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.recipientInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.giftDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.giftPrice {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 600;
  color: #ff9800;
}

.giftMessage {
  font-style: italic;
  color: #666;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: #f5f5f5;
  border-radius: 8px;
  border-left: 3px solid #2196f3;
}

.deliveryInfo {
  font-weight: 600;
  margin-bottom: 1rem;
}

.giftActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: auto;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.errorAlert,
.emptyAlert {
  margin: 2rem 0;
  border-radius: 12px;
  text-align: center;
}

.emptyAlert {
  padding: 2rem;
  background: linear-gradient(45deg, #f8f9fa, #fff);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0;
  }
  
  .header {
    margin-bottom: 1.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .filtersCard,
  .statsGrid {
    margin-bottom: 1.5rem;
  }
  
  .statContent {
    padding: 1rem;
  }
  
  .statIcon svg {
    font-size: 2rem;
  }
  
  .statNumber {
    font-size: 1.75rem;
  }
  
  .giftDetails {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .recipientInfo {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.75rem;
  }
  
  .giftCard {
    margin-bottom: 1rem;
  }
  
  .giftContent {
    padding: 0.75rem;
  }
  
  .paginationContainer {
    margin-top: 2rem;
  }
  
  .emptyAlert {
    padding: 1.5rem;
  }
}

/* Status-specific styles */
.giftCard[data-status="delivered"] {
  border-left: 4px solid #4caf50;
}

.giftCard[data-status="pending"] {
  border-left: 4px solid #ff9800;
}

.giftCard[data-status="cancelled"] {
  border-left: 4px solid #f44336;
  opacity: 0.7;
}

.giftCard[data-status="viewed"] {
  border-left: 4px solid #2196f3;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .giftCard,
  .giftImage,
  .statCard {
    transition: none;
  }
  
  .giftCard:hover,
  .statCard:hover {
    transform: none;
  }
  
  .giftCard:hover .giftImage {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .giftCard {
    border: 2px solid #000;
  }
  
  .statusChip {
    border: 1px solid #000;
  }
  
  .statCard {
    border: 2px solid #000;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .giftMessage {
    background: #2d2d2d;
    color: #ccc;
    border-left-color: #bb86fc;
  }
  
  .emptyAlert {
    background: linear-gradient(45deg, #2d2d2d, #3d3d3d);
  }
  
  .statusChip {
    background: rgba(45, 45, 45, 0.9);
  }
}

/* Animation for status changes */
@keyframes statusChange {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.statusChip.status-changed {
  animation: statusChange 0.5s ease-in-out;
}

/* Loading states */
.giftCard.loading {
  pointer-events: none;
  opacity: 0.6;
}

.giftCard.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
