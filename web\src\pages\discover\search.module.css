.container {
  padding: 2rem 0;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  background: linear-gradient(45deg, #e91e63, #9c27b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.subtitle {
  margin-bottom: 2rem;
}

.filtersDesktop {
  display: block;
}

.filtersCard {
  position: sticky;
  top: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.filtersContent {
  padding: 0;
}

.filtersButtonMobile {
  display: none;
}

.resultsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f8f9fa, #fff);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.usersGrid {
  margin-bottom: 2rem;
}

.userCard {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

.userCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.userImageContainer {
  position: relative;
  overflow: hidden;
  height: 300px;
}

.userImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.userCard:hover .userImage {
  transform: scale(1.05);
}

.onlineBadge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(76, 175, 80, 0.9);
  color: white;
  backdrop-filter: blur(4px);
}

.premiumBadge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 193, 7, 0.9);
  color: white;
  backdrop-filter: blur(4px);
}

.userActions {
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.userCard:hover .userActions {
  opacity: 1;
}

.superLikeButton {
  background: rgba(255, 193, 7, 0.9);
  color: white;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.superLikeButton:hover {
  background: rgba(255, 193, 7, 1);
  transform: scale(1.1);
}

.likeButton {
  background: rgba(233, 30, 99, 0.9);
  color: white;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.likeButton:hover {
  background: rgba(233, 30, 99, 1);
  transform: scale(1.1);
}

.userContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
}

.userName {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #333;
}

.userAge {
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.userLocation,
.userOccupation {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.userInterests {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;
  align-items: center;
}

.interestChip {
  background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
  color: #5e35b1;
  font-size: 0.75rem;
}

.userCardActions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1rem;
}

.userCardActions button {
  flex: 1;
  border-radius: 20px;
  text-transform: none;
  font-weight: 600;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.errorAlert,
.emptyAlert {
  margin: 2rem 0;
  border-radius: 12px;
  text-align: center;
}

.emptyAlert {
  padding: 2rem;
}

.filtersDrawer {
  display: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .filtersDesktop {
    display: none;
  }
  
  .filtersButtonMobile {
    display: inline-flex;
  }
  
  .filtersDrawer {
    display: block;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem 0;
  }
  
  .header {
    margin-bottom: 1.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .resultsHeader {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .userCard {
    margin-bottom: 1rem;
  }
  
  .userImageContainer {
    height: 250px;
  }
  
  .userContent {
    padding: 1rem;
  }
  
  .userActions {
    opacity: 1;
    bottom: 8px;
    right: 8px;
  }
  
  .userCardActions {
    flex-direction: column;
  }
  
  .paginationContainer {
    margin-top: 2rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.75rem;
  }
  
  .userImageContainer {
    height: 200px;
  }
  
  .userContent {
    padding: 0.75rem;
  }
  
  .userInterests {
    margin: 0.75rem 0;
  }
  
  .interestChip {
    font-size: 0.7rem;
  }
}

/* Loading states */
.userCard.loading {
  pointer-events: none;
  opacity: 0.7;
}

.userCard.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .userCard,
  .userImage,
  .superLikeButton,
  .likeButton {
    transition: none;
  }
  
  .userCard:hover {
    transform: none;
  }
  
  .userCard:hover .userImage {
    transform: none;
  }
  
  .superLikeButton:hover,
  .likeButton:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .userCard {
    border: 2px solid #000;
  }
  
  .onlineBadge,
  .premiumBadge {
    border: 1px solid #000;
  }
  
  .interestChip {
    border: 1px solid #000;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .filtersCard {
    background: #2d2d2d;
    color: #fff;
  }
  
  .userCard {
    background: #2d2d2d;
    color: #fff;
  }
  
  .userName {
    color: #fff;
  }
  
  .resultsHeader {
    background: linear-gradient(135deg, #2d2d2d, #3d3d3d);
    color: #fff;
  }
  
  .interestChip {
    background: linear-gradient(45deg, #2d2d2d, #3d3d3d);
    color: #bb86fc;
  }
}

/* Special effects */
.userCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
  z-index: 1;
  pointer-events: none;
}

.userCard:hover::before {
  left: 100%;
}

/* Match animation */
@keyframes matchFound {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.userCard.matched {
  animation: matchFound 0.6s ease-in-out;
  border: 2px solid #4caf50;
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.5);
}

/* Like animation */
@keyframes likeAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

.likeButton.liked {
  animation: likeAnimation 0.3s ease-in-out;
}

/* Super like animation */
@keyframes superLikeAnimation {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.3) rotate(10deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

.superLikeButton.super-liked {
  animation: superLikeAnimation 0.5s ease-in-out;
}
