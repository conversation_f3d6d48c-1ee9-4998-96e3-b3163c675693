import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
  StatusBar,
  Animated,
  ScrollView,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface SocialLoginScreenProps {
  navigation: any;
}

interface SocialProvider {
  id: string;
  name: string;
  icon: string;
  color: string;
  backgroundColor: string;
  available: boolean;
}

const SocialLoginScreen: React.FC<SocialLoginScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { socialLogin } = useAuth();

  const [isLoading, setIsLoading] = useState(false);
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null);

  // Анимации
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    // Анимация появления
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const socialProviders: SocialProvider[] = [
    {
      id: 'google',
      name: 'Google',
      icon: 'google',
      color: '#FFFFFF',
      backgroundColor: '#DB4437',
      available: true,
    },
    {
      id: 'vk',
      name: 'ВКонтакте',
      icon: 'group',
      color: '#FFFFFF',
      backgroundColor: '#4C75A3',
      available: true,
    },
    {
      id: 'yandex',
      name: 'Яндекс',
      icon: 'search',
      color: '#FFFFFF',
      backgroundColor: '#FFCC00',
      available: true,
    },
    {
      id: 'sber',
      name: 'Сбер ID',
      icon: 'account-balance',
      color: '#FFFFFF',
      backgroundColor: '#21A038',
      available: true,
    },
    {
      id: 'apple',
      name: 'Apple',
      icon: 'phone-iphone',
      color: '#FFFFFF',
      backgroundColor: '#000000',
      available: Platform.OS === 'ios',
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: 'facebook',
      color: '#FFFFFF',
      backgroundColor: '#1877F2',
      available: false, // Отключен в России
    },
  ];

  const handleSocialLogin = async (provider: SocialProvider) => {
    if (!provider.available) {
      Alert.alert('Недоступно', `Вход через ${provider.name} недоступен в вашем регионе`);
      return;
    }

    setIsLoading(true);
    setLoadingProvider(provider.id);

    try {
      const result = await socialLogin(provider.id);

      if (result.success) {
        Alert.alert(
          'Успешно!',
          `Добро пожаловать в Likes Love!`,
          [
            {
              text: 'Продолжить',
              onPress: () => navigation.navigate('MainApp'),
            },
          ]
        );
      } else {
        Alert.alert(
          'Ошибка',
          result.message || `Не удалось войти через ${provider.name}`
        );
      }
    } catch (error) {
      Alert.alert(
        'Ошибка',
        `Произошла ошибка при входе через ${provider.name}`
      );
    } finally {
      setIsLoading(false);
      setLoadingProvider(null);
    }
  };

  const availableProviders = socialProviders.filter(provider => provider.available);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Логотип */}
            <View style={styles.logoContainer}>
              <View style={styles.logoCircle}>
                <Icon name="favorite" size={50} color="#FFFFFF" />
              </View>
              <Text style={styles.logoText}>Likes Love</Text>
              <Text style={styles.subtitle}>Быстрый вход через соцсети</Text>
            </View>

            {/* Социальные кнопки */}
            <View style={styles.socialContainer}>
              {availableProviders.map((provider) => (
                <TouchableOpacity
                  key={provider.id}
                  style={[
                    styles.socialButton,
                    { backgroundColor: provider.backgroundColor },
                    !provider.available && styles.disabledButton,
                  ]}
                  onPress={() => handleSocialLogin(provider)}
                  disabled={isLoading || !provider.available}
                >
                  <View style={styles.socialButtonContent}>
                    {loadingProvider === provider.id ? (
                      <ActivityIndicator color={provider.color} size="small" />
                    ) : (
                      <>
                        <Icon
                          name={provider.icon}
                          size={24}
                          color={provider.color}
                          style={styles.socialIcon}
                        />
                        <Text style={[styles.socialButtonText, { color: provider.color }]}>
                          Войти через {provider.name}
                        </Text>
                      </>
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            {/* Разделитель */}
            <View style={styles.dividerContainer}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>или</Text>
              <View style={styles.dividerLine} />
            </View>

            {/* Альтернативные варианты */}
            <View style={styles.alternativeContainer}>
              <TouchableOpacity
                style={styles.alternativeButton}
                onPress={() => navigation.navigate('Login')}
              >
                <Icon name="email" size={20} color="#FFFFFF" style={styles.alternativeIcon} />
                <Text style={styles.alternativeText}>Войти с email</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.alternativeButton}
                onPress={() => navigation.navigate('Register')}
              >
                <Icon name="person-add" size={20} color="#FFFFFF" style={styles.alternativeIcon} />
                <Text style={styles.alternativeText}>Создать новый аккаунт</Text>
              </TouchableOpacity>
            </View>

            {/* Информация о конфиденциальности */}
            <View style={styles.privacyContainer}>
              <Icon name="security" size={16} color="rgba(255, 255, 255, 0.7)" />
              <Text style={styles.privacyText}>
                Мы не публикуем ничего в ваших социальных сетях без разрешения
              </Text>
            </View>

            {/* Кнопка назад */}
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Icon name="arrow-back" size={24} color="#FFFFFF" />
              <Text style={styles.backButtonText}>Назад</Text>
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  logoText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  socialContainer: {
    width: '100%',
    marginBottom: 30,
  },
  socialButton: {
    borderRadius: 15,
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  disabledButton: {
    opacity: 0.5,
  },
  socialButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    minHeight: 56,
  },
  socialIcon: {
    marginRight: 12,
  },
  socialButtonText: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 30,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  dividerText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginHorizontal: 20,
  },
  alternativeContainer: {
    width: '100%',
    marginBottom: 30,
  },
  alternativeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  alternativeIcon: {
    marginRight: 12,
  },
  alternativeText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  privacyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 30,
    maxWidth: '90%',
  },
  privacyText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginLeft: 8,
    textAlign: 'center',
    flex: 1,
    lineHeight: 16,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default SocialLoginScreen;
