import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  RefreshControl,
  TextInput,
  Modal,
  Alert,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');
const ITEM_SIZE = (width - 60) / 3;

interface MediaGalleryScreenProps {
  navigation: any;
  route: any;
}

interface MediaItem {
  id: string;
  type: 'photo' | 'video' | 'file';
  url: string;
  thumbnail?: string;
  fileName: string;
  fileSize: number;
  uploadedAt: string;
  uploadedBy: {
    id: string;
    name: string;
    avatar: string;
  };
  chatId: string;
  chatName: string;
  messageId: string;
}

const MediaGalleryScreen: React.FC<MediaGalleryScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { getChatMedia, downloadMedia, deleteMedia } = useAuth();
  
  const { chatId, chatName } = route.params;

  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<MediaItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<'all' | 'photo' | 'video' | 'file'>('all');
  const [selectedItem, setSelectedItem] = useState<MediaItem | null>(null);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadMediaItems();
  }, [chatId]);

  useEffect(() => {
    filterItems();
  }, [mediaItems, searchQuery, selectedType]);

  const loadMediaItems = async (pageNum = 1, refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const result = await getChatMedia({
        chatId,
        page: pageNum,
        limit: 30,
        type: selectedType === 'all' ? undefined : selectedType,
      });

      if (result.success) {
        const newItems = result.mediaItems || [];
        
        if (refresh || pageNum === 1) {
          setMediaItems(newItems);
        } else {
          setMediaItems(prev => [...prev, ...newItems]);
        }

        setHasMore(newItems.length === 30);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('Error loading media items:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить медиафайлы');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const filterItems = () => {
    let filtered = mediaItems;

    // Фильтр по типу
    if (selectedType !== 'all') {
      filtered = filtered.filter(item => item.type === selectedType);
    }

    // Поиск по имени файла
    if (searchQuery.trim()) {
      filtered = filtered.filter(item =>
        item.fileName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredItems(filtered);
  };

  const handleRefresh = useCallback(() => {
    loadMediaItems(1, true);
  }, [chatId, selectedType]);

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      loadMediaItems(page + 1);
    }
  };

  const handleItemPress = (item: MediaItem) => {
    setSelectedItem(item);
    setShowPreviewModal(true);
  };

  const handleDownload = async (item: MediaItem) => {
    try {
      // Запрос разрешений для Android
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
        );
        
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          Alert.alert('Ошибка', 'Необходимо разрешение для сохранения файлов');
          return;
        }
      }

      const result = await downloadMedia(item.id);
      if (result.success) {
        Alert.alert('Успешно', 'Файл сохранен в галерею');
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось скачать файл');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при скачивании');
    }
  };

  const handleDelete = async (item: MediaItem) => {
    Alert.alert(
      'Удалить файл',
      'Вы уверены, что хотите удалить этот файл? Это действие нельзя отменить.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await deleteMedia(item.id);
              if (result.success) {
                setMediaItems(prev => prev.filter(i => i.id !== item.id));
                setShowPreviewModal(false);
              } else {
                Alert.alert('Ошибка', result.message || 'Не удалось удалить файл');
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Произошла ошибка при удалении');
            }
          },
        },
      ]
    );
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Сегодня';
    } else if (diffInDays === 1) {
      return 'Вчера';
    } else if (diffInDays < 7) {
      return `${diffInDays} дн. назад`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getFileIcon = (type: string, fileName: string) => {
    if (type === 'video') return 'play-circle-filled';
    if (type === 'photo') return 'photo';
    
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'picture-as-pdf';
      case 'doc':
      case 'docx':
        return 'description';
      case 'xls':
      case 'xlsx':
        return 'table-chart';
      case 'zip':
      case 'rar':
        return 'archive';
      default:
        return 'insert-drive-file';
    }
  };

  const renderMediaItem = ({ item }: { item: MediaItem }) => (
    <TouchableOpacity
      style={styles.mediaItem}
      onPress={() => handleItemPress(item)}
    >
      {item.type === 'photo' ? (
        <Image source={{ uri: item.url }} style={styles.mediaImage} />
      ) : item.type === 'video' ? (
        <View style={styles.videoContainer}>
          <Image 
            source={{ uri: item.thumbnail || item.url }} 
            style={styles.mediaImage} 
          />
          <View style={styles.videoOverlay}>
            <Icon name="play-circle-filled" size={30} color="rgba(255, 255, 255, 0.9)" />
          </View>
        </View>
      ) : (
        <View style={styles.fileContainer}>
          <Icon 
            name={getFileIcon(item.type, item.fileName)} 
            size={40} 
            color="#4ECDC4" 
          />
          <Text style={styles.fileName} numberOfLines={2}>
            {item.fileName}
          </Text>
          <Text style={styles.fileSize}>
            {formatFileSize(item.fileSize)}
          </Text>
        </View>
      )}

      {/* Информация о файле */}
      <View style={styles.mediaInfo}>
        <Text style={styles.mediaDate}>
          {formatDate(item.uploadedAt)}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon 
        name={
          selectedType === 'photo' ? 'photo-library' :
          selectedType === 'video' ? 'video-library' :
          selectedType === 'file' ? 'folder' :
          'perm-media'
        } 
        size={80} 
        color="rgba(255, 255, 255, 0.5)" 
      />
      <Text style={styles.emptyTitle}>
        {selectedType === 'all' ? 'Нет медиафайлов' :
         selectedType === 'photo' ? 'Нет фотографий' :
         selectedType === 'video' ? 'Нет видео' :
         'Нет файлов'}
      </Text>
      <Text style={styles.emptySubtitle}>
        Медиафайлы из чата будут отображаться здесь
      </Text>
    </View>
  );

  const renderPreviewModal = () => {
    if (!selectedItem) return null;

    return (
      <Modal
        visible={showPreviewModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowPreviewModal(false)}
      >
        <View style={styles.previewContainer}>
          <View style={styles.previewHeader}>
            <TouchableOpacity
              style={styles.previewCloseButton}
              onPress={() => setShowPreviewModal(false)}
            >
              <Icon name="close" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            
            <View style={styles.previewInfo}>
              <Text style={styles.previewFileName}>{selectedItem.fileName}</Text>
              <Text style={styles.previewDetails}>
                {formatFileSize(selectedItem.fileSize)} • {formatDate(selectedItem.uploadedAt)}
              </Text>
            </View>

            <TouchableOpacity
              style={styles.previewActionButton}
              onPress={() => handleDownload(selectedItem)}
            >
              <Icon name="download" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          <View style={styles.previewContent}>
            {selectedItem.type === 'photo' ? (
              <Image source={{ uri: selectedItem.url }} style={styles.previewImage} />
            ) : selectedItem.type === 'video' ? (
              <View style={styles.previewVideoContainer}>
                <Image 
                  source={{ uri: selectedItem.thumbnail || selectedItem.url }} 
                  style={styles.previewImage} 
                />
                <TouchableOpacity style={styles.playButton}>
                  <Icon name="play-circle-filled" size={60} color="rgba(255, 255, 255, 0.9)" />
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.previewFileContainer}>
                <Icon 
                  name={getFileIcon(selectedItem.type, selectedItem.fileName)} 
                  size={80} 
                  color="#FFFFFF" 
                />
                <Text style={styles.previewFileText}>{selectedItem.fileName}</Text>
                <Text style={styles.previewFileSize}>
                  {formatFileSize(selectedItem.fileSize)}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.previewActions}>
            <TouchableOpacity
              style={styles.previewActionButton}
              onPress={() => navigation.navigate('ChatScreen', { 
                chatId: selectedItem.chatId,
                messageId: selectedItem.messageId 
              })}
            >
              <Icon name="chat" size={20} color="#FFFFFF" />
              <Text style={styles.previewActionText}>Перейти к сообщению</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.previewActionButton, styles.deleteButton]}
              onPress={() => handleDelete(selectedItem)}
            >
              <Icon name="delete" size={20} color="#FFFFFF" />
              <Text style={styles.previewActionText}>Удалить</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  const renderFilterTabs = () => (
    <View style={styles.filterContainer}>
      {[
        { key: 'all', label: 'Все', icon: 'perm-media' },
        { key: 'photo', label: 'Фото', icon: 'photo' },
        { key: 'video', label: 'Видео', icon: 'videocam' },
        { key: 'file', label: 'Файлы', icon: 'insert-drive-file' },
      ].map((filter) => (
        <TouchableOpacity
          key={filter.key}
          style={[
            styles.filterTab,
            selectedType === filter.key && styles.filterTabActive,
          ]}
          onPress={() => setSelectedType(filter.key as any)}
        >
          <Icon
            name={filter.icon}
            size={16}
            color={selectedType === filter.key ? '#FF6B9D' : 'rgba(255, 255, 255, 0.8)'}
          />
          <Text
            style={[
              styles.filterTabText,
              selectedType === filter.key && styles.filterTabTextActive,
            ]}
          >
            {filter.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Медиафайлы</Text>
          <Text style={styles.headerSubtitle}>{chatName}</Text>
        </View>

        {/* Поиск */}
        <View style={styles.searchContainer}>
          <Icon name="search" size={20} color="rgba(255, 255, 255, 0.7)" />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск файлов..."
            placeholderTextColor="rgba(255, 255, 255, 0.7)"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="clear" size={20} color="rgba(255, 255, 255, 0.7)" />
            </TouchableOpacity>
          )}
        </View>

        {/* Фильтры */}
        {renderFilterTabs()}

        {/* Статистика */}
        <View style={styles.statsContainer}>
          <Text style={styles.statsText}>
            Найдено: {filteredItems.length} файлов
          </Text>
        </View>

        {/* Галерея */}
        <FlatList
          data={filteredItems}
          renderItem={renderMediaItem}
          keyExtractor={(item) => item.id}
          numColumns={3}
          contentContainerStyle={styles.galleryContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
              colors={['#FFFFFF']}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListEmptyComponent={!isLoading ? renderEmptyState : null}
          ListFooterComponent={
            isLoading && !isRefreshing ? (
              <View style={styles.footerLoader}>
                <ActivityIndicator color="#FFFFFF" size="small" />
              </View>
            ) : null
          }
        />
      </LinearGradient>

      {/* Модальное окно предпросмотра */}
      {renderPreviewModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    position: 'absolute',
    left: 20,
    top: 68,
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginHorizontal: 20,
    marginBottom: 15,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 10,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  filterTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 2,
  },
  filterTabActive: {
    backgroundColor: '#FFFFFF',
  },
  filterTabText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  filterTabTextActive: {
    color: '#FF6B9D',
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  statsText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    textAlign: 'center',
  },
  galleryContainer: {
    padding: 20,
  },
  mediaItem: {
    width: ITEM_SIZE,
    height: ITEM_SIZE,
    margin: 5,
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  mediaImage: {
    width: '100%',
    height: '80%',
    resizeMode: 'cover',
  },
  videoContainer: {
    position: 'relative',
    width: '100%',
    height: '80%',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  fileContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  fileName: {
    fontSize: 10,
    color: '#333',
    textAlign: 'center',
    marginTop: 4,
  },
  fileSize: {
    fontSize: 8,
    color: '#666',
    marginTop: 2,
  },
  mediaInfo: {
    padding: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  mediaDate: {
    fontSize: 8,
    color: '#666',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  previewContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  previewCloseButton: {
    padding: 8,
  },
  previewInfo: {
    flex: 1,
    marginHorizontal: 15,
  },
  previewFileName: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  previewDetails: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 2,
  },
  previewActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 5,
  },
  previewActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginLeft: 4,
  },
  previewContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewImage: {
    width: width,
    height: height * 0.6,
    resizeMode: 'contain',
  },
  previewVideoContainer: {
    position: 'relative',
    width: width,
    height: height * 0.6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    position: 'absolute',
  },
  previewFileContainer: {
    alignItems: 'center',
  },
  previewFileText: {
    color: '#FFFFFF',
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
  previewFileSize: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginTop: 8,
  },
  previewActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
});

export default MediaGalleryScreen;
