.container {
  padding: 2rem 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #fff3c4 100%);
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="diamonds" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,2 18,10 10,18 2,10" fill="rgba(255,215,0,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23diamonds)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.title {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  color: rgba(0, 0, 0, 0.7);
  font-weight: 400;
  max-width: 600px;
  margin: 0 auto;
}

.balanceCard {
  margin-bottom: 3rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
  background: linear-gradient(135deg, #fff, rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  border: 2px solid rgba(255, 215, 0, 0.2);
}

.balanceContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
}

.balanceInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.balanceAmount {
  font-weight: 800;
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.buyButton {
  border-radius: 25px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  text-transform: none;
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
}

.buyButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.6);
}

.packagesSection,
.historySection {
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.sectionTitle {
  text-align: center;
  margin-bottom: 2rem;
  font-weight: 700;
  background: linear-gradient(45deg, #333, #666);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.packagesGrid {
  margin-bottom: 2rem;
}

.packageCard {
  height: 100%;
  border-radius: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.packageCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.3);
}

.popularPackage {
  border: 3px solid #ff6b35;
  transform: scale(1.05);
  box-shadow: 0 15px 35px rgba(255, 107, 53, 0.3);
}

.popularPackage:hover {
  transform: scale(1.05) translateY(-8px);
}

.popularBadge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  font-weight: bold;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
}

.bonusBadge {
  position: absolute;
  top: -10px;
  right: 1rem;
  background: linear-gradient(45deg, #4caf50, #8bc34a);
  color: white;
  font-weight: bold;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.packageContent {
  padding: 2rem;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.packageIcon {
  margin-bottom: 1rem;
  animation: sparkle 3s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(5deg);
  }
}

.packageAmount {
  margin-bottom: 0.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.packageDescription {
  margin-bottom: 1rem;
  flex: 1;
}

.packagePrice {
  margin-bottom: 0.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, #333, #666);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bonusAmount {
  color: #4caf50;
  font-weight: 600;
  margin-bottom: 1rem;
  background: rgba(76, 175, 80, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  display: inline-block;
}

.purchaseButton {
  margin-top: 1rem;
  border-radius: 25px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  text-transform: none;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.purchaseButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.filtersCard {
  margin-bottom: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.transactionsTable {
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.diamondAmount {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.loadingContainer {
  text-align: center;
  padding: 3rem;
  color: rgba(0, 0, 0, 0.7);
}

.errorAlert,
.emptyAlert {
  margin: 2rem 0;
  border-radius: 12px;
}

.purchaseContent {
  padding: 1rem 0;
}

.selectedPackage {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #fff3c4, #fff);
  border-radius: 12px;
  border: 2px solid rgba(255, 215, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0;
  }
  
  .header {
    margin-bottom: 1.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .balanceContent {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .packagesSection,
  .historySection {
    margin-bottom: 2rem;
  }
  
  .packageCard {
    margin-bottom: 1.5rem;
  }
  
  .popularPackage {
    transform: none;
  }
  
  .popularPackage:hover {
    transform: translateY(-4px);
  }
  
  .packageContent {
    padding: 1.5rem;
  }
  
  .packageAmount {
    font-size: 1.75rem;
  }
  
  .packagePrice {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem 0;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .packageContent {
    padding: 1rem;
  }
  
  .purchaseButton {
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
  }
  
  .selectedPackage {
    flex-direction: column;
    text-align: center;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .packageCard,
  .buyButton,
  .purchaseButton {
    transition: none;
  }
  
  .packageCard:hover,
  .buyButton:hover,
  .purchaseButton:hover {
    transform: none;
  }
  
  .packageIcon {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .packageCard {
    border: 2px solid #000;
  }
  
  .popularPackage {
    border-color: #000;
  }
  
  .balanceCard {
    border: 2px solid #000;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #2d2d2d 0%, #3d3d3d 50%, #4d4d4d 100%);
  }
  
  .balanceCard,
  .packageCard,
  .filtersCard,
  .transactionsTable {
    background: rgba(45, 45, 45, 0.95);
    color: #fff;
  }
  
  .selectedPackage {
    background: linear-gradient(135deg, #2d2d2d, #3d3d3d);
    border-color: #555;
  }
  
  .subtitle {
    color: rgba(255, 255, 255, 0.7);
  }
}

/* Special diamond effects */
.packageCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
  transition: left 0.5s;
}

.packageCard:hover::before {
  left: 100%;
}

.popularPackage::after {
  content: '💎';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Loading animation */
.loadingContainer svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Shine effect for diamond icons */
.packageIcon svg {
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
}

.balanceInfo svg {
  filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.7));
}
