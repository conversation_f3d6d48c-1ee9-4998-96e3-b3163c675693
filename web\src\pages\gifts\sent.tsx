import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  TextField,
  MenuItem,
  Pagination,
  Skeleton,
  Alert,
  Avatar,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Send,
  Visibility,
  Delete,
  FilterList,
  DateRange,
  Person,
  Diamond,
  CheckCircle,
  Schedule,
  Cancel,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { giftsService } from '../../services/giftsService';
import { SentGift } from '../../types/gifts.types';
import { formatDate, formatDateTime } from '../../utils/dateUtils';
import styles from './sent.module.css';

interface SentGiftsProps {}

const SentGifts: React.FC<SentGiftsProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState({
    status: 'all',
    dateRange: 'all',
    search: '',
  });
  const [page, setPage] = useState(1);

  // Загрузка отправленных подарков
  const {
    data: sentGiftsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['gifts', 'sent', filters, page],
    queryFn: () => giftsService.getSentGifts({ ...filters, page, limit: 12 }),
    enabled: isAuthenticated,
  });

  // Мутация для отзыва подарка
  const cancelGiftMutation = useMutation({
    mutationFn: (giftId: string) => giftsService.cancelSentGift(giftId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gifts', 'sent'] });
    },
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/gifts/sent');
    }
  }, [isAuthenticated, router]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleCancelGift = (giftId: string) => {
    if (window.confirm('Вы уверены, что хотите отозвать этот подарок?')) {
      cancelGiftMutation.mutate(giftId);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'success';
      case 'pending': return 'warning';
      case 'cancelled': return 'error';
      case 'viewed': return 'info';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'delivered': return 'Доставлен';
      case 'pending': return 'Ожидает';
      case 'cancelled': return 'Отменен';
      case 'viewed': return 'Просмотрен';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered': return <CheckCircle />;
      case 'pending': return <Schedule />;
      case 'cancelled': return <Cancel />;
      case 'viewed': return <Visibility />;
      default: return <Send />;
    }
  };

  const statusOptions = [
    { value: 'all', label: 'Все статусы' },
    { value: 'pending', label: 'Ожидают' },
    { value: 'delivered', label: 'Доставлены' },
    { value: 'viewed', label: 'Просмотрены' },
    { value: 'cancelled', label: 'Отменены' },
  ];

  const dateRangeOptions = [
    { value: 'all', label: 'За все время' },
    { value: 'today', label: 'Сегодня' },
    { value: 'week', label: 'За неделю' },
    { value: 'month', label: 'За месяц' },
    { value: 'year', label: 'За год' },
  ];

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Отправленные подарки - Likes & Love</title>
        <meta name="description" content="Просмотрите историю отправленных подарков и их статус доставки." />
        <meta name="keywords" content="подарки, отправленные, история, статус" />
        <meta property="og:title" content="Отправленные подарки - Likes & Love" />
        <meta property="og:description" content="Просмотрите историю отправленных подарков" />
        <meta property="og:type" content="website" />
      </Head>

      <Container maxWidth="xl" className={styles.container}>
        {/* Заголовок */}
        <Box className={styles.header}>
          <Typography variant="h3" component="h1" className={styles.title}>
            Отправленные подарки
          </Typography>
          <Typography variant="h6" color="text.secondary" className={styles.subtitle}>
            История ваших подарков и их статус доставки
          </Typography>
        </Box>

        {/* Фильтры */}
        <Card className={styles.filtersCard}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  placeholder="Поиск по получателю..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  size="small"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  fullWidth
                  label="Статус"
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  size="small"
                >
                  {statusOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  fullWidth
                  label="Период"
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  size="small"
                >
                  {dateRangeOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => router.push('/gifts/catalog')}
                  startIcon={<Send />}
                >
                  Отправить подарок
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Статистика */}
        <Grid container spacing={3} className={styles.statsGrid}>
          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent className={styles.statContent}>
                <Box className={styles.statIcon}>
                  <Send color="primary" />
                </Box>
                <Typography variant="h4" className={styles.statNumber}>
                  {sentGiftsData?.stats?.total || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Всего отправлено
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent className={styles.statContent}>
                <Box className={styles.statIcon}>
                  <CheckCircle color="success" />
                </Box>
                <Typography variant="h4" className={styles.statNumber}>
                  {sentGiftsData?.stats?.delivered || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Доставлено
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent className={styles.statContent}>
                <Box className={styles.statIcon}>
                  <Diamond color="warning" />
                </Box>
                <Typography variant="h4" className={styles.statNumber}>
                  {sentGiftsData?.stats?.totalSpent || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Потрачено 💎
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card className={styles.statCard}>
              <CardContent className={styles.statContent}>
                <Box className={styles.statIcon}>
                  <Person color="info" />
                </Box>
                <Typography variant="h4" className={styles.statNumber}>
                  {sentGiftsData?.stats?.uniqueRecipients || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Получателей
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Список подарков */}
        {isLoading ? (
          <Grid container spacing={3} className={styles.giftsGrid}>
            {Array.from({ length: 8 }).map((_, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Card>
                  <Skeleton variant="rectangular" height={120} />
                  <CardContent>
                    <Skeleton variant="text" height={24} />
                    <Skeleton variant="text" height={20} />
                    <Skeleton variant="text" height={20} width="60%" />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : error ? (
          <Alert severity="error" className={styles.errorAlert}>
            Ошибка загрузки отправленных подарков. Попробуйте обновить страницу.
          </Alert>
        ) : sentGiftsData?.gifts?.length === 0 ? (
          <Alert severity="info" className={styles.emptyAlert}>
            <Typography variant="h6" gutterBottom>
              У вас пока нет отправленных подарков
            </Typography>
            <Typography variant="body2" gutterBottom>
              Начните дарить подарки, чтобы показать свой интерес к другим пользователям.
            </Typography>
            <Button
              variant="contained"
              onClick={() => router.push('/gifts/catalog')}
              startIcon={<Send />}
              sx={{ mt: 2 }}
            >
              Выбрать подарок
            </Button>
          </Alert>
        ) : (
          <>
            <Grid container spacing={3} className={styles.giftsGrid}>
              {sentGiftsData?.gifts?.map((sentGift: SentGift) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={sentGift.id}>
                  <Card className={styles.giftCard}>
                    <Box className={styles.giftHeader}>
                      <img
                        src={sentGift.gift.image}
                        alt={sentGift.gift.name}
                        className={styles.giftImage}
                      />
                      <Chip
                        icon={getStatusIcon(sentGift.status)}
                        label={getStatusLabel(sentGift.status)}
                        color={getStatusColor(sentGift.status) as any}
                        size="small"
                        className={styles.statusChip}
                      />
                    </Box>

                    <CardContent className={styles.giftContent}>
                      <Typography variant="h6" className={styles.giftName}>
                        {sentGift.gift.name}
                      </Typography>

                      <Box className={styles.recipientInfo}>
                        <Avatar
                          src={sentGift.recipient.avatar}
                          sx={{ width: 32, height: 32 }}
                        >
                          {sentGift.recipient.firstName.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {sentGift.recipient.firstName} {sentGift.recipient.lastName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {sentGift.recipient.age} лет
                          </Typography>
                        </Box>
                      </Box>

                      <Divider sx={{ my: 1 }} />

                      <Box className={styles.giftDetails}>
                        <Box className={styles.giftPrice}>
                          <Diamond sx={{ color: 'warning.main', fontSize: 16 }} />
                          <Typography variant="body2" fontWeight="bold">
                            {sentGift.gift.price}
                          </Typography>
                        </Box>

                        <Typography variant="caption" color="text.secondary">
                          {formatDateTime(sentGift.sentAt)}
                        </Typography>
                      </Box>

                      {sentGift.message && (
                        <Typography variant="body2" className={styles.giftMessage}>
                          "{sentGift.message}"
                        </Typography>
                      )}

                      {sentGift.deliveredAt && (
                        <Typography variant="caption" color="success.main" className={styles.deliveryInfo}>
                          Доставлен: {formatDateTime(sentGift.deliveredAt)}
                        </Typography>
                      )}

                      <Box className={styles.giftActions}>
                        <Tooltip title="Просмотреть профиль">
                          <IconButton
                            size="small"
                            onClick={() => router.push(`/profile/${sentGift.recipient.id}`)}
                          >
                            <Visibility />
                          </IconButton>
                        </Tooltip>

                        {sentGift.status === 'pending' && (
                          <Tooltip title="Отозвать подарок">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleCancelGift(sentGift.id)}
                              disabled={cancelGiftMutation.isPending}
                            >
                              <Cancel />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* Пагинация */}
            {sentGiftsData?.totalPages > 1 && (
              <Box className={styles.paginationContainer}>
                <Pagination
                  count={sentGiftsData.totalPages}
                  page={page}
                  onChange={(_, newPage) => setPage(newPage)}
                  color="primary"
                  size="large"
                />
              </Box>
            )}
          </>
        )}
      </Container>
    </>
  );
};

export default SentGifts;
