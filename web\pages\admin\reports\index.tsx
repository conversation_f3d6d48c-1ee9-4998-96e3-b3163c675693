import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Grid,
  InputAdornment,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Visibility as ViewIcon,
  Assignment as AssignIcon,
  CheckCircle as ResolveIcon,
  Cancel as DismissIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Download as ExportIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AdminLayout } from '../../../components/admin/AdminLayout';
import { useAuth } from '../../../hooks/useAuth';
import { adminService } from '../../../services/adminService';
import { Report, ReportFilters, ReportListResponse } from '../../../types/admin.types';
import styles from '../../../styles/admin/Reports.module.css';

const ReportsManagement: React.FC = () => {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState<ReportFilters>({
    search: '',
    type: undefined,
    status: undefined,
    priority: undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(20);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [actionDialog, setActionDialog] = useState<{
    open: boolean;
    type: 'resolve' | 'dismiss' | 'assign';
    report: Report | null;
  }>({
    open: false,
    type: 'resolve',
    report: null,
  });
  const [resolution, setResolution] = useState('');
  const [actionTaken, setActionTaken] = useState<Report['actionTaken']>('no_action');
  const [assignedTo, setAssignedTo] = useState('');

  // Проверка прав доступа
  useEffect(() => {
    if (!isAdmin) {
      router.push('/');
    }
  }, [isAdmin, router]);

  // Загрузка отчетов
  const {
    data: reportsData,
    isLoading,
    error,
    refetch,
  } = useQuery<ReportListResponse>({
    queryKey: ['admin', 'reports', filters, page, rowsPerPage],
    queryFn: () => adminService.getReports(filters, { page: page + 1, limit: rowsPerPage }),
    enabled: isAdmin,
  });

  // Мутации для действий с отчетами
  const updateReportMutation = useMutation({
    mutationFn: ({ 
      reportId, 
      status, 
      resolution, 
      actionTaken 
    }: { 
      reportId: string; 
      status: Report['status']; 
      resolution?: string; 
      actionTaken?: Report['actionTaken']; 
    }) =>
      adminService.updateReportStatus(reportId, status, resolution, actionTaken),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'reports'] });
      setActionDialog({ open: false, type: 'resolve', report: null });
      setResolution('');
      setActionTaken('no_action');
    },
  });

  const assignReportMutation = useMutation({
    mutationFn: ({ reportId, assignedTo }: { reportId: string; assignedTo: string }) =>
      adminService.assignReport(reportId, assignedTo),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'reports'] });
      setActionDialog({ open: false, type: 'assign', report: null });
      setAssignedTo('');
    },
  });

  const handleFilterChange = (field: keyof ReportFilters, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPage(0);
  };

  const handleClearFilters = () => {
    setFilters({
      search: '',
      type: undefined,
      status: undefined,
      priority: undefined,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
    setPage(0);
  };

  const handleAction = (type: typeof actionDialog.type, report: Report) => {
    setActionDialog({ open: true, type, report });
  };

  const handleConfirmAction = () => {
    if (!actionDialog.report) return;

    switch (actionDialog.type) {
      case 'resolve':
        updateReportMutation.mutate({
          reportId: actionDialog.report.id,
          status: 'resolved',
          resolution,
          actionTaken,
        });
        break;
      case 'dismiss':
        updateReportMutation.mutate({
          reportId: actionDialog.report.id,
          status: 'dismissed',
          resolution,
          actionTaken: 'no_action',
        });
        break;
      case 'assign':
        if (assignedTo.trim()) {
          assignReportMutation.mutate({
            reportId: actionDialog.report.id,
            assignedTo,
          });
        }
        break;
    }
  };

  const getStatusChip = (status: Report['status']) => {
    const statusConfig = {
      pending: { label: 'Ожидает', color: 'warning' as const, icon: <WarningIcon /> },
      investigating: { label: 'Расследуется', color: 'info' as const, icon: <InfoIcon /> },
      resolved: { label: 'Решен', color: 'success' as const, icon: <CheckCircle /> },
      dismissed: { label: 'Отклонен', color: 'error' as const, icon: <Cancel /> },
    };

    const config = statusConfig[status];
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        icon={config.icon}
      />
    );
  };

  const getPriorityChip = (priority: Report['priority']) => {
    const priorityConfig = {
      low: { label: 'Низкий', color: 'default' as const },
      medium: { label: 'Средний', color: 'warning' as const },
      high: { label: 'Высокий', color: 'error' as const },
      critical: { label: 'Критический', color: 'error' as const },
    };

    const config = priorityConfig[priority];
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  const getTypeLabel = (type: Report['type']) => {
    const typeLabels = {
      inappropriate_content: 'Неподходящий контент',
      fake_profile: 'Фейковый профиль',
      harassment: 'Домогательства',
      spam: 'Спам',
      other: 'Другое',
    };
    return typeLabels[type];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ru-RU');
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Отчеты и жалобы - Админ-панель</title>
        <meta name="description" content="Управление отчетами и жалобами пользователей" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <AdminLayout>
        <Box className={styles.container}>
          {/* Заголовок */}
          <Box className={styles.header}>
            <Box>
              <Typography variant="h4" component="h1" className={styles.title}>
                Отчеты и жалобы
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Всего отчетов: {reportsData?.total || 0}
              </Typography>
            </Box>
            <Box className={styles.headerActions}>
              <Button
                variant="outlined"
                startIcon={<ExportIcon />}
                className={styles.actionButton}
              >
                Экспорт
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => refetch()}
                className={styles.actionButton}
              >
                Обновить
              </Button>
            </Box>
          </Box>

          {/* Статистика */}
          <Grid container spacing={3} className={styles.statsGrid}>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Ожидают рассмотрения
                  </Typography>
                  <Typography variant="h4" color="warning.main">
                    {reportsData?.reports.filter(r => r.status === 'pending').length || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    В расследовании
                  </Typography>
                  <Typography variant="h4" color="info.main">
                    {reportsData?.reports.filter(r => r.status === 'investigating').length || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Решены
                  </Typography>
                  <Typography variant="h4" color="success.main">
                    {reportsData?.reports.filter(r => r.status === 'resolved').length || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.statCard}>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Критические
                  </Typography>
                  <Typography variant="h4" color="error.main">
                    {reportsData?.reports.filter(r => r.priority === 'critical').length || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Фильтры */}
          <Card className={styles.filtersCard}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    placeholder="Поиск по описанию, пользователю..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Тип</InputLabel>
                    <Select
                      value={filters.type || ''}
                      onChange={(e) => handleFilterChange('type', e.target.value || undefined)}
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="inappropriate_content">Неподходящий контент</MenuItem>
                      <MenuItem value="fake_profile">Фейковый профиль</MenuItem>
                      <MenuItem value="harassment">Домогательства</MenuItem>
                      <MenuItem value="spam">Спам</MenuItem>
                      <MenuItem value="other">Другое</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Статус</InputLabel>
                    <Select
                      value={filters.status || ''}
                      onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="pending">Ожидает</MenuItem>
                      <MenuItem value="investigating">Расследуется</MenuItem>
                      <MenuItem value="resolved">Решен</MenuItem>
                      <MenuItem value="dismissed">Отклонен</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Приоритет</InputLabel>
                    <Select
                      value={filters.priority || ''}
                      onChange={(e) => handleFilterChange('priority', e.target.value || undefined)}
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="low">Низкий</MenuItem>
                      <MenuItem value="medium">Средний</MenuItem>
                      <MenuItem value="high">Высокий</MenuItem>
                      <MenuItem value="critical">Критический</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Сортировка</InputLabel>
                    <Select
                      value={filters.sortBy || 'createdAt'}
                      onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    >
                      <MenuItem value="createdAt">По дате создания</MenuItem>
                      <MenuItem value="updatedAt">По дате обновления</MenuItem>
                      <MenuItem value="priority">По приоритету</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={1}>
                  <Button
                    variant="outlined"
                    startIcon={<FilterIcon />}
                    onClick={handleClearFilters}
                    fullWidth
                  >
                    Сбросить
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Список отчетов */}
          <Card className={styles.reportsCard}>
            {isLoading && <LinearProgress />}
            
            {error && (
              <Alert severity="error" className={styles.errorAlert}>
                Ошибка загрузки отчетов: {error.message}
              </Alert>
            )}

            <Box className={styles.reportsList}>
              {reportsData?.reports.map((report) => (
                <Accordion key={report.id} className={styles.reportAccordion}>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    className={styles.reportSummary}
                  >
                    <Box className={styles.reportHeader}>
                      <Box className={styles.reportInfo}>
                        <Typography variant="h6" className={styles.reportTitle}>
                          {getTypeLabel(report.type)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          От: {report.reporterName} → На: {report.reportedUserName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(report.createdAt)}
                        </Typography>
                      </Box>
                      <Box className={styles.reportBadges}>
                        {getPriorityChip(report.priority)}
                        {getStatusChip(report.status)}
                      </Box>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails className={styles.reportDetails}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={8}>
                        <Typography variant="subtitle2" gutterBottom>
                          Причина:
                        </Typography>
                        <Typography variant="body2" paragraph>
                          {report.reason}
                        </Typography>
                        
                        {report.description && (
                          <>
                            <Typography variant="subtitle2" gutterBottom>
                              Описание:
                            </Typography>
                            <Typography variant="body2" paragraph>
                              {report.description}
                            </Typography>
                          </>
                        )}

                        {report.resolution && (
                          <>
                            <Typography variant="subtitle2" gutterBottom>
                              Решение:
                            </Typography>
                            <Typography variant="body2" paragraph>
                              {report.resolution}
                            </Typography>
                          </>
                        )}
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Box className={styles.reportActions}>
                          <Button
                            variant="outlined"
                            startIcon={<ViewIcon />}
                            onClick={() => router.push(`/admin/reports/${report.id}`)}
                            fullWidth
                            className={styles.actionButton}
                          >
                            Подробнее
                          </Button>
                          
                          {report.status === 'pending' && (
                            <>
                              <Button
                                variant="contained"
                                startIcon={<ResolveIcon />}
                                onClick={() => handleAction('resolve', report)}
                                fullWidth
                                className={styles.actionButton}
                                color="success"
                              >
                                Решить
                              </Button>
                              <Button
                                variant="outlined"
                                startIcon={<DismissIcon />}
                                onClick={() => handleAction('dismiss', report)}
                                fullWidth
                                className={styles.actionButton}
                                color="error"
                              >
                                Отклонить
                              </Button>
                              <Button
                                variant="outlined"
                                startIcon={<AssignIcon />}
                                onClick={() => handleAction('assign', report)}
                                fullWidth
                                className={styles.actionButton}
                              >
                                Назначить
                              </Button>
                            </>
                          )}
                        </Box>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Box>

            {reportsData && (
              <TablePagination
                component="div"
                count={reportsData.total}
                page={page}
                onPageChange={(_, newPage) => setPage(newPage)}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={(e) => {
                  setRowsPerPage(parseInt(e.target.value, 10));
                  setPage(0);
                }}
                rowsPerPageOptions={[10, 20, 50]}
                labelRowsPerPage="Отчетов на странице:"
                labelDisplayedRows={({ from, to, count }) => 
                  `${from}-${to} из ${count !== -1 ? count : `более ${to}`}`
                }
              />
            )}
          </Card>

          {/* Диалог действий */}
          <Dialog
            open={actionDialog.open}
            onClose={() => setActionDialog({ open: false, type: 'resolve', report: null })}
            maxWidth="md"
            fullWidth
          >
            <DialogTitle>
              {actionDialog.type === 'resolve' && 'Решить отчет'}
              {actionDialog.type === 'dismiss' && 'Отклонить отчет'}
              {actionDialog.type === 'assign' && 'Назначить отчет'}
            </DialogTitle>
            <DialogContent>
              {actionDialog.report && (
                <Box className={styles.dialogContent}>
                  <Typography variant="body1" gutterBottom>
                    <strong>Отчет:</strong> {getTypeLabel(actionDialog.report.type)}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    <strong>От:</strong> {actionDialog.report.reporterName}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    <strong>На:</strong> {actionDialog.report.reportedUserName}
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>Причина:</strong> {actionDialog.report.reason}
                  </Typography>

                  {actionDialog.type === 'assign' ? (
                    <TextField
                      fullWidth
                      label="Назначить администратору"
                      value={assignedTo}
                      onChange={(e) => setAssignedTo(e.target.value)}
                      margin="normal"
                      required
                    />
                  ) : (
                    <>
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        label="Решение/Комментарий"
                        value={resolution}
                        onChange={(e) => setResolution(e.target.value)}
                        margin="normal"
                        required
                      />
                      
                      {actionDialog.type === 'resolve' && (
                        <FormControl fullWidth margin="normal">
                          <InputLabel>Принятые меры</InputLabel>
                          <Select
                            value={actionTaken}
                            onChange={(e) => setActionTaken(e.target.value as Report['actionTaken'])}
                          >
                            <MenuItem value="no_action">Никаких мер</MenuItem>
                            <MenuItem value="warning">Предупреждение</MenuItem>
                            <MenuItem value="temporary_ban">Временная блокировка</MenuItem>
                            <MenuItem value="permanent_ban">Постоянная блокировка</MenuItem>
                            <MenuItem value="content_removal">Удаление контента</MenuItem>
                          </Select>
                        </FormControl>
                      )}
                    </>
                  )}
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setActionDialog({ open: false, type: 'resolve', report: null })}
              >
                Отмена
              </Button>
              <Button
                onClick={handleConfirmAction}
                variant="contained"
                disabled={
                  (actionDialog.type !== 'assign' && !resolution.trim()) ||
                  (actionDialog.type === 'assign' && !assignedTo.trim()) ||
                  updateReportMutation.isPending ||
                  assignReportMutation.isPending
                }
              >
                {actionDialog.type === 'resolve' && 'Решить'}
                {actionDialog.type === 'dismiss' && 'Отклонить'}
                {actionDialog.type === 'assign' && 'Назначить'}
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </AdminLayout>
    </>
  );
};

export default ReportsManagement;
