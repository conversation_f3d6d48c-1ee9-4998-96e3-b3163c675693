import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  TextField,
  MenuItem,
  Slider,
  FormControl,
  FormLabel,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Pagination,
  Skeleton,
  Alert,
  Avatar,
  IconButton,
  Tooltip,
  Fab,
  Drawer,
  AppBar,
  Toolbar,
} from '@mui/material';
import {
  Search,
  FilterList,
  Favorite,
  FavoriteBorder,
  Message,
  LocationOn,
  Work,
  School,
  Star,
  Close,
  Tune,
  Person,
  Visibility,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { discoverService } from '../../services/discoverService';
import { SearchFilters, UserProfile } from '../../types/discover.types';
import { calculateAge } from '../../utils/dateUtils';
import styles from './search.module.css';

interface DiscoverSearchProps {}

const DiscoverSearch: React.FC<DiscoverSearchProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState<SearchFilters>({
    ageRange: [18, 50],
    distance: 50,
    interests: [],
    education: '',
    occupation: '',
    location: '',
    hasPhotos: true,
    isOnline: false,
    isPremium: false,
  });
  const [page, setPage] = useState(1);
  const [filtersDrawerOpen, setFiltersDrawerOpen] = useState(false);

  // Загрузка пользователей
  const {
    data: usersData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['discover', 'search', filters, page],
    queryFn: () => discoverService.searchUsers({ ...filters, page, limit: 12 }),
    enabled: isAuthenticated,
  });

  // Загрузка интересов для фильтра
  const {
    data: availableInterests,
  } = useQuery({
    queryKey: ['discover', 'interests'],
    queryFn: () => discoverService.getAvailableInterests(),
  });

  // Мутация для лайка
  const likeMutation = useMutation({
    mutationFn: (userId: string) => discoverService.likeUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['discover', 'search'] });
    },
  });

  // Мутация для суперлайка
  const superLikeMutation = useMutation({
    mutationFn: (userId: string) => discoverService.superLikeUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['discover', 'search'] });
    },
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/discover/search');
    }
  }, [isAuthenticated, router]);

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleLike = (userId: string) => {
    likeMutation.mutate(userId);
  };

  const handleSuperLike = (userId: string) => {
    superLikeMutation.mutate(userId);
  };

  const handleViewProfile = (userId: string) => {
    router.push(`/profile/${userId}`);
  };

  const handleSendMessage = (userId: string) => {
    router.push(`/chat/${userId}`);
  };

  const resetFilters = () => {
    setFilters({
      ageRange: [18, 50],
      distance: 50,
      interests: [],
      education: '',
      occupation: '',
      location: '',
      hasPhotos: true,
      isOnline: false,
      isPremium: false,
    });
    setPage(1);
  };

  const educationOptions = [
    { value: '', label: 'Любое образование' },
    { value: 'high_school', label: 'Среднее' },
    { value: 'bachelor', label: 'Бакалавр' },
    { value: 'master', label: 'Магистр' },
    { value: 'phd', label: 'Кандидат наук' },
  ];

  const FiltersContent = () => (
    <Box className={styles.filtersContent}>
      <Typography variant="h6" gutterBottom>
        Фильтры поиска
      </Typography>

      {/* Возраст */}
      <FormControl fullWidth sx={{ mb: 3 }}>
        <FormLabel>Возраст: {filters.ageRange[0]} - {filters.ageRange[1]} лет</FormLabel>
        <Slider
          value={filters.ageRange}
          onChange={(_, value) => handleFilterChange('ageRange', value)}
          valueLabelDisplay="auto"
          min={18}
          max={80}
          marks={[
            { value: 18, label: '18' },
            { value: 30, label: '30' },
            { value: 50, label: '50' },
            { value: 80, label: '80' },
          ]}
        />
      </FormControl>

      {/* Расстояние */}
      <FormControl fullWidth sx={{ mb: 3 }}>
        <FormLabel>Расстояние: {filters.distance} км</FormLabel>
        <Slider
          value={filters.distance}
          onChange={(_, value) => handleFilterChange('distance', value)}
          valueLabelDisplay="auto"
          min={1}
          max={200}
          marks={[
            { value: 1, label: '1км' },
            { value: 50, label: '50км' },
            { value: 100, label: '100км' },
            { value: 200, label: '200км' },
          ]}
        />
      </FormControl>

      {/* Местоположение */}
      <TextField
        fullWidth
        label="Город"
        value={filters.location}
        onChange={(e) => handleFilterChange('location', e.target.value)}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: <LocationOn sx={{ mr: 1, color: 'action.active' }} />,
        }}
      />

      {/* Образование */}
      <TextField
        select
        fullWidth
        label="Образование"
        value={filters.education}
        onChange={(e) => handleFilterChange('education', e.target.value)}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: <School sx={{ mr: 1, color: 'action.active' }} />,
        }}
      >
        {educationOptions.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </TextField>

      {/* Профессия */}
      <TextField
        fullWidth
        label="Профессия"
        value={filters.occupation}
        onChange={(e) => handleFilterChange('occupation', e.target.value)}
        sx={{ mb: 3 }}
        InputProps={{
          startAdornment: <Work sx={{ mr: 1, color: 'action.active' }} />,
        }}
      />

      {/* Интересы */}
      <FormControl fullWidth sx={{ mb: 3 }}>
        <FormLabel>Интересы</FormLabel>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
          {availableInterests?.map((interest) => (
            <Chip
              key={interest}
              label={interest}
              clickable
              color={filters.interests.includes(interest) ? 'primary' : 'default'}
              onClick={() => {
                const newInterests = filters.interests.includes(interest)
                  ? filters.interests.filter(i => i !== interest)
                  : [...filters.interests, interest];
                handleFilterChange('interests', newInterests);
              }}
            />
          ))}
        </Box>
      </FormControl>

      {/* Дополнительные фильтры */}
      <FormGroup>
        <FormControlLabel
          control={
            <Checkbox
              checked={filters.hasPhotos}
              onChange={(e) => handleFilterChange('hasPhotos', e.target.checked)}
            />
          }
          label="Только с фотографиями"
        />
        <FormControlLabel
          control={
            <Checkbox
              checked={filters.isOnline}
              onChange={(e) => handleFilterChange('isOnline', e.target.checked)}
            />
          }
          label="Только онлайн"
        />
        <FormControlLabel
          control={
            <Checkbox
              checked={filters.isPremium}
              onChange={(e) => handleFilterChange('isPremium', e.target.checked)}
            />
          }
          label="Только Premium пользователи"
        />
      </FormGroup>

      <Button
        variant="outlined"
        fullWidth
        onClick={resetFilters}
        sx={{ mt: 2 }}
      >
        Сбросить фильтры
      </Button>
    </Box>
  );

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Поиск знакомств - Likes & Love</title>
        <meta name="description" content="Найдите идеального партнера с помощью умного поиска по интересам, возрасту и местоположению." />
        <meta name="keywords" content="поиск, знакомства, фильтры, партнер" />
        <meta property="og:title" content="Поиск знакомств - Likes & Love" />
        <meta property="og:description" content="Найдите идеального партнера" />
        <meta property="og:type" content="website" />
      </Head>

      <Container maxWidth="xl" className={styles.container}>
        {/* Заголовок */}
        <Box className={styles.header}>
          <Typography variant="h3" component="h1" className={styles.title}>
            Поиск знакомств
          </Typography>
          <Typography variant="h6" color="text.secondary" className={styles.subtitle}>
            Найдите идеального партнера с помощью умных фильтров
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Фильтры (десктоп) */}
          <Grid item xs={12} lg={3} className={styles.filtersDesktop}>
            <Card className={styles.filtersCard}>
              <CardContent>
                <FiltersContent />
              </CardContent>
            </Card>
          </Grid>

          {/* Результаты поиска */}
          <Grid item xs={12} lg={9}>
            {/* Статистика */}
            <Box className={styles.resultsHeader}>
              <Typography variant="h6">
                {usersData?.total ? `Найдено ${usersData.total} пользователей` : 'Поиск...'}
              </Typography>
              
              <Button
                variant="outlined"
                startIcon={<Tune />}
                onClick={() => setFiltersDrawerOpen(true)}
                className={styles.filtersButtonMobile}
              >
                Фильтры
              </Button>
            </Box>

            {/* Список пользователей */}
            {isLoading ? (
              <Grid container spacing={3}>
                {Array.from({ length: 12 }).map((_, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Card>
                      <Skeleton variant="rectangular" height={300} />
                      <CardContent>
                        <Skeleton variant="text" height={24} />
                        <Skeleton variant="text" height={20} width="60%" />
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            ) : error ? (
              <Alert severity="error" className={styles.errorAlert}>
                Ошибка загрузки пользователей. Попробуйте обновить страницу.
              </Alert>
            ) : usersData?.users?.length === 0 ? (
              <Alert severity="info" className={styles.emptyAlert}>
                <Typography variant="h6" gutterBottom>
                  Пользователи не найдены
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Попробуйте изменить фильтры поиска или расширить критерии.
                </Typography>
                <Button
                  variant="contained"
                  onClick={resetFilters}
                  sx={{ mt: 2 }}
                >
                  Сбросить фильтры
                </Button>
              </Alert>
            ) : (
              <>
                <Grid container spacing={3} className={styles.usersGrid}>
                  {usersData?.users?.map((userProfile: UserProfile) => (
                    <Grid item xs={12} sm={6} md={4} key={userProfile.id}>
                      <Card className={styles.userCard}>
                        <Box className={styles.userImageContainer}>
                          <CardMedia
                            component="img"
                            height="300"
                            image={userProfile.photos[0]?.url || '/default-avatar.png'}
                            alt={`${userProfile.firstName} ${userProfile.lastName}`}
                            className={styles.userImage}
                          />
                          
                          {userProfile.isOnline && (
                            <Chip
                              label="Онлайн"
                              color="success"
                              size="small"
                              className={styles.onlineBadge}
                            />
                          )}
                          
                          {userProfile.isPremium && (
                            <Chip
                              icon={<Star />}
                              label="Premium"
                              color="warning"
                              size="small"
                              className={styles.premiumBadge}
                            />
                          )}

                          <Box className={styles.userActions}>
                            <Tooltip title="Суперлайк">
                              <IconButton
                                className={styles.superLikeButton}
                                onClick={() => handleSuperLike(userProfile.id)}
                                disabled={superLikeMutation.isPending}
                              >
                                <Star />
                              </IconButton>
                            </Tooltip>
                            
                            <Tooltip title="Лайк">
                              <IconButton
                                className={styles.likeButton}
                                onClick={() => handleLike(userProfile.id)}
                                disabled={likeMutation.isPending}
                                color={userProfile.isLiked ? 'error' : 'default'}
                              >
                                {userProfile.isLiked ? <Favorite /> : <FavoriteBorder />}
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>

                        <CardContent className={styles.userContent}>
                          <Typography variant="h6" className={styles.userName}>
                            {userProfile.firstName} {userProfile.lastName}
                          </Typography>
                          
                          <Typography variant="body2" color="text.secondary" className={styles.userAge}>
                            {calculateAge(userProfile.birthDate)} лет
                          </Typography>

                          <Box className={styles.userLocation}>
                            <LocationOn sx={{ fontSize: 16, mr: 0.5 }} />
                            <Typography variant="body2" color="text.secondary">
                              {userProfile.location}
                            </Typography>
                          </Box>

                          {userProfile.occupation && (
                            <Box className={styles.userOccupation}>
                              <Work sx={{ fontSize: 16, mr: 0.5 }} />
                              <Typography variant="body2" color="text.secondary">
                                {userProfile.occupation}
                              </Typography>
                            </Box>
                          )}

                          {userProfile.interests && userProfile.interests.length > 0 && (
                            <Box className={styles.userInterests}>
                              {userProfile.interests.slice(0, 3).map((interest, index) => (
                                <Chip
                                  key={index}
                                  label={interest}
                                  size="small"
                                  className={styles.interestChip}
                                />
                              ))}
                              {userProfile.interests.length > 3 && (
                                <Typography variant="caption" color="text.secondary">
                                  +{userProfile.interests.length - 3} еще
                                </Typography>
                              )}
                            </Box>
                          )}

                          <Box className={styles.userCardActions}>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => handleViewProfile(userProfile.id)}
                              startIcon={<Visibility />}
                            >
                              Профиль
                            </Button>
                            
                            <Button
                              variant="contained"
                              size="small"
                              onClick={() => handleSendMessage(userProfile.id)}
                              startIcon={<Message />}
                            >
                              Написать
                            </Button>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>

                {/* Пагинация */}
                {usersData?.totalPages > 1 && (
                  <Box className={styles.paginationContainer}>
                    <Pagination
                      count={usersData.totalPages}
                      page={page}
                      onChange={(_, newPage) => setPage(newPage)}
                      color="primary"
                      size="large"
                    />
                  </Box>
                )}
              </>
            )}
          </Grid>
        </Grid>

        {/* Мобильные фильтры */}
        <Drawer
          anchor="bottom"
          open={filtersDrawerOpen}
          onClose={() => setFiltersDrawerOpen(false)}
          className={styles.filtersDrawer}
        >
          <AppBar position="static" color="default" elevation={0}>
            <Toolbar>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Фильтры поиска
              </Typography>
              <IconButton onClick={() => setFiltersDrawerOpen(false)}>
                <Close />
              </IconButton>
            </Toolbar>
          </AppBar>
          
          <Box sx={{ p: 2, maxHeight: '70vh', overflow: 'auto' }}>
            <FiltersContent />
          </Box>
        </Drawer>
      </Container>
    </>
  );
};

export default DiscoverSearch;
