import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
  StatusBar,
  Animated,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface TwoFactorScreenProps {
  navigation: any;
  route: any;
}

interface RouteParams {
  email?: string;
  userId?: string;
  isSetup?: boolean; // true если настройка 2FA, false если вход
}

const TwoFactorScreen: React.FC<TwoFactorScreenProps> = ({ navigation }) => {
  const route = useRoute();
  const { email, userId, isSetup = false } = (route.params as RouteParams) || {};
  const { theme } = useTheme();
  const { verify2FA, setup2FA, generateBackupCodes } = useAuth();

  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState('');

  // Анимации
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    // Анимация появления
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Если это настройка 2FA, генерируем QR код
    if (isSetup) {
      generateQRCode();
    }
  }, [isSetup]);

  const generateQRCode = async () => {
    try {
      const result = await setup2FA(userId || '');
      if (result.success) {
        setQrCodeUrl(result.qrCodeUrl || '');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось сгенерировать QR код');
    }
  };

  const handleVerify2FA = async () => {
    if (!verificationCode.trim()) {
      Alert.alert('Ошибка', 'Пожалуйста, введите код из приложения');
      return;
    }

    if (verificationCode.length !== 6) {
      Alert.alert('Ошибка', 'Код должен содержать 6 цифр');
      return;
    }

    setIsLoading(true);

    try {
      const result = await verify2FA({
        userId: userId || '',
        code: verificationCode,
        isSetup,
      });

      if (result.success) {
        if (isSetup) {
          // Генерируем резервные коды
          const backupResult = await generateBackupCodes(userId || '');
          if (backupResult.success) {
            setBackupCodes(backupResult.codes || []);
            setShowBackupCodes(true);
          } else {
            Alert.alert(
              'Успешно!',
              'Двухфакторная аутентификация настроена!',
              [
                {
                  text: 'Продолжить',
                  onPress: () => navigation.navigate('MainApp'),
                },
              ]
            );
          }
        } else {
          Alert.alert(
            'Успешно!',
            'Добро пожаловать в Likes Love!',
            [
              {
                text: 'Продолжить',
                onPress: () => navigation.navigate('MainApp'),
              },
            ]
          );
        }
      } else {
        Alert.alert('Ошибка', result.message || 'Неверный код');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при проверке кода');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackupCodesConfirmed = () => {
    Alert.alert(
      'Успешно!',
      'Двухфакторная аутентификация настроена! Сохраните резервные коды в безопасном месте.',
      [
        {
          text: 'Продолжить',
          onPress: () => navigation.navigate('MainApp'),
        },
      ]
    );
  };

  if (showBackupCodes) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
        <LinearGradient
          colors={['#FF6B9D', '#4ECDC4']}
          style={styles.background}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <View style={styles.backupCodesContainer}>
              <Icon name="security" size={80} color="#FFFFFF" />
              <Text style={styles.title}>Резервные коды</Text>
              <Text style={styles.subtitle}>
                Сохраните эти коды в безопасном месте. Они помогут восстановить доступ, если вы потеряете устройство.
              </Text>

              <View style={styles.codesContainer}>
                {backupCodes.map((code, index) => (
                  <View key={index} style={styles.codeItem}>
                    <Text style={styles.codeText}>{code}</Text>
                  </View>
                ))}
              </View>

              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleBackupCodesConfirmed}
              >
                <View style={styles.buttonContent}>
                  <Text style={styles.confirmButtonText}>Я сохранил коды</Text>
                </View>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </LinearGradient>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Animated.View
              style={[
                styles.content,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {/* Иконка */}
              <View style={styles.iconContainer}>
                <Icon name="security" size={80} color="#FFFFFF" />
              </View>

              {/* Заголовок */}
              <Text style={styles.title}>
                {isSetup ? 'Настройка 2FA' : 'Двухфакторная аутентификация'}
              </Text>
              <Text style={styles.subtitle}>
                {isSetup
                  ? 'Отсканируйте QR код в приложении аутентификатора и введите код'
                  : 'Введите код из приложения аутентификатора'}
              </Text>

              {/* QR код для настройки */}
              {isSetup && qrCodeUrl && (
                <View style={styles.qrContainer}>
                  {/* Здесь должен быть QR код */}
                  <View style={styles.qrPlaceholder}>
                    <Icon name="qr-code" size={120} color="#FFFFFF" />
                    <Text style={styles.qrText}>QR код для сканирования</Text>
                  </View>
                </View>
              )}

              {/* Поле ввода кода */}
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.codeInput}
                  placeholder="Введите 6-значный код"
                  placeholderTextColor="rgba(255, 255, 255, 0.7)"
                  value={verificationCode}
                  onChangeText={setVerificationCode}
                  keyboardType="numeric"
                  maxLength={6}
                  autoFocus={!isSetup}
                  textAlign="center"
                />
              </View>

              {/* Кнопка подтверждения */}
              <TouchableOpacity
                style={styles.verifyButton}
                onPress={handleVerify2FA}
                disabled={isLoading}
              >
                <View style={styles.buttonContent}>
                  {isLoading ? (
                    <ActivityIndicator color="#FF6B9D" size="small" />
                  ) : (
                    <Text style={styles.verifyButtonText}>
                      {isSetup ? 'Настроить 2FA' : 'Подтвердить'}
                    </Text>
                  )}
                </View>
              </TouchableOpacity>

              {/* Информация о приложениях */}
              {isSetup && (
                <View style={styles.appsContainer}>
                  <Text style={styles.appsTitle}>Рекомендуемые приложения:</Text>
                  <Text style={styles.appsText}>
                    • Google Authenticator{'\n'}
                    • Microsoft Authenticator{'\n'}
                    • Authy{'\n'}
                    • 1Password
                  </Text>
                </View>
              )}

              {/* Кнопка назад */}
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Icon name="arrow-back" size={24} color="#FFFFFF" />
                <Text style={styles.backButtonText}>Назад</Text>
              </TouchableOpacity>
            </Animated.View>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingVertical: 40,
  },
  iconContainer: {
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  qrContainer: {
    marginBottom: 30,
  },
  qrPlaceholder: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 15,
    padding: 30,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  qrText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginTop: 10,
  },
  inputContainer: {
    width: '100%',
    marginBottom: 30,
  },
  codeInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 20,
    paddingHorizontal: 20,
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 8,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  verifyButton: {
    width: '100%',
    marginBottom: 30,
  },
  buttonContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  verifyButtonText: {
    color: '#FF6B9D',
    fontSize: 18,
    fontWeight: 'bold',
  },
  appsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 15,
    padding: 20,
    marginBottom: 30,
    width: '100%',
  },
  appsTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  appsText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    lineHeight: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
  // Стили для резервных кодов
  backupCodesContainer: {
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingVertical: 40,
  },
  codesContainer: {
    width: '100%',
    marginBottom: 30,
  },
  codeItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  codeText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    letterSpacing: 2,
  },
  confirmButton: {
    width: '100%',
  },
  confirmButtonText: {
    color: '#FF6B9D',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default TwoFactorScreen;
