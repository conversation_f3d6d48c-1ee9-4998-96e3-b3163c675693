import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  Dimensions,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { eventsService } from '../../services/eventsService';
import { useAuth } from '../../hooks/useAuth';
import { Event, EventParticipant } from '../../types/events.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface EventDetailScreenProps {}

const EventDetailScreen: React.FC<EventDetailScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const { eventId } = route.params as { eventId: string };
  const [showFullDescription, setShowFullDescription] = useState(false);

  // Загрузка деталей события
  const {
    data: event,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['event', eventId],
    queryFn: () => eventsService.getEventDetails(eventId),
  });

  // Мутация для участия в событии
  const joinEventMutation = useMutation({
    mutationFn: () => eventsService.joinEvent(eventId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['event', eventId] });
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  // Мутация для выхода из события
  const leaveEventMutation = useMutation({
    mutationFn: () => eventsService.leaveEvent(eventId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['event', eventId] });
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  // Мутация для отмены события
  const cancelEventMutation = useMutation({
    mutationFn: () => eventsService.cancelEvent(eventId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['event', eventId] });
      queryClient.invalidateQueries({ queryKey: ['events'] });
      navigation.goBack();
    },
  });

  const handleJoinEvent = () => {
    Alert.alert(
      'Присоединиться к событию',
      'Вы уверены, что хотите присоединиться к этому событию?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Присоединиться',
          onPress: () => joinEventMutation.mutate(),
        },
      ]
    );
  };

  const handleLeaveEvent = () => {
    Alert.alert(
      'Покинуть событие',
      'Вы уверены, что хотите покинуть это событие?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Покинуть',
          style: 'destructive',
          onPress: () => leaveEventMutation.mutate(),
        },
      ]
    );
  };

  const handleCancelEvent = () => {
    Alert.alert(
      'Отменить событие',
      'Вы уверены, что хотите отменить это событие? Это действие нельзя отменить.',
      [
        { text: 'Нет', style: 'cancel' },
        {
          text: 'Отменить событие',
          style: 'destructive',
          onPress: () => cancelEventMutation.mutate(),
        },
      ]
    );
  };

  const handleShareEvent = async () => {
    if (!event) return;

    try {
      await Share.share({
        message: `Присоединяйтесь к событию "${event.title}"!\n\nДата: ${formatDate(event.dateTime)}\nВремя: ${formatTime(event.dateTime)}\nМесто: ${event.location.name}\n\nПодробности в приложении Likes & Love`,
        title: event.title,
      });
    } catch (error) {
      console.error('Error sharing event:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'party':
        return 'musical-notes-outline';
      case 'sport':
        return 'fitness-outline';
      case 'culture':
        return 'library-outline';
      case 'food':
        return 'restaurant-outline';
      case 'outdoor':
        return 'leaf-outline';
      default:
        return 'calendar-outline';
    }
  };

  const isParticipant = event?.participants.some(p => p.id === user?.id);
  const isOrganizer = event?.organizerId === user?.id;
  const canJoin = event && !isParticipant && event.participants.length < event.maxParticipants;
  const isUpcoming = event && new Date(event.dateTime) > new Date();

  const renderParticipant = (participant: EventParticipant, index: number) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.8, 1]),
          },
        ],
      };
    });

    return (
      <Animated.View key={participant.id} style={[styles.participantCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('UserProfile', { userId: participant.id })}
          activeOpacity={0.8}
        >
          <Image source={{ uri: participant.avatar }} style={styles.participantAvatar} />
          <Text style={styles.participantName}>{participant.name}</Text>
          {participant.id === event?.organizerId && (
            <View style={styles.organizerBadge}>
              <Text style={styles.organizerBadgeText}>Организатор</Text>
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Загружаем детали события...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !event) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить детали события
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Детали события</Text>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleShareEvent}
          >
            <Ionicons name="share-outline" size={24} color={colors.text} />
          </TouchableOpacity>
          
          {isOrganizer && (
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => navigation.navigate('EditEvent', { eventId })}
            >
              <Ionicons name="create-outline" size={24} color={colors.text} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Изображение события */}
        <View style={styles.imageContainer}>
          <Image source={{ uri: event.image }} style={styles.eventImage} />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.imageGradient}
          />
          
          {/* Тип события */}
          <View style={styles.eventTypeBadge}>
            <Ionicons
              name={getEventTypeIcon(event.type) as any}
              size={20}
              color={colors.white}
            />
            <Text style={styles.eventTypeText}>{event.type}</Text>
          </View>
          
          {/* Цена */}
          {event.price > 0 && (
            <View style={styles.priceBadge}>
              <Text style={styles.priceText}>{event.price}₽</Text>
            </View>
          )}
        </View>

        {/* Основная информация */}
        <View style={styles.mainInfo}>
          <Text style={styles.eventTitle}>{event.title}</Text>
          
          <View style={styles.eventMeta}>
            <View style={styles.metaItem}>
              <Ionicons name="calendar-outline" size={20} color={colors.primary} />
              <Text style={styles.metaText}>
                {formatDate(event.dateTime)} в {formatTime(event.dateTime)}
              </Text>
            </View>
            
            <View style={styles.metaItem}>
              <Ionicons name="location-outline" size={20} color={colors.primary} />
              <Text style={styles.metaText}>{event.location.name}</Text>
            </View>
            
            <View style={styles.metaItem}>
              <Ionicons name="people-outline" size={20} color={colors.primary} />
              <Text style={styles.metaText}>
                {event.participants.length}/{event.maxParticipants} участников
              </Text>
            </View>
          </View>
        </View>

        {/* Описание */}
        {event.description && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Описание</Text>
            <Text
              style={styles.descriptionText}
              numberOfLines={showFullDescription ? undefined : 3}
            >
              {event.description}
            </Text>
            {event.description.length > 150 && (
              <TouchableOpacity
                style={styles.showMoreButton}
                onPress={() => setShowFullDescription(!showFullDescription)}
              >
                <Text style={styles.showMoreText}>
                  {showFullDescription ? 'Скрыть' : 'Показать полностью'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Карта */}
        {event.location.coordinates && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Место проведения</Text>
            <View style={styles.mapContainer}>
              <MapView
                provider={PROVIDER_GOOGLE}
                style={styles.map}
                region={{
                  latitude: event.location.coordinates.latitude,
                  longitude: event.location.coordinates.longitude,
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.01,
                }}
                scrollEnabled={false}
                zoomEnabled={false}
              >
                <Marker
                  coordinate={{
                    latitude: event.location.coordinates.latitude,
                    longitude: event.location.coordinates.longitude,
                  }}
                  title={event.location.name}
                />
              </MapView>
              <TouchableOpacity
                style={styles.mapOverlay}
                onPress={() => navigation.navigate('EventMap', { eventId })}
              >
                <Text style={styles.mapOverlayText}>Открыть карту</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Участники */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Участники ({event.participants.length})
          </Text>
          
          <View style={styles.participantsGrid}>
            {event.participants.map(renderParticipant)}
            
            {canJoin && (
              <View style={styles.joinSlot}>
                <TouchableOpacity
                  style={styles.joinButton}
                  onPress={handleJoinEvent}
                  disabled={joinEventMutation.isPending}
                >
                  <Ionicons name="add" size={32} color={colors.primary} />
                  <Text style={styles.joinButtonText}>Присоединиться</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Нижние действия */}
      {isUpcoming && (
        <View style={styles.bottomActions}>
          {isParticipant && !isOrganizer && (
            <TouchableOpacity
              style={[styles.actionButton, styles.leaveButton]}
              onPress={handleLeaveEvent}
              disabled={leaveEventMutation.isPending}
            >
              <Ionicons name="exit-outline" size={20} color={colors.error} />
              <Text style={[styles.actionButtonText, { color: colors.error }]}>
                Покинуть
              </Text>
            </TouchableOpacity>
          )}
          
          {isOrganizer && (
            <TouchableOpacity
              style={[styles.actionButton, styles.cancelButton]}
              onPress={handleCancelEvent}
              disabled={cancelEventMutation.isPending}
            >
              <Ionicons name="trash-outline" size={20} color={colors.error} />
              <Text style={[styles.actionButtonText, { color: colors.error }]}>
                Отменить событие
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, styles.chatButton]}
            onPress={() => navigation.navigate('EventChat', { eventId })}
          >
            <Ionicons name="chatbubble-outline" size={20} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>
              Чат события
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Индикатор загрузки */}
      {(joinEventMutation.isPending || leaveEventMutation.isPending || cancelEventMutation.isPending) && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
  },
  eventImage: {
    width: '100%',
    height: 250,
    resizeMode: 'cover',
  },
  imageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  eventTypeBadge: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    gap: spacing.sm,
  },
  eventTypeText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
    textTransform: 'capitalize',
  },
  priceBadge: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    backgroundColor: colors.success,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  },
  priceText: {
    fontSize: typography.sizes.md,
    color: colors.white,
    fontWeight: typography.weights.bold,
  },
  mainInfo: {
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  eventTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  eventMeta: {
    gap: spacing.md,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  metaText: {
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  section: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  descriptionText: {
    fontSize: typography.sizes.md,
    color: colors.text,
    lineHeight: 22,
  },
  showMoreButton: {
    marginTop: spacing.sm,
  },
  showMoreText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium,
  },
  mapContainer: {
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  mapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapOverlayText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  participantsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  participantCard: {
    alignItems: 'center',
    width: (screenWidth - spacing.lg * 2 - spacing.md * 2) / 3,
  },
  participantAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: spacing.sm,
  },
  participantName: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  organizerBadge: {
    backgroundColor: colors.warning,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  },
  organizerBadgeText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  joinSlot: {
    alignItems: 'center',
    width: (screenWidth - spacing.lg * 2 - spacing.md * 2) / 3,
  },
  joinButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.surface,
    borderWidth: 2,
    borderColor: colors.primary,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  joinButtonText: {
    fontSize: typography.sizes.xs,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    textAlign: 'center',
  },
  bottomActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 12,
    backgroundColor: colors.background,
    gap: spacing.xs,
  },
  leaveButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  cancelButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  chatButton: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EventDetailScreen;
