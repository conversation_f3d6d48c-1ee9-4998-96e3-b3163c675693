import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { discoverService } from '../../services/discoverService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { User, DiscoverFilters } from '../../types/discover.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const CARD_HEIGHT = screenHeight * 0.7;
const CARD_WIDTH = screenWidth * 0.9;

interface DiscoverScreenProps {}

const DiscoverScreen: React.FC<DiscoverScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location, requestLocation } = useLocation();
  const queryClient = useQueryClient();

  const [currentIndex, setCurrentIndex] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState<DiscoverFilters>({
    ageMin: 18,
    ageMax: 35,
    distance: 50,
    interests: [],
    verified: false,
    online: false,
  });

  // Анимированные значения для карточек
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);
  const rotate = useSharedValue(0);

  // Загрузка пользователей для знакомств
  const {
    data: discoverUsers,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['discover', 'users', filters, location],
    queryFn: () => discoverService.getDiscoverUsers(filters, location),
    enabled: !!location,
  });

  // Мутация для лайка/дизлайка
  const likeMutation = useMutation({
    mutationFn: ({ userId, action }: { userId: string; action: 'like' | 'dislike' | 'superlike' }) =>
      discoverService.reactToUser(userId, action),
    onSuccess: (data, variables) => {
      if (data.isMatch && variables.action === 'like') {
        Alert.alert(
          'Это матч! 💕',
          'Вы понравились друг другу! Начните общение.',
          [
            { text: 'Позже', style: 'cancel' },
            { text: 'Написать', onPress: () => navigation.navigate('Chat', { userId: variables.userId }) },
          ]
        );
      }
      nextCard();
      queryClient.invalidateQueries({ queryKey: ['matches'] });
    },
    onError: (error) => {
      Alert.alert('Ошибка', 'Не удалось отправить реакцию. Попробуйте снова.');
    },
  });

  // Запрос геолокации при загрузке
  useEffect(() => {
    if (!location) {
      requestLocation();
    }
  }, [location, requestLocation]);

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      if (location) {
        refetch();
      }
    }, [location, refetch])
  );

  const nextCard = () => {
    if (discoverUsers && currentIndex < discoverUsers.length - 1) {
      setCurrentIndex(prev => prev + 1);
      resetCardPosition();
    } else {
      // Загружаем новых пользователей
      refetch();
      setCurrentIndex(0);
    }
  };

  const resetCardPosition = () => {
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    scale.value = withSpring(1);
    rotate.value = withSpring(0);
  };

  const handleLike = () => {
    if (discoverUsers && discoverUsers[currentIndex]) {
      likeMutation.mutate({
        userId: discoverUsers[currentIndex].id,
        action: 'like',
      });
    }
  };

  const handleDislike = () => {
    if (discoverUsers && discoverUsers[currentIndex]) {
      likeMutation.mutate({
        userId: discoverUsers[currentIndex].id,
        action: 'dislike',
      });
    }
  };

  const handleSuperLike = () => {
    if (discoverUsers && discoverUsers[currentIndex]) {
      likeMutation.mutate({
        userId: discoverUsers[currentIndex].id,
        action: 'superlike',
      });
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setCurrentIndex(0);
    setRefreshing(false);
  };

  // Обработчик жестов для свайпа
  const onGestureEvent = (event: any) => {
    translateX.value = event.nativeEvent.translationX;
    translateY.value = event.nativeEvent.translationY;
    
    const rotateValue = interpolate(
      event.nativeEvent.translationX,
      [-screenWidth / 2, 0, screenWidth / 2],
      [-15, 0, 15]
    );
    rotate.value = rotateValue;

    const scaleValue = interpolate(
      Math.abs(event.nativeEvent.translationX),
      [0, screenWidth / 2],
      [1, 0.95]
    );
    scale.value = scaleValue;
  };

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.state === State.END) {
      const { translationX, velocityX } = event.nativeEvent;
      
      if (Math.abs(translationX) > screenWidth * 0.3 || Math.abs(velocityX) > 1000) {
        // Свайп влево или вправо
        if (translationX > 0) {
          handleLike();
        } else {
          handleDislike();
        }
        
        // Анимация исчезновения карточки
        translateX.value = withTiming(translationX > 0 ? screenWidth : -screenWidth, { duration: 300 });
        scale.value = withTiming(0.8, { duration: 300 });
      } else {
        // Возврат карточки в исходное положение
        resetCardPosition();
      }
    }
  };

  // Стили анимации для карточки
  const cardAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
        { rotate: `${rotate.value}deg` },
      ],
    };
  });

  const renderUserCard = (user: User, index: number) => {
    const isActive = index === currentIndex;
    const isNext = index === currentIndex + 1;

    return (
      <Animated.View
        key={user.id}
        style={[
          styles.card,
          {
            zIndex: discoverUsers!.length - index,
            opacity: isActive ? 1 : isNext ? 0.8 : 0,
            transform: [
              { scale: isActive ? 1 : isNext ? 0.95 : 0.9 },
              { translateY: isActive ? 0 : isNext ? 10 : 20 },
            ],
          },
          isActive && cardAnimatedStyle,
        ]}
      >
        <PanGestureHandler
          onGestureEvent={isActive ? onGestureEvent : undefined}
          onHandlerStateChange={isActive ? onHandlerStateChange : undefined}
          enabled={isActive}
        >
          <Animated.View style={styles.cardContent}>
            <Image source={{ uri: user.photos[0] }} style={styles.cardImage} />
            
            {/* Градиент для текста */}
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.cardGradient}
            />
            
            {/* Информация о пользователе */}
            <View style={styles.cardInfo}>
              <View style={styles.userMainInfo}>
                <Text style={styles.userName}>
                  {user.firstName}, {user.age}
                </Text>
                {user.isVerified && (
                  <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
                )}
              </View>
              
              <Text style={styles.userLocation}>
                <Ionicons name="location-outline" size={16} color={colors.textSecondary} />
                {' '}{user.distance}км от вас
              </Text>
              
              {user.bio && (
                <Text style={styles.userBio} numberOfLines={2}>
                  {user.bio}
                </Text>
              )}
              
              {user.interests.length > 0 && (
                <View style={styles.interestsContainer}>
                  {user.interests.slice(0, 3).map((interest, idx) => (
                    <View key={idx} style={styles.interestTag}>
                      <Text style={styles.interestText}>{interest}</Text>
                    </View>
                  ))}
                  {user.interests.length > 3 && (
                    <Text style={styles.moreInterests}>+{user.interests.length - 3}</Text>
                  )}
                </View>
              )}
            </View>
            
            {/* Индикаторы фотографий */}
            <View style={styles.photoIndicators}>
              {user.photos.map((_, idx) => (
                <View
                  key={idx}
                  style={[
                    styles.photoIndicator,
                    { backgroundColor: idx === 0 ? colors.primary : 'rgba(255,255,255,0.3)' },
                  ]}
                />
              ))}
            </View>
            
            {/* Статус онлайн */}
            {user.isOnline && (
              <View style={styles.onlineIndicator}>
                <View style={styles.onlineDot} />
                <Text style={styles.onlineText}>Онлайн</Text>
              </View>
            )}
          </Animated.View>
        </PanGestureHandler>
      </Animated.View>
    );
  };

  if (isLoading && !discoverUsers) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Ищем интересных людей...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>Не удалось загрузить пользователей</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('DiscoverFilters')}
        >
          <Ionicons name="options-outline" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Знакомства</Text>
        
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('NearbyUsers')}
        >
          <Ionicons name="location-outline" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Карточки пользователей */}
      <View style={styles.cardsContainer}>
        {discoverUsers && discoverUsers.length > 0 ? (
          discoverUsers.map((user, index) => renderUserCard(user, index))
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="heart-outline" size={64} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>Пока никого нет</Text>
            <Text style={styles.emptyText}>
              Попробуйте изменить фильтры или зайдите позже
            </Text>
            <TouchableOpacity
              style={styles.filtersButton}
              onPress={() => navigation.navigate('DiscoverFilters')}
            >
              <Text style={styles.filtersButtonText}>Настроить фильтры</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Кнопки действий */}
      {discoverUsers && discoverUsers.length > 0 && (
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.actionButton, styles.dislikeButton]}
            onPress={handleDislike}
            disabled={likeMutation.isPending}
          >
            <Ionicons name="close" size={32} color={colors.error} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.superLikeButton]}
            onPress={handleSuperLike}
            disabled={likeMutation.isPending}
          >
            <Ionicons name="star" size={28} color={colors.warning} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.likeButton]}
            onPress={handleLike}
            disabled={likeMutation.isPending}
          >
            <Ionicons name="heart" size={32} color={colors.success} />
          </TouchableOpacity>
        </View>
      )}

      {/* Индикатор загрузки */}
      {likeMutation.isPending && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  cardsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
  },
  card: {
    position: 'absolute',
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    borderRadius: 20,
    backgroundColor: colors.surface,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  cardContent: {
    flex: 1,
    borderRadius: 20,
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  cardGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '50%',
  },
  cardInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.lg,
  },
  userMainInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  userName: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginRight: spacing.sm,
  },
  userLocation: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  userBio: {
    fontSize: typography.sizes.md,
    color: colors.white,
    lineHeight: 20,
    marginBottom: spacing.md,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  interestTag: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  interestText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  moreInterests: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium,
  },
  photoIndicators: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    right: spacing.md,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  photoIndicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 2,
  },
  onlineIndicator: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  onlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.success,
    marginRight: spacing.xs,
  },
  onlineText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    gap: spacing.lg,
  },
  actionButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.surface,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  dislikeButton: {
    backgroundColor: colors.surface,
  },
  superLikeButton: {
    backgroundColor: colors.surface,
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  likeButton: {
    backgroundColor: colors.surface,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  filtersButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  filtersButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default DiscoverScreen;
