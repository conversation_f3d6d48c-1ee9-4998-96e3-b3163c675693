import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Switch,
  FormControl,
  FormControlLabel,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert,
  LinearProgress,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Payment as PaymentIcon,
  Settings as SettingsIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Build as BuildIcon,
  Storage as StorageIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AdminLayout } from '../../../components/admin/AdminLayout';
import { useAuth } from '../../../hooks/useAuth';
import { adminService } from '../../../services/adminService';
import { SystemSettings } from '../../../types/admin.types';
import styles from '../../../styles/admin/Settings.module.css';

const AdminSettings: React.FC = () => {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const queryClient = useQueryClient();

  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    type: 'save' | 'reset' | 'maintenance';
    title: string;
    message: string;
  }>({
    open: false,
    type: 'save',
    title: '',
    message: '',
  });

  // Проверка прав доступа
  useEffect(() => {
    if (!isAdmin) {
      router.push('/');
    }
  }, [isAdmin, router]);

  // Загрузка настроек
  const {
    data: systemSettings,
    isLoading,
    error,
    refetch,
  } = useQuery<SystemSettings>({
    queryKey: ['admin', 'settings'],
    queryFn: () => adminService.getSystemSettings(),
    enabled: isAdmin,
    onSuccess: (data) => {
      setSettings(data);
      setHasChanges(false);
    },
  });

  // Мутация для сохранения настроек
  const updateSettingsMutation = useMutation({
    mutationFn: (newSettings: Partial<SystemSettings>) =>
      adminService.updateSystemSettings(newSettings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'settings'] });
      setHasChanges(false);
      setConfirmDialog({ open: false, type: 'save', title: '', message: '' });
    },
  });

  // Системные операции
  const clearCacheMutation = useMutation({
    mutationFn: () => adminService.clearCache(),
    onSuccess: () => {
      // Показать уведомление об успехе
    },
  });

  const restartServicesMutation = useMutation({
    mutationFn: () => adminService.restartServices(),
    onSuccess: () => {
      // Показать уведомление об успехе
    },
  });

  const backupDatabaseMutation = useMutation({
    mutationFn: () => adminService.backupDatabase(),
    onSuccess: () => {
      // Показать уведомление об успехе
    },
  });

  const handleSettingChange = (section: keyof SystemSettings, field: string, value: any) => {
    if (!settings) return;

    const newSettings = {
      ...settings,
      [section]: {
        ...settings[section],
        [field]: value,
      },
    };

    setSettings(newSettings);
    setHasChanges(true);
  };

  const handleSaveSettings = () => {
    setConfirmDialog({
      open: true,
      type: 'save',
      title: 'Сохранить настройки',
      message: 'Вы уверены, что хотите сохранить изменения? Некоторые настройки могут потребовать перезапуска системы.',
    });
  };

  const handleResetSettings = () => {
    setConfirmDialog({
      open: true,
      type: 'reset',
      title: 'Сбросить настройки',
      message: 'Вы уверены, что хотите сбросить все изменения? Несохраненные данные будут потеряны.',
    });
  };

  const handleMaintenanceMode = () => {
    if (!settings) return;

    const isEnabling = !settings.general.maintenanceMode;
    setConfirmDialog({
      open: true,
      type: 'maintenance',
      title: isEnabling ? 'Включить режим обслуживания' : 'Выключить режим обслуживания',
      message: isEnabling 
        ? 'Это заблокирует доступ к приложению для всех пользователей, кроме администраторов.'
        : 'Это восстановит нормальную работу приложения для всех пользователей.',
    });
  };

  const handleConfirmAction = () => {
    if (!settings) return;

    switch (confirmDialog.type) {
      case 'save':
        updateSettingsMutation.mutate(settings);
        break;
      case 'reset':
        if (systemSettings) {
          setSettings(systemSettings);
          setHasChanges(false);
        }
        setConfirmDialog({ open: false, type: 'save', title: '', message: '' });
        break;
      case 'maintenance':
        handleSettingChange('general', 'maintenanceMode', !settings.general.maintenanceMode);
        break;
    }
  };

  if (!isAdmin || !settings) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Настройки системы - Админ-панель</title>
        <meta name="description" content="Настройки системы приложения знакомств" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <AdminLayout>
        <Box className={styles.container}>
          {/* Заголовок */}
          <Box className={styles.header}>
            <Typography variant="h4" component="h1" className={styles.title}>
              Настройки системы
            </Typography>
            <Box className={styles.headerActions}>
              {hasChanges && (
                <Alert severity="warning" className={styles.changesAlert}>
                  У вас есть несохраненные изменения
                </Alert>
              )}
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => refetch()}
                disabled={updateSettingsMutation.isPending}
              >
                Обновить
              </Button>
              <Button
                variant="outlined"
                onClick={handleResetSettings}
                disabled={!hasChanges || updateSettingsMutation.isPending}
              >
                Сбросить
              </Button>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSaveSettings}
                disabled={!hasChanges || updateSettingsMutation.isPending}
                className={styles.saveButton}
              >
                Сохранить
              </Button>
            </Box>
          </Box>

          {isLoading && <LinearProgress />}

          {error && (
            <Alert severity="error" className={styles.errorAlert}>
              Ошибка загрузки настроек: {error.message}
            </Alert>
          )}

          {/* Режим обслуживания */}
          <Card className={styles.maintenanceCard}>
            <CardContent>
              <Box className={styles.maintenanceHeader}>
                <Box>
                  <Typography variant="h6" className={styles.maintenanceTitle}>
                    <WarningIcon className={styles.maintenanceIcon} />
                    Режим обслуживания
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {settings.general.maintenanceMode 
                      ? 'Приложение находится в режиме обслуживания'
                      : 'Приложение работает в обычном режиме'
                    }
                  </Typography>
                </Box>
                <Button
                  variant={settings.general.maintenanceMode ? "outlined" : "contained"}
                  color={settings.general.maintenanceMode ? "success" : "warning"}
                  onClick={handleMaintenanceMode}
                  className={styles.maintenanceButton}
                >
                  {settings.general.maintenanceMode ? 'Выключить' : 'Включить'}
                </Button>
              </Box>
              {settings.general.maintenanceMode && (
                <TextField
                  fullWidth
                  multiline
                  rows={2}
                  label="Сообщение для пользователей"
                  value={settings.general.maintenanceMessage || ''}
                  onChange={(e) => handleSettingChange('general', 'maintenanceMessage', e.target.value)}
                  margin="normal"
                />
              )}
            </CardContent>
          </Card>

          {/* Основные настройки */}
          <Accordion className={styles.settingsAccordion}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6" className={styles.sectionTitle}>
                <SettingsIcon className={styles.sectionIcon} />
                Основные настройки
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Название приложения"
                    value={settings.general.appName}
                    onChange={(e) => handleSettingChange('general', 'appName', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email поддержки"
                    type="email"
                    value={settings.general.supportEmail}
                    onChange={(e) => handleSettingChange('general', 'supportEmail', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Описание приложения"
                    value={settings.general.appDescription}
                    onChange={(e) => handleSettingChange('general', 'appDescription', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Максимум фото на пользователя"
                    value={settings.general.maxPhotosPerUser}
                    onChange={(e) => handleSettingChange('general', 'maxPhotosPerUser', Number(e.target.value))}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Минимальный возраст"
                    value={settings.general.minAge}
                    onChange={(e) => handleSettingChange('general', 'minAge', Number(e.target.value))}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Максимальный возраст"
                    value={settings.general.maxAge}
                    onChange={(e) => handleSettingChange('general', 'maxAge', Number(e.target.value))}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Безопасность */}
          <Accordion className={styles.settingsAccordion}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6" className={styles.sectionTitle}>
                <SecurityIcon className={styles.sectionIcon} />
                Безопасность
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Минимальная длина пароля"
                    value={settings.security.passwordMinLength}
                    onChange={(e) => handleSettingChange('security', 'passwordMinLength', Number(e.target.value))}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Время сессии (минуты)"
                    value={settings.security.sessionTimeoutMinutes}
                    onChange={(e) => handleSettingChange('security', 'sessionTimeoutMinutes', Number(e.target.value))}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.security.passwordRequireSpecialChars}
                        onChange={(e) => handleSettingChange('security', 'passwordRequireSpecialChars', e.target.checked)}
                      />
                    }
                    label="Требовать специальные символы в пароле"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.security.passwordRequireNumbers}
                        onChange={(e) => handleSettingChange('security', 'passwordRequireNumbers', e.target.checked)}
                      />
                    }
                    label="Требовать цифры в пароле"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.security.twoFactorAuthRequired}
                        onChange={(e) => handleSettingChange('security', 'twoFactorAuthRequired', e.target.checked)}
                      />
                    }
                    label="Обязательная двухфакторная аутентификация"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Максимум попыток входа"
                    value={settings.security.maxLoginAttempts}
                    onChange={(e) => handleSettingChange('security', 'maxLoginAttempts', Number(e.target.value))}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Уведомления */}
          <Accordion className={styles.settingsAccordion}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6" className={styles.sectionTitle}>
                <NotificationsIcon className={styles.sectionIcon} />
                Уведомления
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.notifications.emailNotificationsEnabled}
                        onChange={(e) => handleSettingChange('notifications', 'emailNotificationsEnabled', e.target.checked)}
                      />
                    }
                    label="Email уведомления"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.notifications.pushNotificationsEnabled}
                        onChange={(e) => handleSettingChange('notifications', 'pushNotificationsEnabled', e.target.checked)}
                      />
                    }
                    label="Push уведомления"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.notifications.smsNotificationsEnabled}
                        onChange={(e) => handleSettingChange('notifications', 'smsNotificationsEnabled', e.target.checked)}
                      />
                    }
                    label="SMS уведомления"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Хранить уведомления (дни)"
                    value={settings.notifications.notificationRetentionDays}
                    onChange={(e) => handleSettingChange('notifications', 'notificationRetentionDays', Number(e.target.value))}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Модерация */}
          <Accordion className={styles.settingsAccordion}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6" className={styles.sectionTitle}>
                <SecurityIcon className={styles.sectionIcon} />
                Модерация
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.moderation.autoModerationEnabled}
                        onChange={(e) => handleSettingChange('moderation', 'autoModerationEnabled', e.target.checked)}
                      />
                    }
                    label="Автоматическая модерация"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Порог автомодерации"
                    value={settings.moderation.autoModerationThreshold}
                    onChange={(e) => handleSettingChange('moderation', 'autoModerationThreshold', Number(e.target.value))}
                    inputProps={{ min: 0, max: 1, step: 0.1 }}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.moderation.requirePhotoModeration}
                        onChange={(e) => handleSettingChange('moderation', 'requirePhotoModeration', e.target.checked)}
                      />
                    }
                    label="Модерация фото"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.moderation.requireProfileModeration}
                        onChange={(e) => handleSettingChange('moderation', 'requireProfileModeration', e.target.checked)}
                      />
                    }
                    label="Модерация профилей"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.moderation.requireMessageModeration}
                        onChange={(e) => handleSettingChange('moderation', 'requireMessageModeration', e.target.checked)}
                      />
                    }
                    label="Модерация сообщений"
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Системные операции */}
          <Card className={styles.systemOpsCard}>
            <CardContent>
              <Typography variant="h6" className={styles.sectionTitle} gutterBottom>
                <BuildIcon className={styles.sectionIcon} />
                Системные операции
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => clearCacheMutation.mutate()}
                    disabled={clearCacheMutation.isPending}
                    className={styles.systemButton}
                  >
                    Очистить кеш
                  </Button>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<BuildIcon />}
                    onClick={() => restartServicesMutation.mutate()}
                    disabled={restartServicesMutation.isPending}
                    className={styles.systemButton}
                  >
                    Перезапустить сервисы
                  </Button>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<StorageIcon />}
                    onClick={() => backupDatabaseMutation.mutate()}
                    disabled={backupDatabaseMutation.isPending}
                    className={styles.systemButton}
                  >
                    Создать резервную копию
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Диалог подтверждения */}
          <Dialog
            open={confirmDialog.open}
            onClose={() => setConfirmDialog({ open: false, type: 'save', title: '', message: '' })}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>{confirmDialog.title}</DialogTitle>
            <DialogContent>
              <Typography variant="body1">
                {confirmDialog.message}
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setConfirmDialog({ open: false, type: 'save', title: '', message: '' })}
              >
                Отмена
              </Button>
              <Button
                onClick={handleConfirmAction}
                variant="contained"
                disabled={updateSettingsMutation.isPending}
              >
                Подтвердить
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </AdminLayout>
    </>
  );
};

export default AdminSettings;
