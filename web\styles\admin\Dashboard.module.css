/* Стили для главной страницы админ-панели */

.container {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title {
  display: flex;
  align-items: center;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.titleIcon {
  margin-right: 12px;
  color: #3498db;
}

.refreshButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: all 0.3s ease;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.systemAlert {
  margin-bottom: 24px;
  border-radius: 12px;
  font-weight: 500;
}

/* Статистические карточки */
.statsGrid {
  margin-bottom: 32px;
}

.statCard {
  height: 100%;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.statCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.statContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.statIcon {
  font-size: 48px;
  opacity: 0.7;
  color: #667eea;
}

/* Графики */
.chartsGrid {
  margin-bottom: 32px;
}

.chartCard {
  height: 100%;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.chartCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chartContainer {
  height: 300px;
  position: relative;
  padding: 16px;
}

/* Системное здоровье */
.systemGrid {
  margin-bottom: 32px;
}

.systemCard {
  height: 100%;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.systemHealth {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.healthItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 8px;
  border-left: 4px solid #e9ecef;
  transition: all 0.3s ease;
}

.healthItem:hover {
  background: rgba(248, 249, 250, 1);
  border-left-color: #667eea;
}

/* Активность */
.activityList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.activityItem {
  padding: 12px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 8px;
  border-left: 3px solid #667eea;
  transition: all 0.3s ease;
}

.activityItem:hover {
  background: rgba(248, 249, 250, 1);
  transform: translateX(4px);
}

.activityText {
  margin-bottom: 4px;
  font-weight: 500;
  color: #2c3e50;
}

/* Быстрые действия */
.actionsGrid {
  margin-bottom: 32px;
}

.actionsCard {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.actionButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
}

.actionButton {
  flex: 1;
  min-width: 200px;
  padding: 16px 24px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  text-transform: none;
  transition: all 0.3s ease;
  border: none;
}

.actionButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Адаптивность */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .title {
    font-size: 1.5rem;
  }

  .statContent {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .statIcon {
    font-size: 36px;
  }

  .chartContainer {
    height: 250px;
    padding: 8px;
  }

  .actionButtons {
    flex-direction: column;
  }

  .actionButton {
    min-width: auto;
    width: 100%;
  }

  .systemHealth {
    gap: 12px;
  }

  .healthItem {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .activityList {
    max-height: 200px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px;
  }

  .header {
    padding: 16px;
  }

  .title {
    font-size: 1.25rem;
  }

  .chartContainer {
    height: 200px;
    padding: 4px;
  }
}

/* Анимации */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.statCard,
.chartCard,
.systemCard,
.actionsCard {
  animation: fadeInUp 0.6s ease-out;
}

.statCard:nth-child(1) { animation-delay: 0.1s; }
.statCard:nth-child(2) { animation-delay: 0.2s; }
.statCard:nth-child(3) { animation-delay: 0.3s; }
.statCard:nth-child(4) { animation-delay: 0.4s; }

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }

  .header,
  .statCard,
  .chartCard,
  .systemCard,
  .actionsCard {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .title {
    color: #ffffff;
  }

  .healthItem,
  .activityItem {
    background: rgba(40, 40, 40, 0.8);
    border-left-color: #667eea;
  }

  .healthItem:hover,
  .activityItem:hover {
    background: rgba(40, 40, 40, 1);
  }

  .activityText {
    color: #ffffff;
  }
}
