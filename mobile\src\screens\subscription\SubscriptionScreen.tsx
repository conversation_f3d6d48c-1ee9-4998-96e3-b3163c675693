import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { subscriptionService } from '../../services/subscriptionService';
import { useAuth } from '../../hooks/useAuth';
import { Subscription, SubscriptionPlan, SubscriptionFeature } from '../../types/subscription.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface SubscriptionScreenProps {}

const SubscriptionScreen: React.FC<SubscriptionScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const [selectedPlan, setSelectedPlan] = useState<string>('');

  // Загрузка текущей подписки
  const {
    data: currentSubscription,
    isLoading: subscriptionLoading,
    refetch: refetchSubscription,
  } = useQuery({
    queryKey: ['subscription', 'current'],
    queryFn: () => subscriptionService.getCurrentSubscription(),
  });

  // Загрузка доступных планов
  const {
    data: plans,
    isLoading: plansLoading,
  } = useQuery({
    queryKey: ['subscription', 'plans'],
    queryFn: () => subscriptionService.getAvailablePlans(),
  });

  // Мутация для отмены подписки
  const cancelSubscriptionMutation = useMutation({
    mutationFn: () => subscriptionService.cancelSubscription(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      Alert.alert('Подписка отменена', 'Ваша подписка будет активна до конца оплаченного периода');
    },
    onError: () => {
      Alert.alert('Ошибка', 'Не удалось отменить подписку. Попробуйте позже.');
    },
  });

  // Мутация для возобновления подписки
  const resumeSubscriptionMutation = useMutation({
    mutationFn: () => subscriptionService.resumeSubscription(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      Alert.alert('Подписка возобновлена', 'Ваша подписка снова активна');
    },
  });

  const handleCancelSubscription = () => {
    Alert.alert(
      'Отменить подписку',
      'Вы уверены, что хотите отменить подписку? Вы потеряете доступ к премиум-функциям в конце текущего периода.',
      [
        { text: 'Нет', style: 'cancel' },
        {
          text: 'Отменить подписку',
          style: 'destructive',
          onPress: () => cancelSubscriptionMutation.mutate(),
        },
      ]
    );
  };

  const handleResumeSubscription = () => {
    resumeSubscriptionMutation.mutate();
  };

  const handleUpgrade = (planId: string) => {
    navigation.navigate('PaymentScreen', { planId, action: 'upgrade' });
  };

  const handleChangePlan = (planId: string) => {
    navigation.navigate('PaymentScreen', { planId, action: 'change' });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return colors.success;
      case 'cancelled':
        return colors.warning;
      case 'expired':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Активна';
      case 'cancelled':
        return 'Отменена';
      case 'expired':
        return 'Истекла';
      default:
        return 'Неизвестно';
    }
  };

  const renderFeature = (feature: SubscriptionFeature, index: number) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateX: interpolate(animatedValue.value, [0, 1], [-20, 0]),
          },
        ],
      };
    });

    return (
      <Animated.View key={feature.id} style={[styles.featureItem, animatedStyle]}>
        <View style={styles.featureIcon}>
          <Ionicons
            name={feature.available ? 'checkmark-circle' : 'close-circle'}
            size={20}
            color={feature.available ? colors.success : colors.error}
          />
        </View>
        <Text
          style={[
            styles.featureText,
            !feature.available && styles.featureTextDisabled,
          ]}
        >
          {feature.name}
        </Text>
        {feature.description && (
          <Text style={styles.featureDescription}>{feature.description}</Text>
        )}
      </Animated.View>
    );
  };

  const renderPlanCard = (plan: SubscriptionPlan, index: number) => {
    const isCurrentPlan = currentSubscription?.planId === plan.id;
    const isPopular = plan.isPopular;
    
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 150,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.9, 1]),
          },
        ],
      };
    });

    return (
      <Animated.View key={plan.id} style={[styles.planCard, animatedStyle]}>
        {isPopular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularBadgeText}>Популярный</Text>
          </View>
        )}
        
        <LinearGradient
          colors={isCurrentPlan ? [colors.primary, colors.secondary] : [colors.surface, colors.surface]}
          style={styles.planGradient}
        >
          <View style={styles.planHeader}>
            <Text style={[styles.planName, isCurrentPlan && styles.planNameActive]}>
              {plan.name}
            </Text>
            <View style={styles.planPricing}>
              <Text style={[styles.planPrice, isCurrentPlan && styles.planPriceActive]}>
                {plan.price === 0 ? 'Бесплатно' : `${plan.price}₽`}
              </Text>
              {plan.price > 0 && (
                <Text style={[styles.planPeriod, isCurrentPlan && styles.planPeriodActive]}>
                  /{plan.period === 'month' ? 'мес' : 'год'}
                </Text>
              )}
            </View>
          </View>
          
          <Text style={[styles.planDescription, isCurrentPlan && styles.planDescriptionActive]}>
            {plan.description}
          </Text>
          
          <View style={styles.planFeatures}>
            {plan.features.map((feature, idx) => (
              <View key={idx} style={styles.planFeatureItem}>
                <Ionicons
                  name="checkmark"
                  size={16}
                  color={isCurrentPlan ? colors.white : colors.success}
                />
                <Text style={[styles.planFeatureText, isCurrentPlan && styles.planFeatureTextActive]}>
                  {feature}
                </Text>
              </View>
            ))}
          </View>
          
          <View style={styles.planActions}>
            {isCurrentPlan ? (
              <View style={styles.currentPlanBadge}>
                <Ionicons name="checkmark-circle" size={20} color={colors.white} />
                <Text style={styles.currentPlanText}>Текущий план</Text>
              </View>
            ) : (
              <TouchableOpacity
                style={[
                  styles.planButton,
                  isPopular && styles.planButtonPopular,
                ]}
                onPress={() => {
                  if (currentSubscription) {
                    handleChangePlan(plan.id);
                  } else {
                    handleUpgrade(plan.id);
                  }
                }}
              >
                <Text
                  style={[
                    styles.planButtonText,
                    isPopular && styles.planButtonTextPopular,
                  ]}
                >
                  {currentSubscription ? 'Изменить план' : 'Выбрать план'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </LinearGradient>
      </Animated.View>
    );
  };

  if (subscriptionLoading || plansLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Загружаем информацию о подписке...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Подписка</Text>
        
        <TouchableOpacity
          style={styles.historyButton}
          onPress={() => navigation.navigate('SubscriptionHistory')}
        >
          <Ionicons name="time-outline" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Текущая подписка */}
        {currentSubscription && (
          <View style={styles.currentSubscriptionSection}>
            <Text style={styles.sectionTitle}>Текущая подписка</Text>
            
            <View style={styles.currentSubscriptionCard}>
              <LinearGradient
                colors={[colors.primary, colors.secondary]}
                style={styles.currentSubscriptionGradient}
              >
                <View style={styles.currentSubscriptionHeader}>
                  <Text style={styles.currentSubscriptionName}>
                    {currentSubscription.planName}
                  </Text>
                  <View style={styles.currentSubscriptionStatus}>
                    <View
                      style={[
                        styles.statusIndicator,
                        { backgroundColor: getStatusColor(currentSubscription.status) },
                      ]}
                    />
                    <Text style={styles.statusText}>
                      {getStatusText(currentSubscription.status)}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.currentSubscriptionDetails}>
                  <View style={styles.subscriptionDetailItem}>
                    <Ionicons name="calendar-outline" size={20} color={colors.white} />
                    <Text style={styles.subscriptionDetailText}>
                      Действует до: {formatDate(currentSubscription.expiresAt)}
                    </Text>
                  </View>
                  
                  {currentSubscription.nextBillingDate && (
                    <View style={styles.subscriptionDetailItem}>
                      <Ionicons name="card-outline" size={20} color={colors.white} />
                      <Text style={styles.subscriptionDetailText}>
                        Следующее списание: {formatDate(currentSubscription.nextBillingDate)}
                      </Text>
                    </View>
                  )}
                </View>
                
                <View style={styles.currentSubscriptionActions}>
                  {currentSubscription.status === 'active' && (
                    <TouchableOpacity
                      style={styles.cancelButton}
                      onPress={handleCancelSubscription}
                      disabled={cancelSubscriptionMutation.isPending}
                    >
                      <Text style={styles.cancelButtonText}>Отменить подписку</Text>
                    </TouchableOpacity>
                  )}
                  
                  {currentSubscription.status === 'cancelled' && (
                    <TouchableOpacity
                      style={styles.resumeButton}
                      onPress={handleResumeSubscription}
                      disabled={resumeSubscriptionMutation.isPending}
                    >
                      <Text style={styles.resumeButtonText}>Возобновить подписку</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </LinearGradient>
            </View>
          </View>
        )}

        {/* Доступные планы */}
        <View style={styles.plansSection}>
          <Text style={styles.sectionTitle}>
            {currentSubscription ? 'Изменить план' : 'Выберите план'}
          </Text>
          <Text style={styles.sectionSubtitle}>
            Получите доступ к премиум-функциям и найдите свою любовь быстрее
          </Text>
          
          <View style={styles.plansContainer}>
            {plans?.map(renderPlanCard)}
          </View>
        </View>

        {/* Преимущества подписки */}
        <View style={styles.benefitsSection}>
          <Text style={styles.sectionTitle}>Преимущества подписки</Text>
          
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <View style={styles.benefitIcon}>
                <Ionicons name="heart" size={24} color={colors.error} />
              </View>
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>Безлимитные лайки</Text>
                <Text style={styles.benefitDescription}>
                  Ставьте лайки без ограничений и находите больше совпадений
                </Text>
              </View>
            </View>
            
            <View style={styles.benefitItem}>
              <View style={styles.benefitIcon}>
                <Ionicons name="star" size={24} color={colors.warning} />
              </View>
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>Суперлайки</Text>
                <Text style={styles.benefitDescription}>
                  Выделитесь среди других пользователей с помощью суперлайков
                </Text>
              </View>
            </View>
            
            <View style={styles.benefitItem}>
              <View style={styles.benefitIcon}>
                <Ionicons name="eye" size={24} color={colors.primary} />
              </View>
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>Кто вас лайкнул</Text>
                <Text style={styles.benefitDescription}>
                  Смотрите, кто поставил вам лайк, и отвечайте взаимностью
                </Text>
              </View>
            </View>
            
            <View style={styles.benefitItem}>
              <View style={styles.benefitIcon}>
                <Ionicons name="location" size={24} color={colors.success} />
              </View>
              <View style={styles.benefitContent}>
                <Text style={styles.benefitTitle}>Путешествия</Text>
                <Text style={styles.benefitDescription}>
                  Знакомьтесь с людьми в любом городе мира
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Индикатор загрузки */}
      {(cancelSubscriptionMutation.isPending || resumeSubscriptionMutation.isPending) && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  historyButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  scrollView: {
    flex: 1,
  },
  currentSubscriptionSection: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  sectionSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: 22,
  },
  currentSubscriptionCard: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
  },
  currentSubscriptionGradient: {
    padding: spacing.lg,
  },
  currentSubscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  currentSubscriptionName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  currentSubscriptionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  currentSubscriptionDetails: {
    marginBottom: spacing.lg,
  },
  subscriptionDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    gap: spacing.sm,
  },
  subscriptionDetailText: {
    fontSize: typography.sizes.md,
    color: colors.white,
  },
  currentSubscriptionActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: spacing.sm,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  resumeButton: {
    flex: 1,
    backgroundColor: colors.white,
    paddingVertical: spacing.sm,
    borderRadius: 12,
    alignItems: 'center',
  },
  resumeButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.primary,
  },
  plansSection: {
    padding: spacing.lg,
  },
  plansContainer: {
    gap: spacing.md,
  },
  planCard: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    position: 'relative',
  },
  popularBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: colors.warning,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderBottomLeftRadius: 12,
    zIndex: 1,
  },
  popularBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  planGradient: {
    padding: spacing.lg,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  planName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  planNameActive: {
    color: colors.white,
  },
  planPricing: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  planPrice: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
  },
  planPriceActive: {
    color: colors.white,
  },
  planPeriod: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
  },
  planPeriodActive: {
    color: colors.white,
  },
  planDescription: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    marginBottom: spacing.md,
    lineHeight: 20,
  },
  planDescriptionActive: {
    color: colors.white,
  },
  planFeatures: {
    marginBottom: spacing.lg,
  },
  planFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
    gap: spacing.sm,
  },
  planFeatureText: {
    fontSize: typography.sizes.sm,
    color: colors.text,
  },
  planFeatureTextActive: {
    color: colors.white,
  },
  planActions: {
    alignItems: 'center',
  },
  currentPlanBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    gap: spacing.xs,
  },
  currentPlanText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  planButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 25,
    minWidth: 150,
    alignItems: 'center',
  },
  planButtonPopular: {
    backgroundColor: colors.warning,
  },
  planButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  planButtonTextPopular: {
    color: colors.white,
  },
  benefitsSection: {
    padding: spacing.lg,
  },
  benefitsList: {
    gap: spacing.lg,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
  },
  benefitIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  benefitDescription: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    gap: spacing.md,
  },
  featureIcon: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureText: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  featureTextDisabled: {
    color: colors.textSecondary,
    textDecorationLine: 'line-through',
  },
  featureDescription: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SubscriptionScreen;
