import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { subscriptionService } from '../../services/subscriptionService';
import { useAuth } from '../../hooks/useAuth';
import { SubscriptionPlan } from '../../types/subscription.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface PlansScreenProps {}

const PlansScreen: React.FC<PlansScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();

  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'year'>('month');

  // Загрузка планов подписки
  const {
    data: plans,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['subscription', 'plans', selectedPeriod],
    queryFn: () => subscriptionService.getPlans(selectedPeriod),
  });

  // Загрузка текущей подписки
  const {
    data: currentSubscription,
  } = useQuery({
    queryKey: ['subscription', 'current'],
    queryFn: () => subscriptionService.getCurrentSubscription(),
  });

  const handleSelectPlan = (plan: SubscriptionPlan) => {
    if (currentSubscription?.planId === plan.id) {
      Alert.alert('Информация', 'Это ваш текущий план подписки');
      return;
    }

    navigation.navigate('PaymentScreen', {
      planId: plan.id,
      action: currentSubscription ? 'change' : 'upgrade',
    });
  };

  const calculateSavings = (monthlyPrice: number, yearlyPrice: number) => {
    const yearlyMonthly = yearlyPrice / 12;
    const savings = ((monthlyPrice - yearlyMonthly) / monthlyPrice) * 100;
    return Math.round(savings);
  };

  const renderPlanCard = (plan: SubscriptionPlan, index: number) => {
    const animatedValue = useSharedValue(0);
    const isCurrentPlan = currentSubscription?.planId === plan.id;
    const isPopular = plan.isPopular;

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 150,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.9, 1]),
          },
          {
            translateY: interpolate(animatedValue.value, [0, 1], [30, 0]),
          },
        ],
      };
    });

    return (
      <Animated.View key={plan.id} style={[styles.planCard, animatedStyle]}>
        {isPopular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularBadgeText}>Популярный</Text>
          </View>
        )}
        
        <LinearGradient
          colors={
            isCurrentPlan
              ? [colors.primary, colors.secondary]
              : isPopular
              ? [colors.warning, '#FF8F00']
              : [colors.surface, colors.surface]
          }
          style={styles.planGradient}
        >
          <View style={styles.planHeader}>
            <Text style={[styles.planName, (isCurrentPlan || isPopular) && styles.planNameActive]}>
              {plan.name}
            </Text>
            
            <View style={styles.planPricing}>
              <Text style={[styles.planPrice, (isCurrentPlan || isPopular) && styles.planPriceActive]}>
                {plan.price === 0 ? 'Бесплатно' : `${plan.price}₽`}
              </Text>
              {plan.price > 0 && (
                <Text style={[styles.planPeriod, (isCurrentPlan || isPopular) && styles.planPeriodActive]}>
                  /{selectedPeriod === 'month' ? 'мес' : 'год'}
                </Text>
              )}
            </View>
            
            {selectedPeriod === 'year' && plan.monthlyPrice && (
              <View style={styles.savingsContainer}>
                <Text style={[styles.savingsText, (isCurrentPlan || isPopular) && styles.savingsTextActive]}>
                  Экономия {calculateSavings(plan.monthlyPrice, plan.price)}%
                </Text>
              </View>
            )}
          </View>
          
          <Text style={[styles.planDescription, (isCurrentPlan || isPopular) && styles.planDescriptionActive]}>
            {plan.description}
          </Text>
          
          <View style={styles.planFeatures}>
            {plan.features.map((feature, idx) => (
              <View key={idx} style={styles.planFeatureItem}>
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color={(isCurrentPlan || isPopular) ? colors.white : colors.success}
                />
                <Text style={[styles.planFeatureText, (isCurrentPlan || isPopular) && styles.planFeatureTextActive]}>
                  {feature}
                </Text>
              </View>
            ))}
          </View>
          
          <View style={styles.planActions}>
            {isCurrentPlan ? (
              <View style={styles.currentPlanBadge}>
                <Ionicons name="checkmark-circle" size={20} color={colors.white} />
                <Text style={styles.currentPlanText}>Текущий план</Text>
              </View>
            ) : (
              <TouchableOpacity
                style={[
                  styles.planButton,
                  isPopular && styles.planButtonPopular,
                ]}
                onPress={() => handleSelectPlan(plan)}
              >
                <Text
                  style={[
                    styles.planButtonText,
                    isPopular && styles.planButtonTextPopular,
                  ]}
                >
                  {currentSubscription ? 'Изменить план' : 'Выбрать план'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </LinearGradient>
      </Animated.View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Загружаем планы подписки...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить планы подписки
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Планы подписки</Text>
        
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Переключатель периода */}
        <View style={styles.periodToggleContainer}>
          <Text style={styles.periodToggleTitle}>Выберите период</Text>
          <View style={styles.periodToggle}>
            <TouchableOpacity
              style={[
                styles.periodButton,
                selectedPeriod === 'month' && styles.periodButtonActive,
              ]}
              onPress={() => setSelectedPeriod('month')}
            >
              <Text
                style={[
                  styles.periodButtonText,
                  selectedPeriod === 'month' && styles.periodButtonTextActive,
                ]}
              >
                Месяц
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.periodButton,
                selectedPeriod === 'year' && styles.periodButtonActive,
              ]}
              onPress={() => setSelectedPeriod('year')}
            >
              <Text
                style={[
                  styles.periodButtonText,
                  selectedPeriod === 'year' && styles.periodButtonTextActive,
                ]}
              >
                Год
              </Text>
              <View style={styles.savingsBadge}>
                <Text style={styles.savingsBadgeText}>-30%</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Планы подписки */}
        <View style={styles.plansContainer}>
          {plans?.map(renderPlanCard)}
        </View>

        {/* Информация о подписке */}
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Что входит в подписку?</Text>
          
          <View style={styles.infoList}>
            <View style={styles.infoItem}>
              <Ionicons name="heart" size={24} color={colors.error} />
              <View style={styles.infoContent}>
                <Text style={styles.infoItemTitle}>Безлимитные лайки</Text>
                <Text style={styles.infoItemDescription}>
                  Ставьте лайки без ограничений и находите больше совпадений
                </Text>
              </View>
            </View>
            
            <View style={styles.infoItem}>
              <Ionicons name="star" size={24} color={colors.warning} />
              <View style={styles.infoContent}>
                <Text style={styles.infoItemTitle}>Суперлайки</Text>
                <Text style={styles.infoItemDescription}>
                  Выделитесь среди других пользователей с помощью суперлайков
                </Text>
              </View>
            </View>
            
            <View style={styles.infoItem}>
              <Ionicons name="eye" size={24} color={colors.primary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoItemTitle}>Кто вас лайкнул</Text>
                <Text style={styles.infoItemDescription}>
                  Смотрите, кто поставил вам лайк, и отвечайте взаимностью
                </Text>
              </View>
            </View>
            
            <View style={styles.infoItem}>
              <Ionicons name="location" size={24} color={colors.success} />
              <View style={styles.infoContent}>
                <Text style={styles.infoItemTitle}>Путешествия</Text>
                <Text style={styles.infoItemDescription}>
                  Знакомьтесь с людьми в любом городе мира
                </Text>
              </View>
            </View>
            
            <View style={styles.infoItem}>
              <Ionicons name="shield-checkmark" size={24} color={colors.secondary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoItemTitle}>Приоритетная поддержка</Text>
                <Text style={styles.infoItemDescription}>
                  Быстрая помощь от службы поддержки 24/7
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Гарантии */}
        <View style={styles.guaranteeSection}>
          <View style={styles.guaranteeCard}>
            <Ionicons name="shield-checkmark-outline" size={32} color={colors.success} />
            <Text style={styles.guaranteeTitle}>Гарантия возврата</Text>
            <Text style={styles.guaranteeText}>
              Если вы не найдете свою любовь в течение 6 месяцев, мы вернем деньги
            </Text>
          </View>
          
          <View style={styles.guaranteeCard}>
            <Ionicons name="lock-closed-outline" size={32} color={colors.primary} />
            <Text style={styles.guaranteeTitle}>Безопасные платежи</Text>
            <Text style={styles.guaranteeText}>
              Все платежи защищены 256-битным SSL шифрованием
            </Text>
          </View>
        </View>

        {/* FAQ */}
        <View style={styles.faqSection}>
          <Text style={styles.faqTitle}>Часто задаваемые вопросы</Text>
          
          <View style={styles.faqItem}>
            <Text style={styles.faqQuestion}>Можно ли отменить подписку?</Text>
            <Text style={styles.faqAnswer}>
              Да, вы можете отменить подписку в любое время. Доступ к премиум-функциям сохранится до конца оплаченного периода.
            </Text>
          </View>
          
          <View style={styles.faqItem}>
            <Text style={styles.faqQuestion}>Что происходит после окончания подписки?</Text>
            <Text style={styles.faqAnswer}>
              Ваш аккаунт автоматически переходит на бесплатный план с базовой функциональностью.
            </Text>
          </View>
          
          <View style={styles.faqItem}>
            <Text style={styles.faqQuestion}>Можно ли изменить план подписки?</Text>
            <Text style={styles.faqAnswer}>
              Да, вы можете повысить или понизить план в любое время. Изменения вступят в силу немедленно.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  periodToggleContainer: {
    padding: spacing.lg,
    alignItems: 'center',
  },
  periodToggleTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  periodToggle: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: 25,
    padding: 4,
  },
  periodButton: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    position: 'relative',
  },
  periodButtonActive: {
    backgroundColor: colors.primary,
  },
  periodButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  periodButtonTextActive: {
    color: colors.white,
  },
  savingsBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.error,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  savingsBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  plansContainer: {
    padding: spacing.lg,
    gap: spacing.lg,
  },
  planCard: {
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
    position: 'relative',
  },
  popularBadge: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.warning,
    paddingVertical: spacing.xs,
    alignItems: 'center',
    zIndex: 1,
  },
  popularBadgeText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  planGradient: {
    padding: spacing.lg,
    paddingTop: spacing.xl,
  },
  planHeader: {
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  planName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  planNameActive: {
    color: colors.white,
  },
  planPricing: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: spacing.xs,
  },
  planPrice: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
  },
  planPriceActive: {
    color: colors.white,
  },
  planPeriod: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  planPeriodActive: {
    color: colors.white,
  },
  savingsContainer: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  savingsText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.success,
  },
  savingsTextActive: {
    color: colors.white,
  },
  planDescription: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
    lineHeight: 22,
  },
  planDescriptionActive: {
    color: colors.white,
  },
  planFeatures: {
    marginBottom: spacing.lg,
  },
  planFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    gap: spacing.md,
  },
  planFeatureText: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  planFeatureTextActive: {
    color: colors.white,
  },
  planActions: {
    alignItems: 'center',
  },
  currentPlanBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 25,
    gap: spacing.sm,
  },
  currentPlanText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  planButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 25,
    minWidth: 200,
    alignItems: 'center',
  },
  planButtonPopular: {
    backgroundColor: colors.white,
  },
  planButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  planButtonTextPopular: {
    color: colors.warning,
  },
  infoSection: {
    padding: spacing.lg,
  },
  infoTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  infoList: {
    gap: spacing.lg,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
  },
  infoContent: {
    flex: 1,
  },
  infoItemTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  infoItemDescription: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  guaranteeSection: {
    padding: spacing.lg,
    gap: spacing.md,
  },
  guaranteeCard: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  guaranteeTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  guaranteeText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  faqSection: {
    padding: spacing.lg,
  },
  faqTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  faqItem: {
    backgroundColor: colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  faqQuestion: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  faqAnswer: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default PlansScreen;
