import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { meetingsService } from '../../services/meetingsService';
import { placesService } from '../../services/placesService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { CreateMeetingData, MeetingType, Place } from '../../types/meetings.types';
import { colors, typography, spacing } from '../../theme';

const createMeetingSchema = yup.object().shape({
  title: yup.string().required('Название обязательно').min(3, 'Минимум 3 символа'),
  description: yup.string().max(500, 'Максимум 500 символов'),
  dateTime: yup.date().required('Дата и время обязательны').min(new Date(), 'Дата не может быть в прошлом'),
  type: yup.string().required('Тип встречи обязателен'),
  maxParticipants: yup.number().min(2, 'Минимум 2 участника').max(20, 'Максимум 20 участников'),
  isPrivate: yup.boolean(),
});

interface CreateMeetingScreenProps {}

const CreateMeetingScreen: React.FC<CreateMeetingScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location } = useLocation();
  const queryClient = useQueryClient();

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [selectedPlace, setSelectedPlace] = useState<Place | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
  } = useForm<CreateMeetingData>({
    resolver: yupResolver(createMeetingSchema),
    defaultValues: {
      title: '',
      description: '',
      dateTime: new Date(Date.now() + 60 * 60 * 1000), // Через час
      type: 'casual',
      maxParticipants: 4,
      isPrivate: false,
    },
  });

  const watchedDateTime = watch('dateTime');
  const watchedType = watch('type');

  // Поиск мест
  const {
    data: places,
    isLoading: placesLoading,
  } = useQuery({
    queryKey: ['places', 'search', searchQuery, location],
    queryFn: () => placesService.searchPlaces(searchQuery, location),
    enabled: searchQuery.length > 2,
  });

  // Создание встречи
  const createMeetingMutation = useMutation({
    mutationFn: (data: CreateMeetingData) => meetingsService.createMeeting(data),
    onSuccess: (meeting) => {
      queryClient.invalidateQueries({ queryKey: ['meetings'] });
      Alert.alert(
        'Встреча создана!',
        'Ваша встреча успешно создана. Пригласите друзей!',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('MeetingDetail', { meetingId: meeting.id }),
          },
        ]
      );
    },
    onError: (error) => {
      Alert.alert('Ошибка', 'Не удалось создать встречу. Попробуйте снова.');
    },
  });

  const onSubmit = (data: CreateMeetingData) => {
    if (!selectedPlace) {
      Alert.alert('Ошибка', 'Выберите место для встречи');
      return;
    }

    const meetingData = {
      ...data,
      placeId: selectedPlace.id,
      location: selectedPlace.location,
    };

    createMeetingMutation.mutate(meetingData);
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const currentTime = watchedDateTime;
      const newDateTime = new Date(selectedDate);
      newDateTime.setHours(currentTime.getHours());
      newDateTime.setMinutes(currentTime.getMinutes());
      setValue('dateTime', newDateTime);
    }
  };

  const handleTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(false);
    if (selectedTime) {
      const currentDate = watchedDateTime;
      const newDateTime = new Date(currentDate);
      newDateTime.setHours(selectedTime.getHours());
      newDateTime.setMinutes(selectedTime.getMinutes());
      setValue('dateTime', newDateTime);
    }
  };

  const meetingTypes = [
    { value: 'casual', label: 'Неформальная встреча', icon: 'cafe-outline' },
    { value: 'dinner', label: 'Ужин', icon: 'restaurant-outline' },
    { value: 'activity', label: 'Активность', icon: 'fitness-outline' },
    { value: 'cultural', label: 'Культурное мероприятие', icon: 'library-outline' },
    { value: 'party', label: 'Вечеринка', icon: 'musical-notes-outline' },
  ];

  const renderMeetingType = (type: { value: string; label: string; icon: string }) => (
    <TouchableOpacity
      key={type.value}
      style={[
        styles.typeButton,
        watchedType === type.value && styles.typeButtonActive,
      ]}
      onPress={() => setValue('type', type.value as MeetingType)}
    >
      <Ionicons
        name={type.icon as any}
        size={24}
        color={watchedType === type.value ? colors.white : colors.text}
      />
      <Text
        style={[
          styles.typeButtonText,
          watchedType === type.value && styles.typeButtonTextActive,
        ]}
      >
        {type.label}
      </Text>
    </TouchableOpacity>
  );

  const renderPlaceItem = (place: Place) => (
    <TouchableOpacity
      key={place.id}
      style={[
        styles.placeItem,
        selectedPlace?.id === place.id && styles.placeItemSelected,
      ]}
      onPress={() => setSelectedPlace(place)}
    >
      <View style={styles.placeInfo}>
        <Text style={styles.placeName}>{place.name}</Text>
        <Text style={styles.placeAddress}>{place.address}</Text>
        <View style={styles.placeDetails}>
          <View style={styles.placeRating}>
            <Ionicons name="star" size={14} color={colors.warning} />
            <Text style={styles.ratingText}>{place.rating}</Text>
          </View>
          <Text style={styles.placeDistance}>
            {place.distance ? `${place.distance.toFixed(1)}км` : ''}
          </Text>
        </View>
      </View>
      {selectedPlace?.id === place.id && (
        <Ionicons name="checkmark-circle" size={24} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Создать встречу</Text>
          
          <TouchableOpacity
            style={[
              styles.saveButton,
              (!isValid || !selectedPlace) && styles.saveButtonDisabled,
            ]}
            onPress={handleSubmit(onSubmit)}
            disabled={!isValid || !selectedPlace || createMeetingMutation.isPending}
          >
            {createMeetingMutation.isPending ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <Text style={styles.saveButtonText}>Создать</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Название */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Название встречи</Text>
            <Controller
              control={control}
              name="title"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[styles.input, errors.title && styles.inputError]}
                  placeholder="Например: Ужин в центре города"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  maxLength={100}
                />
              )}
            />
            {errors.title && (
              <Text style={styles.errorText}>{errors.title.message}</Text>
            )}
          </View>

          {/* Описание */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Описание (необязательно)</Text>
            <Controller
              control={control}
              name="description"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[styles.textArea, errors.description && styles.inputError]}
                  placeholder="Расскажите подробнее о встрече..."
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  multiline
                  numberOfLines={4}
                  maxLength={500}
                />
              )}
            />
            {errors.description && (
              <Text style={styles.errorText}>{errors.description.message}</Text>
            )}
          </View>

          {/* Дата и время */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Дата и время</Text>
            <View style={styles.dateTimeContainer}>
              <TouchableOpacity
                style={styles.dateTimeButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Ionicons name="calendar-outline" size={20} color={colors.primary} />
                <Text style={styles.dateTimeText}>
                  {watchedDateTime.toLocaleDateString('ru-RU')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.dateTimeButton}
                onPress={() => setShowTimePicker(true)}
              >
                <Ionicons name="time-outline" size={20} color={colors.primary} />
                <Text style={styles.dateTimeText}>
                  {watchedDateTime.toLocaleTimeString('ru-RU', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Тип встречи */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Тип встречи</Text>
            <View style={styles.typesContainer}>
              {meetingTypes.map(renderMeetingType)}
            </View>
          </View>

          {/* Место */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Место встречи</Text>
            <TextInput
              style={styles.searchInput}
              placeholder="Поиск мест..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            
            {selectedPlace && (
              <View style={styles.selectedPlace}>
                <Text style={styles.selectedPlaceTitle}>Выбранное место:</Text>
                {renderPlaceItem(selectedPlace)}
              </View>
            )}
            
            {placesLoading && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={colors.primary} />
                <Text style={styles.loadingText}>Поиск мест...</Text>
              </View>
            )}
            
            {places && places.length > 0 && (
              <View style={styles.placesList}>
                {places.slice(0, 5).map(renderPlaceItem)}
              </View>
            )}
          </View>

          {/* Настройки */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Настройки</Text>
            
            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Максимум участников</Text>
              <Controller
                control={control}
                name="maxParticipants"
                render={({ field: { onChange, value } }) => (
                  <View style={styles.participantsControls}>
                    <TouchableOpacity
                      style={styles.participantButton}
                      onPress={() => onChange(Math.max(2, value - 1))}
                    >
                      <Ionicons name="remove" size={20} color={colors.primary} />
                    </TouchableOpacity>
                    <Text style={styles.participantsValue}>{value}</Text>
                    <TouchableOpacity
                      style={styles.participantButton}
                      onPress={() => onChange(Math.min(20, value + 1))}
                    >
                      <Ionicons name="add" size={20} color={colors.primary} />
                    </TouchableOpacity>
                  </View>
                )}
              />
            </View>
            
            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Приватная встреча</Text>
              <Controller
                control={control}
                name="isPrivate"
                render={({ field: { onChange, value } }) => (
                  <TouchableOpacity
                    style={[styles.switch, value && styles.switchActive]}
                    onPress={() => onChange(!value)}
                  >
                    <View style={[styles.switchThumb, value && styles.switchThumbActive]} />
                  </TouchableOpacity>
                )}
              />
            </View>
            
            <Text style={styles.settingDescription}>
              Приватные встречи видны только приглашенным участникам
            </Text>
          </View>
        </ScrollView>

        {/* Date/Time Pickers */}
        {showDatePicker && (
          <DateTimePicker
            value={watchedDateTime}
            mode="date"
            display="default"
            onChange={handleDateChange}
            minimumDate={new Date()}
          />
        )}
        
        {showTimePicker && (
          <DateTimePicker
            value={watchedDateTime}
            mode="time"
            display="default"
            onChange={handleTimeChange}
          />
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  saveButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  },
  saveButtonDisabled: {
    backgroundColor: colors.textSecondary,
  },
  saveButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.md,
    color: colors.text,
    backgroundColor: colors.surface,
  },
  textArea: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.md,
    color: colors.text,
    backgroundColor: colors.surface,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    fontSize: typography.sizes.sm,
    color: colors.error,
    marginTop: spacing.xs,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  dateTimeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    backgroundColor: colors.surface,
    gap: spacing.sm,
  },
  dateTimeText: {
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  typesContainer: {
    gap: spacing.sm,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    backgroundColor: colors.surface,
    gap: spacing.sm,
  },
  typeButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  typeButtonText: {
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  typeButtonTextActive: {
    color: colors.white,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.md,
    color: colors.text,
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
  },
  selectedPlace: {
    marginBottom: spacing.md,
  },
  selectedPlaceTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  placesList: {
    gap: spacing.sm,
  },
  placeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    backgroundColor: colors.surface,
  },
  placeItemSelected: {
    borderColor: colors.primary,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  placeInfo: {
    flex: 1,
  },
  placeName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  placeAddress: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  placeDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  placeRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.sizes.sm,
    color: colors.text,
  },
  placeDistance: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    gap: spacing.sm,
  },
  loadingText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  settingLabel: {
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  participantsControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  participantButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  participantsValue: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    minWidth: 30,
    textAlign: 'center',
  },
  switch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.border,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  switchActive: {
    backgroundColor: colors.primary,
  },
  switchThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: colors.white,
    alignSelf: 'flex-start',
  },
  switchThumbActive: {
    alignSelf: 'flex-end',
  },
  settingDescription: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginTop: spacing.sm,
  },
});

export default CreateMeetingScreen;
