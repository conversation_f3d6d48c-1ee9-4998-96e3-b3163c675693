import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import { discoverService } from '../../services/discoverService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { User, NearbyUser } from '../../types/discover.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface NearbyScreenProps {}

const NearbyScreen: React.FC<NearbyScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location, requestLocation } = useLocation();
  const queryClient = useQueryClient();

  const [viewMode, setViewMode] = useState<'list' | 'map'>('list');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedUser, setSelectedUser] = useState<NearbyUser | null>(null);
  const [mapRegion, setMapRegion] = useState({
    latitude: 55.7558,
    longitude: 37.6176,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });

  // Загрузка пользователей рядом
  const {
    data: nearbyUsers,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['discover', 'nearby', location],
    queryFn: () => discoverService.getNearbyUsers(location),
    enabled: !!location,
  });

  // Мутация для лайка
  const likeMutation = useMutation({
    mutationFn: ({ userId, action }: { userId: string; action: 'like' | 'superlike' }) =>
      discoverService.reactToUser(userId, action),
    onSuccess: (data, variables) => {
      if (data.isMatch) {
        Alert.alert(
          'Это матч! 💕',
          'Вы понравились друг другу!',
          [
            { text: 'Позже', style: 'cancel' },
            { text: 'Написать', onPress: () => navigation.navigate('Chat', { userId: variables.userId }) },
          ]
        );
      }
      queryClient.invalidateQueries({ queryKey: ['matches'] });
    },
  });

  // Запрос геолокации при загрузке
  useEffect(() => {
    if (!location) {
      requestLocation();
    } else {
      setMapRegion({
        latitude: location.latitude,
        longitude: location.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  }, [location, requestLocation]);

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      if (location) {
        refetch();
      }
    }, [location, refetch])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleLike = (userId: string) => {
    likeMutation.mutate({ userId, action: 'like' });
  };

  const handleSuperLike = (userId: string) => {
    likeMutation.mutate({ userId, action: 'superlike' });
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}м`;
    }
    return `${distance.toFixed(1)}км`;
  };

  const formatLastSeen = (lastSeen: string) => {
    const now = new Date();
    const lastSeenDate = new Date(lastSeen);
    const diffInMinutes = Math.floor((now.getTime() - lastSeenDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Только что';
    if (diffInMinutes < 60) return `${diffInMinutes} мин назад`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ч назад`;
    return `${Math.floor(diffInMinutes / 1440)} дн назад`;
  };

  const renderUserCard = ({ item }: { item: NearbyUser }) => (
    <TouchableOpacity
      style={styles.userCard}
      onPress={() => navigation.navigate('UserProfile', { userId: item.id })}
      activeOpacity={0.8}
    >
      <Image source={{ uri: item.photos[0] }} style={styles.userImage} />
      
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.7)']}
        style={styles.userImageGradient}
      />
      
      <View style={styles.userInfo}>
        <View style={styles.userMainInfo}>
          <Text style={styles.userName}>
            {item.firstName}, {item.age}
          </Text>
          {item.isVerified && (
            <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
          )}
        </View>
        
        <View style={styles.userDetails}>
          <Text style={styles.userDistance}>
            <Ionicons name="location-outline" size={14} color={colors.textSecondary} />
            {' '}{formatDistance(item.distance)}
          </Text>
          
          {item.isOnline ? (
            <View style={styles.onlineStatus}>
              <View style={styles.onlineDot} />
              <Text style={styles.onlineText}>Онлайн</Text>
            </View>
          ) : (
            <Text style={styles.lastSeenText}>
              {formatLastSeen(item.lastSeen)}
            </Text>
          )}
        </View>
        
        {item.bio && (
          <Text style={styles.userBio} numberOfLines={2}>
            {item.bio}
          </Text>
        )}
      </View>
      
      <View style={styles.userActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.superLikeButton]}
          onPress={() => handleSuperLike(item.id)}
          disabled={likeMutation.isPending}
        >
          <Ionicons name="star" size={20} color={colors.warning} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.likeButton]}
          onPress={() => handleLike(item.id)}
          disabled={likeMutation.isPending}
        >
          <Ionicons name="heart" size={20} color={colors.success} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderMapMarker = (user: NearbyUser) => (
    <Marker
      key={user.id}
      coordinate={{
        latitude: user.location.latitude,
        longitude: user.location.longitude,
      }}
      onPress={() => setSelectedUser(user)}
    >
      <View style={styles.markerContainer}>
        <Image source={{ uri: user.photos[0] }} style={styles.markerImage} />
        {user.isOnline && <View style={styles.markerOnlineIndicator} />}
      </View>
    </Marker>
  );

  const renderSelectedUserInfo = () => {
    if (!selectedUser) return null;

    return (
      <View style={styles.selectedUserCard}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => setSelectedUser(null)}
        >
          <Ionicons name="close" size={20} color={colors.text} />
        </TouchableOpacity>
        
        <Image source={{ uri: selectedUser.photos[0] }} style={styles.selectedUserImage} />
        
        <View style={styles.selectedUserInfo}>
          <View style={styles.selectedUserMainInfo}>
            <Text style={styles.selectedUserName}>
              {selectedUser.firstName}, {selectedUser.age}
            </Text>
            {selectedUser.isVerified && (
              <Ionicons name="checkmark-circle" size={18} color={colors.primary} />
            )}
          </View>
          
          <Text style={styles.selectedUserDistance}>
            {formatDistance(selectedUser.distance)} от вас
          </Text>
          
          {selectedUser.bio && (
            <Text style={styles.selectedUserBio} numberOfLines={2}>
              {selectedUser.bio}
            </Text>
          )}
          
          <View style={styles.selectedUserActions}>
            <TouchableOpacity
              style={[styles.actionButton, styles.superLikeButton]}
              onPress={() => handleSuperLike(selectedUser.id)}
            >
              <Ionicons name="star" size={18} color={colors.warning} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.likeButton]}
              onPress={() => handleLike(selectedUser.id)}
            >
              <Ionicons name="heart" size={18} color={colors.success} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.profileButton]}
              onPress={() => navigation.navigate('UserProfile', { userId: selectedUser.id })}
            >
              <Ionicons name="person" size={18} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  if (isLoading && !nearbyUsers) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Ищем людей рядом...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="location-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка геолокации</Text>
          <Text style={styles.errorText}>
            Не удалось определить ваше местоположение
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={requestLocation}>
            <Text style={styles.retryButtonText}>Разрешить доступ</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Люди рядом</Text>
        
        <View style={styles.viewModeToggle}>
          <TouchableOpacity
            style={[styles.toggleButton, viewMode === 'list' && styles.toggleButtonActive]}
            onPress={() => setViewMode('list')}
          >
            <Ionicons name="list" size={20} color={viewMode === 'list' ? colors.white : colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.toggleButton, viewMode === 'map' && styles.toggleButtonActive]}
            onPress={() => setViewMode('map')}
          >
            <Ionicons name="map" size={20} color={viewMode === 'map' ? colors.white : colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Контент */}
      {viewMode === 'list' ? (
        <FlatList
          data={nearbyUsers}
          renderItem={renderUserCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="people-outline" size={64} color={colors.textSecondary} />
              <Text style={styles.emptyTitle}>Никого нет рядом</Text>
              <Text style={styles.emptyText}>
                Попробуйте зайти позже или расширьте радиус поиска
              </Text>
            </View>
          }
        />
      ) : (
        <View style={styles.mapContainer}>
          <MapView
            provider={PROVIDER_GOOGLE}
            style={styles.map}
            region={mapRegion}
            onRegionChangeComplete={setMapRegion}
            showsUserLocation
            showsMyLocationButton
          >
            {nearbyUsers?.map(renderMapMarker)}
          </MapView>
          
          {renderSelectedUserInfo()}
        </View>
      )}

      {/* Счетчик пользователей */}
      {nearbyUsers && nearbyUsers.length > 0 && (
        <View style={styles.counterContainer}>
          <Text style={styles.counterText}>
            {nearbyUsers.length} {nearbyUsers.length === 1 ? 'человек' : 'людей'} рядом
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: 20,
    padding: 2,
  },
  toggleButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: colors.primary,
  },
  listContainer: {
    padding: spacing.md,
  },
  userCard: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    marginBottom: spacing.md,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  userImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  userImageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  userInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.md,
  },
  userMainInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  userName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginRight: spacing.xs,
  },
  userDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  userDistance: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  onlineStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.success,
    marginRight: spacing.xs,
  },
  onlineText: {
    fontSize: typography.sizes.sm,
    color: colors.success,
    fontWeight: typography.weights.medium,
  },
  lastSeenText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  userBio: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    lineHeight: 18,
  },
  userActions: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.9)',
  },
  superLikeButton: {
    backgroundColor: 'rgba(255,255,255,0.9)',
  },
  likeButton: {
    backgroundColor: 'rgba(255,255,255,0.9)',
  },
  profileButton: {
    backgroundColor: 'rgba(255,255,255,0.9)',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  markerContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
    borderWidth: 3,
    borderColor: colors.primary,
    position: 'relative',
  },
  markerImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  markerOnlineIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.success,
    borderWidth: 2,
    borderColor: colors.white,
  },
  selectedUserCard: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.md,
    right: spacing.md,
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  closeButton: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  selectedUserImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: spacing.sm,
  },
  selectedUserInfo: {
    flex: 1,
  },
  selectedUserMainInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  selectedUserName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginRight: spacing.xs,
  },
  selectedUserDistance: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  selectedUserBio: {
    fontSize: typography.sizes.sm,
    color: colors.text,
    lineHeight: 18,
    marginBottom: spacing.md,
  },
  selectedUserActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  counterContainer: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.lg,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  },
  counterText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default NearbyScreen;
