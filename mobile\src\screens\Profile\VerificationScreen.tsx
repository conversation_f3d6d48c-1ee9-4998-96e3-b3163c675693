import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  StatusBar,
  ScrollView,
  Image,
  ActivityIndicator,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { launchImageLibrary, launchCamera, ImagePickerResponse } from 'react-native-image-picker';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface VerificationScreenProps {
  navigation: any;
}

interface VerificationStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  status: 'pending' | 'in_progress' | 'completed' | 'rejected';
  required: boolean;
  photo?: string;
  rejectionReason?: string;
}

const VerificationScreen: React.FC<VerificationScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { user, submitVerification, getVerificationStatus } = useAuth();

  const [verificationSteps, setVerificationSteps] = useState<VerificationStep[]>([
    {
      id: 'selfie',
      title: 'Селфи с документом',
      description: 'Сделайте селфи, держа документ рядом с лицом',
      icon: 'camera-alt',
      status: 'pending',
      required: true,
    },
    {
      id: 'document_front',
      title: 'Лицевая сторона документа',
      description: 'Сфотографируйте лицевую сторону паспорта или водительских прав',
      icon: 'credit-card',
      status: 'pending',
      required: true,
    },
    {
      id: 'document_back',
      title: 'Обратная сторона документа',
      description: 'Сфотографируйте обратную сторону документа (если применимо)',
      icon: 'flip-to-back',
      status: 'pending',
      required: false,
    },
    {
      id: 'phone_verification',
      title: 'Подтверждение телефона',
      description: 'Подтвердите номер телефона через SMS',
      icon: 'phone',
      status: user?.phoneVerified ? 'completed' : 'pending',
      required: true,
    },
    {
      id: 'email_verification',
      title: 'Подтверждение email',
      description: 'Подтвердите адрес электронной почты',
      icon: 'email',
      status: user?.emailVerified ? 'completed' : 'pending',
      required: true,
    },
  ]);

  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<string | null>(null);

  useEffect(() => {
    loadVerificationStatus();
  }, []);

  const loadVerificationStatus = async () => {
    setIsLoading(true);
    try {
      const result = await getVerificationStatus();
      if (result.success && result.verificationData) {
        setVerificationSteps(prev => 
          prev.map(step => ({
            ...step,
            ...result.verificationData[step.id],
          }))
        );
      }
    } catch (error) {
      console.error('Error loading verification status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Разрешение на использование камеры',
            message: 'Для верификации нужен доступ к камере',
            buttonNeutral: 'Спросить позже',
            buttonNegative: 'Отмена',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  const handleStepPress = (step: VerificationStep) => {
    if (step.status === 'completed') {
      return;
    }

    if (step.id === 'phone_verification') {
      navigation.navigate('VerifyPhoneScreen');
      return;
    }

    if (step.id === 'email_verification') {
      navigation.navigate('VerifyEmailScreen');
      return;
    }

    // Для фото-верификации
    setCurrentStep(step.id);
    showImagePicker(step);
  };

  const showImagePicker = (step: VerificationStep) => {
    Alert.alert(
      step.title,
      'Выберите способ создания фотографии',
      [
        { text: 'Отмена', style: 'cancel' },
        { text: 'Галерея', onPress: () => openImageLibrary(step) },
        { text: 'Камера', onPress: () => openCamera(step) },
      ]
    );
  };

  const openImageLibrary = (step: VerificationStep) => {
    const options = {
      mediaType: 'photo' as const,
      quality: 0.9,
      maxWidth: 2048,
      maxHeight: 2048,
    };

    launchImageLibrary(options, (response) => handleImageResponse(response, step));
  };

  const openCamera = async (step: VerificationStep) => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert('Ошибка', 'Нет разрешения на использование камеры');
      return;
    }

    const options = {
      mediaType: 'photo' as const,
      quality: 0.9,
      maxWidth: 2048,
      maxHeight: 2048,
    };

    launchCamera(options, (response) => handleImageResponse(response, step));
  };

  const handleImageResponse = async (response: ImagePickerResponse, step: VerificationStep) => {
    if (response.didCancel || response.errorMessage) {
      setCurrentStep(null);
      return;
    }

    if (response.assets && response.assets[0]) {
      setIsLoading(true);
      try {
        const asset = response.assets[0];
        const result = await submitVerification({
          stepId: step.id,
          photo: {
            uri: asset.uri || '',
            type: asset.type,
            fileName: asset.fileName,
          },
        });

        if (result.success) {
          // Обновляем статус шага
          setVerificationSteps(prev =>
            prev.map(s =>
              s.id === step.id
                ? { ...s, status: 'in_progress', photo: asset.uri }
                : s
            )
          );
          Alert.alert(
            'Успешно',
            'Документ отправлен на проверку. Результат будет готов в течение 24 часов.'
          );
        } else {
          Alert.alert('Ошибка', result.message || 'Не удалось отправить документ');
        }
      } catch (error) {
        Alert.alert('Ошибка', 'Произошла ошибка при отправке документа');
      } finally {
        setIsLoading(false);
        setCurrentStep(null);
      }
    }
  };

  const getStepStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return 'check-circle';
      case 'in_progress':
        return 'schedule';
      case 'rejected':
        return 'error';
      default:
        return 'radio-button-unchecked';
    }
  };

  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#4CAF50';
      case 'in_progress':
        return '#FF9800';
      case 'rejected':
        return '#F44336';
      default:
        return 'rgba(255, 255, 255, 0.6)';
    }
  };

  const getStepStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Подтверждено';
      case 'in_progress':
        return 'На проверке';
      case 'rejected':
        return 'Отклонено';
      default:
        return 'Ожидает';
    }
  };

  const getVerificationProgress = () => {
    const completedSteps = verificationSteps.filter(step => step.status === 'completed').length;
    const totalSteps = verificationSteps.filter(step => step.required).length;
    return Math.round((completedSteps / totalSteps) * 100);
  };

  const isFullyVerified = () => {
    return verificationSteps
      .filter(step => step.required)
      .every(step => step.status === 'completed');
  };

  const renderVerificationStep = (step: VerificationStep) => (
    <TouchableOpacity
      key={step.id}
      style={[
        styles.stepContainer,
        step.status === 'completed' && styles.stepCompleted,
        step.status === 'rejected' && styles.stepRejected,
      ]}
      onPress={() => handleStepPress(step)}
      disabled={step.status === 'completed' || isLoading}
    >
      <View style={styles.stepIcon}>
        <Icon
          name={step.icon}
          size={24}
          color={getStepStatusColor(step.status)}
        />
      </View>

      <View style={styles.stepContent}>
        <View style={styles.stepHeader}>
          <Text style={styles.stepTitle}>{step.title}</Text>
          {step.required && (
            <Text style={styles.requiredBadge}>Обязательно</Text>
          )}
        </View>
        <Text style={styles.stepDescription}>{step.description}</Text>
        
        {step.rejectionReason && (
          <Text style={styles.rejectionReason}>
            Причина отклонения: {step.rejectionReason}
          </Text>
        )}

        {step.photo && (
          <Image source={{ uri: step.photo }} style={styles.stepPhoto} />
        )}
      </View>

      <View style={styles.stepStatus}>
        <Icon
          name={getStepStatusIcon(step.status)}
          size={20}
          color={getStepStatusColor(step.status)}
        />
        <Text style={[styles.stepStatusText, { color: getStepStatusColor(step.status) }]}>
          {getStepStatusText(step.status)}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Верификация профиля</Text>
          <View style={styles.headerRight} />
        </View>

        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Прогресс */}
          <View style={styles.progressContainer}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressTitle}>Прогресс верификации</Text>
              <Text style={styles.progressPercentage}>{getVerificationProgress()}%</Text>
            </View>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${getVerificationProgress()}%` },
                ]}
              />
            </View>
            {isFullyVerified() && (
              <View style={styles.verifiedBadge}>
                <Icon name="verified-user" size={20} color="#4CAF50" />
                <Text style={styles.verifiedText}>Профиль полностью верифицирован!</Text>
              </View>
            )}
          </View>

          {/* Преимущества верификации */}
          <View style={styles.benefitsContainer}>
            <Text style={styles.benefitsTitle}>Преимущества верификации:</Text>
            <View style={styles.benefit}>
              <Icon name="verified" size={16} color="#4CAF50" />
              <Text style={styles.benefitText}>Значок верификации в профиле</Text>
            </View>
            <View style={styles.benefit}>
              <Icon name="trending-up" size={16} color="#4CAF50" />
              <Text style={styles.benefitText}>Больше просмотров и лайков</Text>
            </View>
            <View style={styles.benefit}>
              <Icon name="security" size={16} color="#4CAF50" />
              <Text style={styles.benefitText}>Повышенное доверие пользователей</Text>
            </View>
            <View style={styles.benefit}>
              <Icon name="star" size={16} color="#4CAF50" />
              <Text style={styles.benefitText}>Приоритет в поиске</Text>
            </View>
          </View>

          {/* Шаги верификации */}
          <View style={styles.stepsContainer}>
            <Text style={styles.stepsTitle}>Шаги верификации</Text>
            {verificationSteps.map(renderVerificationStep)}
          </View>

          {/* Информация о безопасности */}
          <View style={styles.securityContainer}>
            <Icon name="security" size={24} color="#FFFFFF" />
            <Text style={styles.securityTitle}>Ваши данные в безопасности</Text>
            <Text style={styles.securityText}>
              Все документы шифруются и используются только для верификации. 
              Мы не передаем ваши данные третьим лицам.
            </Text>
          </View>
        </ScrollView>

        {/* Загрузка */}
        {isLoading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator color="#FFFFFF" size="large" />
            <Text style={styles.loadingText}>
              {currentStep ? 'Отправка документа...' : 'Загрузка...'}
            </Text>
          </View>
        )}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  scrollContent: {
    padding: 20,
  },
  progressContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  progressPercentage: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    marginBottom: 15,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    borderRadius: 10,
    padding: 10,
  },
  verifiedText: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  benefitsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
  },
  benefitsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 15,
  },
  benefit: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  benefitText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    marginLeft: 10,
  },
  stepsContainer: {
    marginBottom: 20,
  },
  stepsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 15,
  },
  stepContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  stepCompleted: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  stepRejected: {
    backgroundColor: 'rgba(244, 67, 54, 0.2)',
    borderWidth: 1,
    borderColor: '#F44336',
  },
  stepIcon: {
    width: 40,
    alignItems: 'center',
    marginRight: 15,
  },
  stepContent: {
    flex: 1,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
  },
  requiredBadge: {
    backgroundColor: '#FF9800',
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  stepDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 10,
  },
  rejectionReason: {
    fontSize: 12,
    color: '#F44336',
    fontStyle: 'italic',
    marginBottom: 10,
  },
  stepPhoto: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginTop: 5,
  },
  stepStatus: {
    alignItems: 'center',
    minWidth: 80,
  },
  stepStatusText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
    textAlign: 'center',
  },
  securityContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 10,
    marginBottom: 10,
  },
  securityText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 20,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginTop: 10,
  },
});

export default VerificationScreen;
