import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');
const CARD_WIDTH = (width - 60) / 2;

interface MatchesListScreenProps {
  navigation: any;
}

interface Match {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  age: number;
  city: string;
  mainPhoto: string;
  isOnline: boolean;
  isVerified: boolean;
  isPremium: boolean;
  matchedAt: string;
  mutualFriends: number;
  commonInterests: string[];
  distance: number;
  compatibility: number;
  lastMessage?: {
    text: string;
    timestamp: string;
    isRead: boolean;
    senderId: string;
  };
  unreadCount: number;
  isActive: boolean;
  lastSeen?: string;
  photos: string[];
}

const MatchesListScreen: React.FC<MatchesListScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { getMatches, unmatchUser } = useAuth();

  const [matches, setMatches] = useState<Match[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [filter, setFilter] = useState<'all' | 'new' | 'active' | 'unread'>('all');
  const [sortBy, setSortBy] = useState<'recent' | 'compatibility' | 'activity'>('recent');

  useEffect(() => {
    loadMatches();
  }, [filter, sortBy]);

  const loadMatches = async (pageNum = 1, refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const result = await getMatches({
        page: pageNum,
        limit: 20,
        filter,
        sortBy,
      });

      if (result.success) {
        const newMatches = result.matches || [];
        
        if (refresh || pageNum === 1) {
          setMatches(newMatches);
        } else {
          setMatches(prev => [...prev, ...newMatches]);
        }

        setHasMore(newMatches.length === 20);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('Error loading matches:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = useCallback(() => {
    loadMatches(1, true);
  }, [filter, sortBy]);

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      loadMatches(page + 1);
    }
  };

  const handleMatchPress = (match: Match) => {
    navigation.navigate('MatchDetailScreen', { 
      matchId: match.id,
      userId: match.userId 
    });
  };

  const handleChatPress = (match: Match) => {
    navigation.navigate('ChatScreen', { 
      userId: match.userId,
      userName: `${match.firstName} ${match.lastName}`,
      userPhoto: match.mainPhoto,
    });
  };

  const handleUnmatch = async (match: Match) => {
    Alert.alert(
      'Отменить матч',
      `Вы уверены, что хотите отменить матч с ${match.firstName}? Это действие нельзя отменить.`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отменить матч',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await unmatchUser(match.userId);
              if (result.success) {
                setMatches(prev => prev.filter(m => m.id !== match.id));
                Alert.alert('Успешно', 'Матч отменен');
              } else {
                Alert.alert('Ошибка', result.message || 'Не удалось отменить матч');
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Произошла ошибка при отмене матча');
            }
          },
        },
      ]
    );
  };

  const formatMatchTime = (matchedAt: string) => {
    const now = new Date();
    const matched = new Date(matchedAt);
    const diffInDays = Math.floor((now.getTime() - matched.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays < 1) {
      return 'Сегодня';
    } else if (diffInDays === 1) {
      return 'Вчера';
    } else if (diffInDays < 7) {
      return `${diffInDays} дн. назад`;
    } else {
      const diffInWeeks = Math.floor(diffInDays / 7);
      return `${diffInWeeks} нед. назад`;
    }
  };

  const formatLastMessage = (message: string) => {
    if (message.length > 30) {
      return message.substring(0, 30) + '...';
    }
    return message;
  };

  const getCompatibilityColor = (compatibility: number) => {
    if (compatibility >= 80) return '#4CAF50';
    if (compatibility >= 60) return '#FF9800';
    return '#F44336';
  };

  const renderMatch = ({ item }: { item: Match }) => (
    <TouchableOpacity
      style={styles.matchCard}
      onPress={() => handleMatchPress(item)}
    >
      <View style={styles.imageContainer}>
        <Image source={{ uri: item.mainPhoto }} style={styles.userImage} />
        
        {/* Онлайн статус */}
        {item.isOnline && (
          <View style={styles.onlineIndicator} />
        )}

        {/* Верификация */}
        {item.isVerified && (
          <View style={styles.verifiedBadge}>
            <Icon name="verified" size={14} color="#4CAF50" />
          </View>
        )}

        {/* Премиум */}
        {item.isPremium && (
          <View style={styles.premiumBadge}>
            <Icon name="star" size={14} color="#FFD700" />
          </View>
        )}

        {/* Совместимость */}
        <View style={[styles.compatibilityBadge, { backgroundColor: getCompatibilityColor(item.compatibility) }]}>
          <Text style={styles.compatibilityText}>{item.compatibility}%</Text>
        </View>

        {/* Непрочитанные сообщения */}
        {item.unreadCount > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadText}>{item.unreadCount > 99 ? '99+' : item.unreadCount}</Text>
          </View>
        )}

        {/* Градиент снизу */}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.imageGradient}
        />

        {/* Информация о пользователе */}
        <View style={styles.userInfo}>
          <Text style={styles.userName}>
            {item.firstName}, {item.age}
          </Text>
          <View style={styles.userLocation}>
            <Icon name="location-on" size={12} color="#FFFFFF" />
            <Text style={styles.locationText}>{item.distance} км</Text>
          </View>
        </View>
      </View>

      {/* Дополнительная информация */}
      <View style={styles.cardContent}>
        <View style={styles.matchInfo}>
          <Text style={styles.matchTime}>{formatMatchTime(item.matchedAt)}</Text>
          <View style={styles.heartsContainer}>
            <Icon name="favorite" size={16} color="#FF6B9D" />
            <Icon name="favorite" size={16} color="#FF6B9D" style={styles.secondHeart} />
          </View>
        </View>
        
        {/* Общие интересы */}
        {item.commonInterests.length > 0 && (
          <View style={styles.commonInterests}>
            <Icon name="favorite" size={12} color="#FF6B9D" />
            <Text style={styles.commonInterestsText}>
              {item.commonInterests.slice(0, 2).join(', ')}
              {item.commonInterests.length > 2 && ` +${item.commonInterests.length - 2}`}
            </Text>
          </View>
        )}

        {/* Последнее сообщение */}
        {item.lastMessage && (
          <View style={styles.lastMessage}>
            <Text style={[
              styles.lastMessageText,
              !item.lastMessage.isRead && styles.unreadMessageText
            ]} numberOfLines={1}>
              {formatLastMessage(item.lastMessage.text)}
            </Text>
          </View>
        )}

        {/* Статус активности */}
        {!item.isActive && item.lastSeen && (
          <Text style={styles.lastSeenText}>
            Был(а) в сети {formatMatchTime(item.lastSeen)}
          </Text>
        )}
      </View>

      {/* Действия */}
      <View style={styles.cardActions}>
        <TouchableOpacity
          style={styles.unmatchButton}
          onPress={() => handleUnmatch(item)}
        >
          <Icon name="close" size={16} color="#F44336" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.chatButton}
          onPress={() => handleChatPress(item)}
        >
          <Icon name="chat" size={16} color="#FFFFFF" />
          <Text style={styles.chatButtonText}>Написать</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={styles.emptyHeartsContainer}>
        <Icon name="favorite" size={60} color="rgba(255, 255, 255, 0.3)" />
        <Icon name="favorite" size={60} color="rgba(255, 255, 255, 0.5)" style={styles.emptySecondHeart} />
      </View>
      <Text style={styles.emptyTitle}>Пока нет матчей</Text>
      <Text style={styles.emptySubtitle}>
        Когда кто-то ответит взаимностью на ваш лайк, матчи появятся здесь
      </Text>
      <TouchableOpacity
        style={styles.startLikingButton}
        onPress={() => navigation.navigate('SearchScreen')}
      >
        <Text style={styles.startLikingText}>Начать ставить лайки</Text>
      </TouchableOpacity>
    </View>
  );

  const renderFooter = () => {
    if (!isLoading || isRefreshing) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator color="#FFFFFF" size="small" />
      </View>
    );
  };

  const renderFilterTabs = () => (
    <View style={styles.filterContainer}>
      {[
        { key: 'all', label: 'Все', count: matches.length },
        { key: 'new', label: 'Новые', count: matches.filter(m => new Date(m.matchedAt) > new Date(Date.now() - 24 * 60 * 60 * 1000)).length },
        { key: 'active', label: 'Активные', count: matches.filter(m => m.isActive).length },
        { key: 'unread', label: 'Непрочитанные', count: matches.filter(m => m.unreadCount > 0).length },
      ].map((tab) => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.filterTab,
            filter === tab.key && styles.filterTabActive,
          ]}
          onPress={() => setFilter(tab.key as any)}
        >
          <Text
            style={[
              styles.filterTabText,
              filter === tab.key && styles.filterTabTextActive,
            ]}
          >
            {tab.label}
          </Text>
          {tab.count > 0 && (
            <View style={styles.filterTabBadge}>
              <Text style={styles.filterTabBadgeText}>{tab.count}</Text>
            </View>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderSortOptions = () => (
    <View style={styles.sortContainer}>
      <Text style={styles.sortLabel}>Сортировка:</Text>
      {[
        { key: 'recent', label: 'Недавние' },
        { key: 'compatibility', label: 'Совместимость' },
        { key: 'activity', label: 'Активность' },
      ].map((option) => (
        <TouchableOpacity
          key={option.key}
          style={[
            styles.sortOption,
            sortBy === option.key && styles.sortOptionActive,
          ]}
          onPress={() => setSortBy(option.key as any)}
        >
          <Text
            style={[
              styles.sortOptionText,
              sortBy === option.key && styles.sortOptionTextActive,
            ]}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Матчи</Text>
          <View style={styles.headerRight}>
            <Text style={styles.countText}>{matches.length}</Text>
          </View>
        </View>

        {/* Фильтры */}
        {renderFilterTabs()}

        {/* Сортировка */}
        {renderSortOptions()}

        {/* Список матчей */}
        <FlatList
          data={matches}
          renderItem={renderMatch}
          keyExtractor={(item) => item.id}
          numColumns={2}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
              colors={['#FFFFFF']}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={!isLoading ? renderEmptyState : null}
        />
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  countText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  filterTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 8,
    paddingHorizontal: 10,
    marginHorizontal: 2,
  },
  filterTabActive: {
    backgroundColor: '#FFFFFF',
  },
  filterTabText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 12,
    fontWeight: '600',
  },
  filterTabTextActive: {
    color: '#FF6B9D',
  },
  filterTabBadge: {
    backgroundColor: '#FF6B9D',
    borderRadius: 8,
    paddingHorizontal: 4,
    paddingVertical: 1,
    marginLeft: 4,
  },
  filterTabBadgeText: {
    color: '#FFFFFF',
    fontSize: 9,
    fontWeight: 'bold',
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sortLabel: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 10,
  },
  sortOption: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 8,
    marginRight: 6,
  },
  sortOptionActive: {
    backgroundColor: '#FFFFFF',
  },
  sortOptionText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 11,
    fontWeight: '600',
  },
  sortOptionTextActive: {
    color: '#FF6B9D',
  },
  listContainer: {
    padding: 20,
  },
  matchCard: {
    width: CARD_WIDTH,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    margin: 5,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  imageContainer: {
    position: 'relative',
    height: CARD_WIDTH * 1.2,
  },
  userImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  onlineIndicator: {
    position: 'absolute',
    top: 8,
    left: 8,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  verifiedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    padding: 2,
  },
  premiumBadge: {
    position: 'absolute',
    top: 30,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    padding: 2,
  },
  compatibilityBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  compatibilityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  unreadBadge: {
    position: 'absolute',
    top: 8,
    left: 30,
    backgroundColor: '#FF6B9D',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  unreadText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  imageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  userInfo: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 50,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  userLocation: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 12,
    color: '#FFFFFF',
    marginLeft: 2,
  },
  cardContent: {
    padding: 12,
  },
  matchInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  matchTime: {
    fontSize: 12,
    color: '#666',
  },
  heartsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  secondHeart: {
    marginLeft: -6,
  },
  commonInterests: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  commonInterestsText: {
    fontSize: 11,
    color: '#FF6B9D',
    marginLeft: 4,
    flex: 1,
  },
  lastMessage: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginBottom: 4,
  },
  lastMessageText: {
    fontSize: 11,
    color: '#333',
  },
  unreadMessageText: {
    fontWeight: 'bold',
    color: '#FF6B9D',
  },
  lastSeenText: {
    fontSize: 10,
    color: '#999',
    fontStyle: 'italic',
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingBottom: 12,
  },
  unmatchButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flex: 1,
    marginLeft: 8,
    justifyContent: 'center',
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyHeartsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  emptySecondHeart: {
    marginLeft: -20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  startLikingButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
  },
  startLikingText: {
    color: '#FF6B9D',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

export default MatchesListScreen;
