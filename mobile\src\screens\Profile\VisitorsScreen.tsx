import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface VisitorsScreenProps {
  navigation: any;
}

interface Visitor {
  id: string;
  firstName: string;
  lastName: string;
  age: number;
  city: string;
  mainPhoto: string;
  isOnline: boolean;
  isVerified: boolean;
  isPremium: boolean;
  visitDate: string;
  mutualFriends: number;
  commonInterests: string[];
  distance: number;
  hasLiked: boolean;
  hasMatched: boolean;
}

const VisitorsScreen: React.FC<VisitorsScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { getProfileVisitors, likeUser, passUser } = useAuth();

  const [visitors, setVisitors] = useState<Visitor[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadVisitors();
  }, []);

  const loadVisitors = async (pageNum = 1, refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const result = await getProfileVisitors({
        page: pageNum,
        limit: 20,
      });

      if (result.success) {
        const newVisitors = result.visitors || [];
        
        if (refresh || pageNum === 1) {
          setVisitors(newVisitors);
        } else {
          setVisitors(prev => [...prev, ...newVisitors]);
        }

        setHasMore(newVisitors.length === 20);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('Error loading visitors:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = useCallback(() => {
    loadVisitors(1, true);
  }, []);

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      loadVisitors(page + 1);
    }
  };

  const handleVisitorPress = (visitor: Visitor) => {
    navigation.navigate('UserProfileScreen', { userId: visitor.id });
  };

  const handleLikeUser = async (visitor: Visitor) => {
    try {
      const result = await likeUser(visitor.id);
      if (result.success) {
        // Обновляем статус лайка
        setVisitors(prev =>
          prev.map(v =>
            v.id === visitor.id
              ? { ...v, hasLiked: true, hasMatched: result.isMatch }
              : v
          )
        );

        if (result.isMatch) {
          // Показываем уведомление о матче
          navigation.navigate('MatchScreen', { matchedUser: visitor });
        }
      }
    } catch (error) {
      console.error('Error liking user:', error);
    }
  };

  const handlePassUser = async (visitor: Visitor) => {
    try {
      await passUser(visitor.id);
      // Удаляем пользователя из списка
      setVisitors(prev => prev.filter(v => v.id !== visitor.id));
    } catch (error) {
      console.error('Error passing user:', error);
    }
  };

  const formatVisitTime = (visitDate: string) => {
    const now = new Date();
    const visit = new Date(visitDate);
    const diffInHours = Math.floor((now.getTime() - visit.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  const renderVisitor = ({ item }: { item: Visitor }) => (
    <TouchableOpacity
      style={styles.visitorCard}
      onPress={() => handleVisitorPress(item)}
    >
      <View style={styles.visitorImageContainer}>
        <Image source={{ uri: item.mainPhoto }} style={styles.visitorImage} />
        
        {/* Онлайн статус */}
        {item.isOnline && (
          <View style={styles.onlineIndicator} />
        )}

        {/* Верификация */}
        {item.isVerified && (
          <View style={styles.verifiedBadge}>
            <Icon name="verified" size={16} color="#4CAF50" />
          </View>
        )}

        {/* Премиум */}
        {item.isPremium && (
          <View style={styles.premiumBadge}>
            <Icon name="star" size={16} color="#FFD700" />
          </View>
        )}
      </View>

      <View style={styles.visitorInfo}>
        <View style={styles.visitorHeader}>
          <Text style={styles.visitorName}>
            {item.firstName} {item.lastName}
          </Text>
          <Text style={styles.visitTime}>
            {formatVisitTime(item.visitDate)}
          </Text>
        </View>

        <View style={styles.visitorDetails}>
          <Text style={styles.visitorAge}>{item.age} лет</Text>
          <Text style={styles.visitorLocation}>
            <Icon name="location-on" size={12} color="#666" />
            {' '}{item.city} • {item.distance} км
          </Text>
        </View>

        {/* Общие интересы */}
        {item.commonInterests.length > 0 && (
          <View style={styles.commonInterests}>
            <Icon name="favorite" size={12} color="#FF6B9D" />
            <Text style={styles.commonInterestsText}>
              {item.commonInterests.length} общих интереса
            </Text>
          </View>
        )}

        {/* Взаимные друзья */}
        {item.mutualFriends > 0 && (
          <View style={styles.mutualFriends}>
            <Icon name="group" size={12} color="#4ECDC4" />
            <Text style={styles.mutualFriendsText}>
              {item.mutualFriends} общих знакомых
            </Text>
          </View>
        )}

        {/* Статус матча */}
        {item.hasMatched && (
          <View style={styles.matchBadge}>
            <Icon name="favorite" size={14} color="#FFFFFF" />
            <Text style={styles.matchText}>Взаимная симпатия</Text>
          </View>
        )}
      </View>

      {/* Действия */}
      <View style={styles.visitorActions}>
        {!item.hasLiked && !item.hasMatched && (
          <>
            <TouchableOpacity
              style={styles.passButton}
              onPress={() => handlePassUser(item)}
            >
              <Icon name="close" size={20} color="#666" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.likeButton}
              onPress={() => handleLikeUser(item)}
            >
              <Icon name="favorite" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </>
        )}

        {(item.hasLiked || item.hasMatched) && (
          <TouchableOpacity
            style={styles.chatButton}
            onPress={() => navigation.navigate('ChatScreen', { userId: item.id })}
          >
            <Icon name="chat" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="visibility-off" size={80} color="rgba(255, 255, 255, 0.5)" />
      <Text style={styles.emptyTitle}>Пока никто не посещал ваш профиль</Text>
      <Text style={styles.emptySubtitle}>
        Улучшите свой профиль, добавьте больше фотографий и будьте активны в приложении
      </Text>
      <TouchableOpacity
        style={styles.improveProfileButton}
        onPress={() => navigation.navigate('EditProfileScreen')}
      >
        <Text style={styles.improveProfileText}>Улучшить профиль</Text>
      </TouchableOpacity>
    </View>
  );

  const renderFooter = () => {
    if (!isLoading || isRefreshing) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator color="#FFFFFF" size="small" />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Посетители профиля</Text>
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => {/* Открыть фильтры */}}
          >
            <Icon name="filter-list" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Статистика */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{visitors.length}</Text>
            <Text style={styles.statLabel}>Всего посещений</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {visitors.filter(v => v.isOnline).length}
            </Text>
            <Text style={styles.statLabel}>Сейчас онлайн</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {visitors.filter(v => v.hasLiked || v.hasMatched).length}
            </Text>
            <Text style={styles.statLabel}>Взаимные лайки</Text>
          </View>
        </View>

        {/* Список посетителей */}
        <FlatList
          data={visitors}
          renderItem={renderVisitor}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
              colors={['#FFFFFF']}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={!isLoading ? renderEmptyState : null}
        />
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  filterButton: {
    padding: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  listContainer: {
    padding: 20,
  },
  visitorCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  visitorImageContainer: {
    position: 'relative',
    marginRight: 15,
  },
  visitorImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  verifiedBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 2,
  },
  premiumBadge: {
    position: 'absolute',
    top: -2,
    left: -2,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 2,
  },
  visitorInfo: {
    flex: 1,
  },
  visitorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  visitorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  visitTime: {
    fontSize: 12,
    color: '#666',
  },
  visitorDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  visitorAge: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  visitorLocation: {
    fontSize: 12,
    color: '#666',
    flex: 1,
  },
  commonInterests: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  commonInterestsText: {
    fontSize: 12,
    color: '#FF6B9D',
    marginLeft: 4,
  },
  mutualFriends: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  mutualFriendsText: {
    fontSize: 12,
    color: '#4ECDC4',
    marginLeft: 4,
  },
  matchBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B9D',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
  },
  matchText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  visitorActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  likeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#FF6B9D',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#4ECDC4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  improveProfileButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
  },
  improveProfileText: {
    color: '#FF6B9D',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

export default VisitorsScreen;
