import React, { useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Report as ReportIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  ExitToApp as LogoutIcon,
  AccountCircle as AccountIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { useThemeMode } from '../../hooks/useThemeMode';
import styles from '../../styles/admin/AdminLayout.module.css';

const DRAWER_WIDTH = 280;

interface AdminLayoutProps {
  children: React.ReactNode;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  badge?: number;
}

export const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();
  const { mode, toggleMode } = useThemeMode();

  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Дашборд',
      icon: <DashboardIcon />,
      path: '/admin',
    },
    {
      id: 'users',
      label: 'Пользователи',
      icon: <PeopleIcon />,
      path: '/admin/users',
    },
    {
      id: 'reports',
      label: 'Жалобы и отчеты',
      icon: <ReportIcon />,
      path: '/admin/reports',
      badge: 5, // Количество новых жалоб
    },
    {
      id: 'analytics',
      label: 'Аналитика',
      icon: <AnalyticsIcon />,
      path: '/admin/analytics',
    },
    {
      id: 'moderation',
      label: 'Модерация',
      icon: <SecurityIcon />,
      path: '/admin/moderation',
      badge: 12, // Количество элементов на модерации
    },
    {
      id: 'settings',
      label: 'Настройки',
      icon: <SettingsIcon />,
      path: '/admin/settings',
    },
  ];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    handleMenuClose();
    await logout();
    router.push('/');
  };

  const handleNavigation = (path: string) => {
    router.push(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const isActiveRoute = (path: string) => {
    if (path === '/admin') {
      return router.pathname === '/admin';
    }
    return router.pathname.startsWith(path);
  };

  const drawer = (
    <Box className={styles.drawer}>
      {/* Логотип и заголовок */}
      <Box className={styles.drawerHeader}>
        <Typography variant="h6" className={styles.logo}>
          Likes & Love
        </Typography>
        <Typography variant="caption" className={styles.adminLabel}>
          Админ-панель
        </Typography>
      </Box>

      <Divider />

      {/* Навигация */}
      <List className={styles.navigationList}>
        {navigationItems.map((item) => (
          <ListItem key={item.id} disablePadding>
            <ListItemButton
              onClick={() => handleNavigation(item.path)}
              className={`${styles.navItem} ${
                isActiveRoute(item.path) ? styles.navItemActive : ''
              }`}
            >
              <ListItemIcon className={styles.navIcon}>
                {item.badge ? (
                  <Badge badgeContent={item.badge} color="error">
                    {item.icon}
                  </Badge>
                ) : (
                  item.icon
                )}
              </ListItemIcon>
              <ListItemText 
                primary={item.label}
                className={styles.navText}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider />

      {/* Информация о пользователе */}
      <Box className={styles.userInfo}>
        <Avatar
          src={user?.avatar}
          alt={user?.firstName}
          className={styles.userAvatar}
        >
          {user?.firstName?.[0]}{user?.lastName?.[0]}
        </Avatar>
        <Box className={styles.userDetails}>
          <Typography variant="body2" className={styles.userName}>
            {user?.firstName} {user?.lastName}
          </Typography>
          <Typography variant="caption" className={styles.userRole}>
            Администратор
          </Typography>
        </Box>
      </Box>
    </Box>
  );

  return (
    <Box className={styles.root}>
      {/* Верхняя панель */}
      <AppBar
        position="fixed"
        className={styles.appBar}
        sx={{
          width: { md: `calc(100% - ${DRAWER_WIDTH}px)` },
          ml: { md: `${DRAWER_WIDTH}px` },
        }}
      >
        <Toolbar className={styles.toolbar}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" noWrap component="div" className={styles.title}>
            {navigationItems.find(item => isActiveRoute(item.path))?.label || 'Админ-панель'}
          </Typography>

          <Box className={styles.headerActions}>
            {/* Переключатель темы */}
            <Tooltip title={mode === 'dark' ? 'Светлая тема' : 'Темная тема'}>
              <IconButton onClick={toggleMode} color="inherit">
                {mode === 'dark' ? <LightModeIcon /> : <DarkModeIcon />}
              </IconButton>
            </Tooltip>

            {/* Уведомления */}
            <Tooltip title="Уведомления">
              <IconButton color="inherit">
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>

            {/* Меню пользователя */}
            <Tooltip title="Профиль">
              <IconButton
                onClick={handleMenuOpen}
                color="inherit"
                className={styles.profileButton}
              >
                <Avatar
                  src={user?.avatar}
                  alt={user?.firstName}
                  className={styles.profileAvatar}
                >
                  {user?.firstName?.[0]}{user?.lastName?.[0]}
                </Avatar>
              </IconButton>
            </Tooltip>

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              className={styles.profileMenu}
            >
              <MenuItem onClick={handleMenuClose}>
                <ListItemIcon>
                  <AccountIcon fontSize="small" />
                </ListItemIcon>
                Профиль
              </MenuItem>
              <MenuItem onClick={handleMenuClose}>
                <ListItemIcon>
                  <SettingsIcon fontSize="small" />
                </ListItemIcon>
                Настройки
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleLogout}>
                <ListItemIcon>
                  <LogoutIcon fontSize="small" />
                </ListItemIcon>
                Выйти
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Боковая панель */}
      <Box
        component="nav"
        sx={{ width: { md: DRAWER_WIDTH }, flexShrink: { md: 0 } }}
      >
        {/* Мобильная версия */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Лучшая производительность на мобильных
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: DRAWER_WIDTH,
            },
          }}
        >
          {drawer}
        </Drawer>

        {/* Десктопная версия */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: DRAWER_WIDTH,
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Основной контент */}
      <Box
        component="main"
        className={styles.content}
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${DRAWER_WIDTH}px)` },
        }}
      >
        <Toolbar /> {/* Отступ для AppBar */}
        {children}
      </Box>
    </Box>
  );
};
