import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { meetingsService } from '../../services/meetingsService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { Meeting, MeetingStatus, MeetingFilters } from '../../types/meetings.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface MeetingsScreenProps {}

const MeetingsScreen: React.FC<MeetingsScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location } = useLocation();
  const queryClient = useQueryClient();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'upcoming' | 'past' | 'invitations'>('upcoming');
  const [filters, setFilters] = useState<MeetingFilters>({
    status: undefined,
    dateFrom: undefined,
    dateTo: undefined,
    location: undefined,
  });

  // Загрузка встреч
  const {
    data: meetings,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['meetings', selectedTab, filters],
    queryFn: () => meetingsService.getMeetings(selectedTab, filters),
  });

  // Мутация для ответа на приглашение
  const respondToInvitationMutation = useMutation({
    mutationFn: ({ meetingId, response }: { meetingId: string; response: 'accept' | 'decline' }) =>
      meetingsService.respondToInvitation(meetingId, response),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meetings'] });
    },
  });

  // Мутация для отмены встречи
  const cancelMeetingMutation = useMutation({
    mutationFn: (meetingId: string) => meetingsService.cancelMeeting(meetingId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meetings'] });
    },
  });

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleAcceptInvitation = (meetingId: string) => {
    Alert.alert(
      'Принять приглашение',
      'Вы уверены, что хотите принять это приглашение?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Принять',
          onPress: () => respondToInvitationMutation.mutate({ meetingId, response: 'accept' }),
        },
      ]
    );
  };

  const handleDeclineInvitation = (meetingId: string) => {
    Alert.alert(
      'Отклонить приглашение',
      'Вы уверены, что хотите отклонить это приглашение?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отклонить',
          style: 'destructive',
          onPress: () => respondToInvitationMutation.mutate({ meetingId, response: 'decline' }),
        },
      ]
    );
  };

  const handleCancelMeeting = (meetingId: string) => {
    Alert.alert(
      'Отменить встречу',
      'Вы уверены, что хотите отменить эту встречу?',
      [
        { text: 'Нет', style: 'cancel' },
        {
          text: 'Отменить встречу',
          style: 'destructive',
          onPress: () => cancelMeetingMutation.mutate(meetingId),
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Сегодня';
    if (diffInDays === 1) return 'Завтра';
    if (diffInDays === -1) return 'Вчера';
    if (diffInDays > 1 && diffInDays <= 7) return `Через ${diffInDays} дн.`;
    if (diffInDays < -1 && diffInDays >= -7) return `${Math.abs(diffInDays)} дн. назад`;

    return date.toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'short',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: MeetingStatus) => {
    switch (status) {
      case 'confirmed':
        return colors.success;
      case 'pending':
        return colors.warning;
      case 'cancelled':
        return colors.error;
      case 'completed':
        return colors.primary;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusText = (status: MeetingStatus) => {
    switch (status) {
      case 'confirmed':
        return 'Подтверждена';
      case 'pending':
        return 'Ожидает';
      case 'cancelled':
        return 'Отменена';
      case 'completed':
        return 'Завершена';
      default:
        return 'Неизвестно';
    }
  };

  const renderTabButton = (tab: typeof selectedTab, label: string, count?: number) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        selectedTab === tab && styles.tabButtonActive,
      ]}
      onPress={() => setSelectedTab(tab)}
    >
      <Text
        style={[
          styles.tabButtonText,
          selectedTab === tab && styles.tabButtonTextActive,
        ]}
      >
        {label}
      </Text>
      {count !== undefined && count > 0 && (
        <View style={styles.tabBadge}>
          <Text style={styles.tabBadgeText}>{count}</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderMeetingCard = ({ item, index }: { item: Meeting; index: number }) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateY: interpolate(animatedValue.value, [0, 1], [30, 0]),
          },
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.95, 1]),
          },
        ],
      };
    });

    const isUpcoming = new Date(item.dateTime) > new Date();
    const isPending = item.status === 'pending';
    const isInvitation = selectedTab === 'invitations';

    return (
      <Animated.View style={[styles.meetingCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('MeetingDetail', { meetingId: item.id })}
          activeOpacity={0.8}
        >
          <View style={styles.meetingHeader}>
            <View style={styles.meetingInfo}>
              <Text style={styles.meetingTitle}>{item.title}</Text>
              <View style={styles.meetingDateTime}>
                <Ionicons name="calendar-outline" size={16} color={colors.textSecondary} />
                <Text style={styles.dateTimeText}>
                  {formatDate(item.dateTime)} в {formatTime(item.dateTime)}
                </Text>
              </View>
              <View style={styles.meetingLocation}>
                <Ionicons name="location-outline" size={16} color={colors.textSecondary} />
                <Text style={styles.locationText}>{item.location.name}</Text>
              </View>
            </View>
            
            <View style={styles.meetingStatus}>
              <View
                style={[
                  styles.statusIndicator,
                  { backgroundColor: getStatusColor(item.status) },
                ]}
              />
              <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
                {getStatusText(item.status)}
              </Text>
            </View>
          </View>

          <View style={styles.participantsSection}>
            <Text style={styles.participantsTitle}>Участники:</Text>
            <View style={styles.participantsList}>
              {item.participants.slice(0, 3).map((participant, idx) => (
                <View key={participant.id} style={styles.participantItem}>
                  <Image
                    source={{ uri: participant.avatar }}
                    style={[
                      styles.participantAvatar,
                      { marginLeft: idx > 0 ? -8 : 0 },
                    ]}
                  />
                </View>
              ))}
              {item.participants.length > 3 && (
                <View style={[styles.participantAvatar, styles.moreParticipants]}>
                  <Text style={styles.moreParticipantsText}>
                    +{item.participants.length - 3}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {item.description && (
            <Text style={styles.meetingDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}
        </TouchableOpacity>

        {/* Действия */}
        <View style={styles.meetingActions}>
          {isInvitation && isPending && (
            <>
              <TouchableOpacity
                style={[styles.actionButton, styles.declineButton]}
                onPress={() => handleDeclineInvitation(item.id)}
                disabled={respondToInvitationMutation.isPending}
              >
                <Ionicons name="close" size={20} color={colors.error} />
                <Text style={[styles.actionButtonText, { color: colors.error }]}>
                  Отклонить
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.actionButton, styles.acceptButton]}
                onPress={() => handleAcceptInvitation(item.id)}
                disabled={respondToInvitationMutation.isPending}
              >
                <Ionicons name="checkmark" size={20} color={colors.success} />
                <Text style={[styles.actionButtonText, { color: colors.success }]}>
                  Принять
                </Text>
              </TouchableOpacity>
            </>
          )}

          {isUpcoming && item.status === 'confirmed' && (
            <>
              <TouchableOpacity
                style={[styles.actionButton, styles.editButton]}
                onPress={() => navigation.navigate('EditMeeting', { meetingId: item.id })}
              >
                <Ionicons name="create-outline" size={20} color={colors.primary} />
                <Text style={[styles.actionButtonText, { color: colors.primary }]}>
                  Изменить
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.actionButton, styles.cancelButton]}
                onPress={() => handleCancelMeeting(item.id)}
                disabled={cancelMeetingMutation.isPending}
              >
                <Ionicons name="trash-outline" size={20} color={colors.error} />
                <Text style={[styles.actionButtonText, { color: colors.error }]}>
                  Отменить
                </Text>
              </TouchableOpacity>
            </>
          )}

          <TouchableOpacity
            style={[styles.actionButton, styles.chatButton]}
            onPress={() => navigation.navigate('MeetingChat', { meetingId: item.id })}
          >
            <Ionicons name="chatbubble-outline" size={20} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>
              Чат
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  if (isLoading && !meetings) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Загружаем встречи...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="calendar-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить список встреч
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Встречи</Text>
        
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => navigation.navigate('CreateMeeting')}
        >
          <Ionicons name="add" size={24} color={colors.white} />
        </TouchableOpacity>
      </View>

      {/* Вкладки */}
      <View style={styles.tabsContainer}>
        {renderTabButton('upcoming', 'Предстоящие', meetings?.upcoming?.length)}
        {renderTabButton('invitations', 'Приглашения', meetings?.invitations?.length)}
        {renderTabButton('past', 'Прошедшие', meetings?.past?.length)}
      </View>

      {/* Список встреч */}
      <FlatList
        data={meetings?.[selectedTab] || []}
        renderItem={renderMeetingCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons
              name={
                selectedTab === 'upcoming'
                  ? 'calendar-outline'
                  : selectedTab === 'invitations'
                  ? 'mail-outline'
                  : 'time-outline'
              }
              size={64}
              color={colors.textSecondary}
            />
            <Text style={styles.emptyTitle}>
              {selectedTab === 'upcoming' && 'Нет предстоящих встреч'}
              {selectedTab === 'invitations' && 'Нет приглашений'}
              {selectedTab === 'past' && 'Нет прошедших встреч'}
            </Text>
            <Text style={styles.emptyText}>
              {selectedTab === 'upcoming' && 'Создайте новую встречу или примите приглашение'}
              {selectedTab === 'invitations' && 'Здесь будут отображаться приглашения на встречи'}
              {selectedTab === 'past' && 'История ваших встреч появится здесь'}
            </Text>
            {selectedTab === 'upcoming' && (
              <TouchableOpacity
                style={styles.createMeetingButton}
                onPress={() => navigation.navigate('CreateMeeting')}
              >
                <Text style={styles.createMeetingButtonText}>Создать встречу</Text>
              </TouchableOpacity>
            )}
          </View>
        }
      />

      {/* Индикатор загрузки действий */}
      {(respondToInvitationMutation.isPending || cancelMeetingMutation.isPending) && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  createButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 20,
    marginHorizontal: spacing.xs,
    gap: spacing.xs,
  },
  tabButtonActive: {
    backgroundColor: colors.primary,
  },
  tabButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  tabButtonTextActive: {
    color: colors.white,
  },
  tabBadge: {
    backgroundColor: colors.error,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  listContainer: {
    padding: spacing.lg,
  },
  meetingCard: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  meetingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  meetingInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  meetingTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  meetingDateTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
    gap: spacing.xs,
  },
  dateTimeText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  meetingLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  locationText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  meetingStatus: {
    alignItems: 'center',
    gap: spacing.xs,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  statusText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
  participantsSection: {
    marginBottom: spacing.md,
  },
  participantsTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  participantsList: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  participantItem: {
    position: 'relative',
  },
  participantAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: colors.surface,
  },
  moreParticipants: {
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: -8,
  },
  moreParticipantsText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  meetingDescription: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    lineHeight: 20,
    marginBottom: spacing.md,
  },
  meetingActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 12,
    backgroundColor: colors.background,
    gap: spacing.xs,
  },
  acceptButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  declineButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  editButton: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  cancelButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  chatButton: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  createMeetingButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  createMeetingButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MeetingsScreen;
