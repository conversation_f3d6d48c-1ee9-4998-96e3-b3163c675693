import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  DatePicker,
  Chip,
  LinearProgress,
  Alert,
  Container,
  Paper,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Divider,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  AttachMoney as RevenueIcon,
  Public as GeoIcon,
  Download as ExportIcon,
  Refresh as RefreshIcon,
  DateRange as DateRangeIcon,
  Insights as InsightsIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
} from 'chart.js';
import { AdminLayout } from '../../../components/admin/AdminLayout';
import { useAuth } from '../../../hooks/useAuth';
import { adminService } from '../../../services/adminService';
import { AnalyticsData } from '../../../types/admin.types';
import styles from '../../../styles/admin/Analytics.module.css';

// Регистрируем компоненты Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement,
  RadialLinearScale
);

interface AnalyticsPageProps {}

const AnalyticsPage: React.FC<AnalyticsPageProps> = () => {
  const router = useRouter();
  const { isAdmin } = useAuth();

  const [dateRange, setDateRange] = useState<{ from: string; to: string }>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    to: new Date().toISOString().split('T')[0],
  });
  const [activeTab, setActiveTab] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0);

  // Проверка прав доступа
  useEffect(() => {
    if (!isAdmin) {
      router.push('/');
    }
  }, [isAdmin, router]);

  // Загрузка аналитических данных
  const {
    data: analyticsData,
    isLoading,
    error,
    refetch,
  } = useQuery<AnalyticsData>({
    queryKey: ['admin', 'analytics', dateRange, refreshKey],
    queryFn: () => adminService.getAnalytics(dateRange),
    enabled: isAdmin,
  });

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
    refetch();
  };

  const handleExport = async (type: 'users' | 'engagement' | 'revenue', format: 'csv' | 'xlsx') => {
    try {
      const blob = await adminService.exportAnalytics(type, format, dateRange);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type}_analytics_${dateRange.from}_${dateRange.to}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export error:', error);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const userGrowthData = {
    labels: analyticsData?.userMetrics.userGrowthChart?.map(item => item.label) || [],
    datasets: [
      {
        label: 'Новые пользователи',
        data: analyticsData?.userMetrics.userGrowthChart?.map(item => item.value) || [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1,
      },
    ],
  };

  const engagementData = {
    labels: analyticsData?.engagementMetrics.engagementChart?.map(item => item.label) || [],
    datasets: [
      {
        label: 'Активность пользователей',
        data: analyticsData?.engagementMetrics.engagementChart?.map(item => item.value) || [],
        backgroundColor: 'rgba(153, 102, 255, 0.8)',
        borderColor: 'rgba(153, 102, 255, 1)',
        borderWidth: 1,
      },
    ],
  };

  const revenueData = {
    labels: analyticsData?.revenueMetrics.revenueChart?.map(item => item.label) || [],
    datasets: [
      {
        label: 'Доход',
        data: analyticsData?.revenueMetrics.revenueChart?.map(item => item.value) || [],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1,
      },
    ],
  };

  const userDemographicsData = {
    labels: analyticsData?.userMetrics.usersByAge?.map(item => item.label) || [],
    datasets: [
      {
        data: analyticsData?.userMetrics.usersByAge?.map(item => item.value) || [],
        backgroundColor: [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 205, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)',
        ],
      },
    ],
  };

  const performanceData = {
    labels: ['Время отклика', 'Доступность', 'Производительность', 'Стабильность', 'Безопасность'],
    datasets: [
      {
        label: 'Показатели системы',
        data: [
          analyticsData?.performanceMetrics.averageResponseTime || 0,
          analyticsData?.performanceMetrics.uptime || 0,
          100 - (analyticsData?.performanceMetrics.errorRate || 0),
          analyticsData?.performanceMetrics.serverLoad || 0,
          95, // Статический показатель безопасности
        ],
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
    ],
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Аналитика - Админ-панель</title>
        <meta name="description" content="Аналитика и статистика приложения знакомств" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <AdminLayout>
        <Container maxWidth="xl" className={styles.container}>
          {/* Заголовок */}
          <Box className={styles.header}>
            <Box>
              <Typography variant="h4" component="h1" className={styles.title}>
                <AnalyticsIcon className={styles.titleIcon} />
                Аналитика и статистика
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Период: {new Date(dateRange.from).toLocaleDateString()} - {new Date(dateRange.to).toLocaleDateString()}
              </Typography>
            </Box>
            <Box className={styles.headerActions}>
              <FormControl size="small" className={styles.dateControl}>
                <InputLabel>Период</InputLabel>
                <Select
                  value="custom"
                  onChange={(e) => {
                    const value = e.target.value;
                    const now = new Date();
                    let from: Date;
                    
                    switch (value) {
                      case '7d':
                        from = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                        break;
                      case '30d':
                        from = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                        break;
                      case '90d':
                        from = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                        break;
                      default:
                        return;
                    }
                    
                    setDateRange({
                      from: from.toISOString().split('T')[0],
                      to: now.toISOString().split('T')[0],
                    });
                  }}
                >
                  <MenuItem value="7d">7 дней</MenuItem>
                  <MenuItem value="30d">30 дней</MenuItem>
                  <MenuItem value="90d">90 дней</MenuItem>
                  <MenuItem value="custom">Настроить</MenuItem>
                </Select>
              </FormControl>
              
              <Tooltip title="Обновить данные">
                <IconButton onClick={handleRefresh} className={styles.refreshButton}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              
              <Button
                variant="outlined"
                startIcon={<ExportIcon />}
                onClick={() => handleExport('users', 'xlsx')}
                className={styles.exportButton}
              >
                Экспорт
              </Button>
            </Box>
          </Box>

          {isLoading && <LinearProgress className={styles.loader} />}

          {error && (
            <Alert severity="error" className={styles.errorAlert}>
              Ошибка загрузки аналитики: {error.message}
            </Alert>
          )}

          {/* Основные метрики */}
          <Grid container spacing={3} className={styles.metricsGrid}>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.metricCard}>
                <CardContent>
                  <Box className={styles.metricContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Всего пользователей
                      </Typography>
                      <Typography variant="h4">
                        {formatNumber(analyticsData?.userMetrics.totalUsers || 0)}
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        Удержание: {analyticsData?.userMetrics.retentionRate || 0}%
                      </Typography>
                    </Box>
                    <PeopleIcon className={styles.metricIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.metricCard}>
                <CardContent>
                  <Box className={styles.metricContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Активные пользователи
                      </Typography>
                      <Typography variant="h4">
                        {formatNumber(analyticsData?.userMetrics.activeUsers || 0)}
                      </Typography>
                      <Typography variant="body2" color="info.main">
                        DAU: {formatNumber(analyticsData?.engagementMetrics.dailyActiveUsers || 0)}
                      </Typography>
                    </Box>
                    <TrendingUpIcon className={styles.metricIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.metricCard}>
                <CardContent>
                  <Box className={styles.metricContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Общий доход
                      </Typography>
                      <Typography variant="h4">
                        ₽{formatNumber(analyticsData?.revenueMetrics.totalRevenue || 0)}
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        ARPU: ₽{analyticsData?.revenueMetrics.averageRevenuePerUser || 0}
                      </Typography>
                    </Box>
                    <RevenueIcon className={styles.metricIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.metricCard}>
                <CardContent>
                  <Box className={styles.metricContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Конверсия подписок
                      </Typography>
                      <Typography variant="h4">
                        {analyticsData?.revenueMetrics.subscriptionConversionRate || 0}%
                      </Typography>
                      <Typography variant="body2" color="warning.main">
                        Месячный доход: ₽{formatNumber(analyticsData?.revenueMetrics.monthlyRevenue || 0)}
                      </Typography>
                    </Box>
                    <InsightsIcon className={styles.metricIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Вкладки аналитики */}
          <Card className={styles.tabsCard}>
            <Box className={styles.tabsHeader}>
              <Tabs
                value={activeTab}
                onChange={(_, newValue) => setActiveTab(newValue)}
                className={styles.tabs}
              >
                <Tab label="Пользователи" />
                <Tab label="Вовлеченность" />
                <Tab label="Доходы" />
                <Tab label="Производительность" />
              </Tabs>
            </Box>

            <CardContent>
              {/* Вкладка пользователей */}
              {activeTab === 0 && (
                <Grid container spacing={3}>
                  <Grid item xs={12} lg={8}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Рост пользователей
                      </Typography>
                      <Box className={styles.chartContainer}>
                        <Line data={userGrowthData} options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'top' as const,
                            },
                          },
                        }} />
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} lg={4}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Возрастные группы
                      </Typography>
                      <Box className={styles.chartContainer}>
                        <Doughnut data={userDemographicsData} options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom' as const,
                            },
                          },
                        }} />
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              )}

              {/* Вкладка вовлеченности */}
              {activeTab === 1 && (
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Активность пользователей
                      </Typography>
                      <Box className={styles.chartContainer}>
                        <Bar data={engagementData} options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false,
                            },
                          },
                        }} />
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card className={styles.statCard}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Среднее время сессии
                        </Typography>
                        <Typography variant="h4" color="primary">
                          {Math.round((analyticsData?.engagementMetrics.averageSessionDuration || 0) / 60)} мин
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card className={styles.statCard}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Сообщений в день
                        </Typography>
                        <Typography variant="h4" color="secondary">
                          {formatNumber(analyticsData?.engagementMetrics.messagesPerDay || 0)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              )}

              {/* Вкладка доходов */}
              {activeTab === 2 && (
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Динамика доходов
                      </Typography>
                      <Box className={styles.chartContainer}>
                        <Line data={revenueData} options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'top' as const,
                            },
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              ticks: {
                                callback: function(value) {
                                  return '₽' + formatNumber(Number(value));
                                },
                              },
                            },
                          },
                        }} />
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              )}

              {/* Вкладка производительности */}
              {activeTab === 3 && (
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Показатели системы
                      </Typography>
                      <Box className={styles.chartContainer}>
                        <Radar data={performanceData} options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'top' as const,
                            },
                          },
                          scales: {
                            r: {
                              beginAtZero: true,
                              max: 100,
                            },
                          },
                        }} />
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box className={styles.performanceStats}>
                      <Card className={styles.performanceCard}>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            Время отклика
                          </Typography>
                          <Typography variant="h4" color="success.main">
                            {analyticsData?.performanceMetrics.averageResponseTime || 0}ms
                          </Typography>
                        </CardContent>
                      </Card>
                      <Card className={styles.performanceCard}>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            Доступность
                          </Typography>
                          <Typography variant="h4" color="primary">
                            {analyticsData?.performanceMetrics.uptime || 0}%
                          </Typography>
                        </CardContent>
                      </Card>
                      <Card className={styles.performanceCard}>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            Ошибки
                          </Typography>
                          <Typography variant="h4" color="error">
                            {analyticsData?.performanceMetrics.errorRate || 0}%
                          </Typography>
                        </CardContent>
                      </Card>
                    </Box>
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        </Container>
      </AdminLayout>
    </>
  );
};

export default AnalyticsPage;
