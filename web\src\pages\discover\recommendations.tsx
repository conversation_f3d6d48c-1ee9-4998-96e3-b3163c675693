import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Close,
  Star,
  Message,
  LocationOn,
  Work,
  School,
  Refresh,
  TuneOutlined,
  Send,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { discoverService } from '../../services/discoverService';
import { UserProfile } from '../../types/discover.types';
import { calculateAge } from '../../utils/dateUtils';
import styles from './recommendations.module.css';

interface RecommendationsProps {}

const Recommendations: React.FC<RecommendationsProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [currentIndex, setCurrentIndex] = useState(0);
  const [messageDialogOpen, setMessageDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [message, setMessage] = useState('');

  // Загрузка рекомендаций
  const {
    data: recommendations,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['discover', 'recommendations'],
    queryFn: () => discoverService.getRecommendations(),
    enabled: isAuthenticated,
  });

  // Мутация для лайка
  const likeMutation = useMutation({
    mutationFn: (userId: string) => discoverService.likeUser(userId),
    onSuccess: (data) => {
      if (data.isMatch) {
        // Показать уведомление о матче
        alert('Это матч! 🎉');
      }
      handleNext();
    },
  });

  // Мутация для суперлайка
  const superLikeMutation = useMutation({
    mutationFn: (userId: string) => discoverService.superLikeUser(userId),
    onSuccess: (data) => {
      if (data.isMatch) {
        alert('Это матч! 🎉');
      }
      handleNext();
    },
  });

  // Мутация для пропуска
  const passMutation = useMutation({
    mutationFn: (userId: string) => discoverService.passUser(userId),
    onSuccess: () => {
      handleNext();
    },
  });

  // Мутация для отправки сообщения
  const sendMessageMutation = useMutation({
    mutationFn: ({ userId, message }: { userId: string; message: string }) =>
      discoverService.sendMessage(userId, message),
    onSuccess: () => {
      setMessageDialogOpen(false);
      setMessage('');
      setSelectedUser(null);
      router.push('/chat');
    },
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/discover/recommendations');
    }
  }, [isAuthenticated, router]);

  const currentUser = recommendations?.[currentIndex];

  const handleLike = () => {
    if (currentUser) {
      likeMutation.mutate(currentUser.id);
    }
  };

  const handleSuperLike = () => {
    if (currentUser) {
      superLikeMutation.mutate(currentUser.id);
    }
  };

  const handlePass = () => {
    if (currentUser) {
      passMutation.mutate(currentUser.id);
    }
  };

  const handleNext = () => {
    if (recommendations && currentIndex < recommendations.length - 1) {
      setCurrentIndex(prev => prev + 1);
    } else {
      // Загрузить новые рекомендации
      refetch();
      setCurrentIndex(0);
    }
  };

  const handleSendMessage = (userProfile: UserProfile) => {
    setSelectedUser(userProfile);
    setMessageDialogOpen(true);
  };

  const handleConfirmSendMessage = () => {
    if (selectedUser && message.trim()) {
      sendMessageMutation.mutate({
        userId: selectedUser.id,
        message: message.trim(),
      });
    }
  };

  const handleKeyPress = (event: KeyboardEvent) => {
    if (!currentUser) return;

    switch (event.key) {
      case 'ArrowLeft':
        handlePass();
        break;
      case 'ArrowRight':
        handleLike();
        break;
      case 'ArrowUp':
        handleSuperLike();
        break;
      case ' ':
        event.preventDefault();
        handleNext();
        break;
    }
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentUser]);

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Рекомендации - Likes & Love</title>
        <meta name="description" content="Персональные рекомендации партнеров на основе ваших предпочтений и интересов." />
        <meta name="keywords" content="рекомендации, знакомства, матчи, партнер" />
        <meta property="og:title" content="Рекомендации - Likes & Love" />
        <meta property="og:description" content="Персональные рекомендации партнеров" />
        <meta property="og:type" content="website" />
      </Head>

      <Container maxWidth="md" className={styles.container}>
        {/* Заголовок */}
        <Box className={styles.header}>
          <Typography variant="h3" component="h1" className={styles.title}>
            Рекомендации для вас
          </Typography>
          <Typography variant="h6" color="text.secondary" className={styles.subtitle}>
            Персональные рекомендации на основе ваших предпочтений
          </Typography>
        </Box>

        {/* Подсказки управления */}
        <Alert severity="info" className={styles.controlsHint}>
          <Typography variant="body2">
            <strong>Управление:</strong> ← Пропустить | → Лайк | ↑ Суперлайк | Пробел - Следующий
          </Typography>
        </Alert>

        {isLoading ? (
          <Box className={styles.loadingContainer}>
            <LinearProgress />
            <Typography>Подбираем для вас идеальные рекомендации...</Typography>
          </Box>
        ) : error ? (
          <Alert severity="error" className={styles.errorAlert}>
            <Typography variant="h6" gutterBottom>
              Ошибка загрузки рекомендаций
            </Typography>
            <Typography variant="body2" gutterBottom>
              Попробуйте обновить страницу или проверьте подключение к интернету.
            </Typography>
            <Button
              variant="contained"
              onClick={() => refetch()}
              startIcon={<Refresh />}
              sx={{ mt: 2 }}
            >
              Попробовать снова
            </Button>
          </Alert>
        ) : !recommendations || recommendations.length === 0 ? (
          <Alert severity="info" className={styles.emptyAlert}>
            <Typography variant="h6" gutterBottom>
              Рекомендации закончились
            </Typography>
            <Typography variant="body2" gutterBottom>
              Мы показали вам всех подходящих пользователей. Попробуйте расширить критерии поиска.
            </Typography>
            <Button
              variant="contained"
              onClick={() => router.push('/discover/search')}
              sx={{ mt: 2, mr: 1 }}
            >
              Расширенный поиск
            </Button>
            <Button
              variant="outlined"
              onClick={() => refetch()}
              startIcon={<Refresh />}
              sx={{ mt: 2 }}
            >
              Обновить
            </Button>
          </Alert>
        ) : currentUser ? (
          <Box className={styles.cardContainer}>
            {/* Индикатор прогресса */}
            <Box className={styles.progressContainer}>
              <Typography variant="body2" color="text.secondary">
                {currentIndex + 1} из {recommendations.length}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={((currentIndex + 1) / recommendations.length) * 100}
                className={styles.progressBar}
              />
            </Box>

            {/* Карточка пользователя */}
            <Card className={styles.userCard}>
              <Box className={styles.imageContainer}>
                <CardMedia
                  component="img"
                  image={currentUser.photos[0]?.url || '/default-avatar.png'}
                  alt={`${currentUser.firstName} ${currentUser.lastName}`}
                  className={styles.userImage}
                />
                
                {currentUser.isOnline && (
                  <Chip
                    label="Онлайн"
                    color="success"
                    size="small"
                    className={styles.onlineBadge}
                  />
                )}
                
                {currentUser.isPremium && (
                  <Chip
                    icon={<Star />}
                    label="Premium"
                    color="warning"
                    size="small"
                    className={styles.premiumBadge}
                  />
                )}

                {/* Кнопки действий на изображении */}
                <Box className={styles.imageActions}>
                  <Tooltip title="Суперлайк">
                    <IconButton
                      className={styles.superLikeButton}
                      onClick={handleSuperLike}
                      disabled={superLikeMutation.isPending}
                    >
                      <Star />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>

              <CardContent className={styles.userContent}>
                <Box className={styles.userHeader}>
                  <Typography variant="h4" className={styles.userName}>
                    {currentUser.firstName} {currentUser.lastName}
                  </Typography>
                  <Typography variant="h6" color="text.secondary" className={styles.userAge}>
                    {calculateAge(currentUser.birthDate)} лет
                  </Typography>
                </Box>

                <Box className={styles.userLocation}>
                  <LocationOn sx={{ fontSize: 20, mr: 0.5, color: 'text.secondary' }} />
                  <Typography variant="body1" color="text.secondary">
                    {currentUser.location}
                  </Typography>
                </Box>

                {currentUser.occupation && (
                  <Box className={styles.userOccupation}>
                    <Work sx={{ fontSize: 20, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body1" color="text.secondary">
                      {currentUser.occupation}
                    </Typography>
                  </Box>
                )}

                {currentUser.education && (
                  <Box className={styles.userEducation}>
                    <School sx={{ fontSize: 20, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body1" color="text.secondary">
                      {currentUser.education}
                    </Typography>
                  </Box>
                )}

                {currentUser.bio && (
                  <Typography variant="body1" className={styles.userBio}>
                    {currentUser.bio}
                  </Typography>
                )}

                {currentUser.interests && currentUser.interests.length > 0 && (
                  <Box className={styles.userInterests}>
                    <Typography variant="subtitle2" gutterBottom>
                      Интересы:
                    </Typography>
                    <Box className={styles.interestsContainer}>
                      {currentUser.interests.map((interest, index) => (
                        <Chip
                          key={index}
                          label={interest}
                          size="small"
                          className={styles.interestChip}
                        />
                      ))}
                    </Box>
                  </Box>
                )}

                {/* Кнопки действий */}
                <Box className={styles.actionButtons}>
                  <Button
                    variant="outlined"
                    size="large"
                    onClick={handlePass}
                    disabled={passMutation.isPending}
                    className={styles.passButton}
                    startIcon={<Close />}
                  >
                    Пропустить
                  </Button>

                  <Button
                    variant="outlined"
                    size="large"
                    onClick={() => handleSendMessage(currentUser)}
                    className={styles.messageButton}
                    startIcon={<Message />}
                  >
                    Написать
                  </Button>

                  <Button
                    variant="contained"
                    size="large"
                    onClick={handleLike}
                    disabled={likeMutation.isPending}
                    className={styles.likeButton}
                    startIcon={currentUser.isLiked ? <Favorite /> : <FavoriteBorder />}
                  >
                    {currentUser.isLiked ? 'Нравится' : 'Лайк'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Box>
        ) : null}

        {/* Плавающие кнопки */}
        <Box className={styles.floatingActions}>
          <Fab
            color="default"
            onClick={() => router.push('/discover/search')}
            className={styles.searchFab}
          >
            <TuneOutlined />
          </Fab>
          
          <Fab
            color="primary"
            onClick={() => refetch()}
            className={styles.refreshFab}
          >
            <Refresh />
          </Fab>
        </Box>

        {/* Диалог отправки сообщения */}
        <Dialog
          open={messageDialogOpen}
          onClose={() => setMessageDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Отправить сообщение
            {selectedUser && (
              <Typography variant="body2" color="text.secondary">
                {selectedUser.firstName} {selectedUser.lastName}
              </Typography>
            )}
          </DialogTitle>
          
          <DialogContent>
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Ваше сообщение"
              placeholder="Напишите что-нибудь интересное..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              sx={{ mt: 1 }}
            />
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setMessageDialogOpen(false)}>
              Отмена
            </Button>
            <Button
              variant="contained"
              onClick={handleConfirmSendMessage}
              disabled={!message.trim() || sendMessageMutation.isPending}
              startIcon={<Send />}
            >
              {sendMessageMutation.isPending ? 'Отправка...' : 'Отправить'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </>
  );
};

export default Recommendations;
