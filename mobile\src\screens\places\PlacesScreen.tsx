import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { placesService } from '../../services/placesService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { Place, PlaceCategory, PlaceFilters } from '../../types/places.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface PlacesScreenProps {}

const PlacesScreen: React.FC<PlacesScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location, requestLocation } = useLocation();
  const queryClient = useQueryClient();

  const [viewMode, setViewMode] = useState<'list' | 'map'>('list');
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPlace, setSelectedPlace] = useState<Place | null>(null);
  const [filters, setFilters] = useState<PlaceFilters>({
    category: undefined,
    rating: undefined,
    distance: 10,
    priceRange: undefined,
    features: [],
  });

  // Загрузка мест
  const {
    data: places,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['places', selectedCategory, searchQuery, filters, location],
    queryFn: () => placesService.getPlaces(selectedCategory, searchQuery, filters, location),
    enabled: !!location,
  });

  // Загрузка категорий
  const {
    data: categories,
  } = useQuery({
    queryKey: ['places', 'categories'],
    queryFn: () => placesService.getCategories(),
  });

  // Мутация для добавления в избранное
  const toggleFavoriteMutation = useMutation({
    mutationFn: (placeId: string) => placesService.toggleFavorite(placeId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['places'] });
      queryClient.invalidateQueries({ queryKey: ['favorites'] });
    },
  });

  // Запрос геолокации при загрузке
  useEffect(() => {
    if (!location) {
      requestLocation();
    }
  }, [location, requestLocation]);

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      if (location) {
        refetch();
      }
    }, [location, refetch])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleToggleFavorite = (placeId: string) => {
    toggleFavoriteMutation.mutate(placeId);
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}м`;
    }
    return `${distance.toFixed(1)}км`;
  };

  const formatPrice = (priceRange: number) => {
    return '₽'.repeat(priceRange);
  };

  const renderCategoryButton = (category: PlaceCategory) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryButton,
        selectedCategory === category.id && styles.categoryButtonActive,
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <Ionicons
        name={category.icon as any}
        size={20}
        color={selectedCategory === category.id ? colors.white : colors.text}
      />
      <Text
        style={[
          styles.categoryButtonText,
          selectedCategory === category.id && styles.categoryButtonTextActive,
        ]}
      >
        {category.name}
      </Text>
    </TouchableOpacity>
  );

  const renderPlaceCard = ({ item, index }: { item: Place; index: number }) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateY: interpolate(animatedValue.value, [0, 1], [30, 0]),
          },
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.95, 1]),
          },
        ],
      };
    });

    return (
      <Animated.View style={[styles.placeCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('PlaceDetail', { placeId: item.id })}
          activeOpacity={0.8}
        >
          <Image source={{ uri: item.photos[0] }} style={styles.placeImage} />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.placeImageGradient}
          />
          
          {/* Кнопка избранного */}
          <TouchableOpacity
            style={styles.favoriteButton}
            onPress={() => handleToggleFavorite(item.id)}
            disabled={toggleFavoriteMutation.isPending}
          >
            <Ionicons
              name={item.isFavorite ? 'heart' : 'heart-outline'}
              size={24}
              color={item.isFavorite ? colors.error : colors.white}
            />
          </TouchableOpacity>
          
          {/* Рейтинг */}
          <View style={styles.ratingBadge}>
            <Ionicons name="star" size={14} color={colors.warning} />
            <Text style={styles.ratingText}>{item.rating}</Text>
          </View>
          
          <View style={styles.placeInfo}>
            <Text style={styles.placeName}>{item.name}</Text>
            <Text style={styles.placeCategory}>{item.category}</Text>
            
            <View style={styles.placeDetails}>
              <Text style={styles.placeDistance}>
                <Ionicons name="location-outline" size={14} color={colors.textSecondary} />
                {' '}{formatDistance(item.distance)}
              </Text>
              
              {item.priceRange && (
                <Text style={styles.priceRange}>
                  {formatPrice(item.priceRange)}
                </Text>
              )}
            </View>
            
            {item.features.length > 0 && (
              <View style={styles.featuresContainer}>
                {item.features.slice(0, 3).map((feature, idx) => (
                  <View key={idx} style={styles.featureTag}>
                    <Text style={styles.featureText}>{feature}</Text>
                  </View>
                ))}
                {item.features.length > 3 && (
                  <Text style={styles.moreFeatures}>+{item.features.length - 3}</Text>
                )}
              </View>
            )}
          </View>
        </TouchableOpacity>
        
        <View style={styles.placeActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('PlaceDetail', { placeId: item.id })}
          >
            <Ionicons name="information-circle-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Подробнее</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('ARNavigation', { placeId: item.id })}
          >
            <Ionicons name="navigate-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Маршрут</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('CreateMeeting', { placeId: item.id })}
          >
            <Ionicons name="calendar-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Встреча</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  const renderMapMarker = (place: Place) => (
    <Marker
      key={place.id}
      coordinate={{
        latitude: place.coordinates.latitude,
        longitude: place.coordinates.longitude,
      }}
      onPress={() => setSelectedPlace(place)}
    >
      <View style={styles.markerContainer}>
        <View style={[styles.marker, { backgroundColor: getCategoryColor(place.category) }]}>
          <Ionicons
            name={getCategoryIcon(place.category) as any}
            size={16}
            color={colors.white}
          />
        </View>
      </View>
    </Marker>
  );

  const getCategoryColor = (category: string) => {
    const colors_map = {
      restaurant: colors.error,
      cafe: colors.warning,
      bar: colors.primary,
      shopping: colors.success,
      entertainment: colors.secondary,
      default: colors.primary,
    };
    return colors_map[category] || colors_map.default;
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      restaurant: 'restaurant-outline',
      cafe: 'cafe-outline',
      bar: 'wine-outline',
      shopping: 'bag-outline',
      entertainment: 'musical-notes-outline',
      default: 'location-outline',
    };
    return icons[category] || icons.default;
  };

  const renderSelectedPlaceInfo = () => {
    if (!selectedPlace) return null;

    return (
      <View style={styles.selectedPlaceCard}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => setSelectedPlace(null)}
        >
          <Ionicons name="close" size={20} color={colors.text} />
        </TouchableOpacity>
        
        <Image source={{ uri: selectedPlace.photos[0] }} style={styles.selectedPlaceImage} />
        
        <View style={styles.selectedPlaceInfo}>
          <Text style={styles.selectedPlaceName}>{selectedPlace.name}</Text>
          <Text style={styles.selectedPlaceCategory}>{selectedPlace.category}</Text>
          
          <View style={styles.selectedPlaceDetails}>
            <View style={styles.selectedPlaceRating}>
              <Ionicons name="star" size={14} color={colors.warning} />
              <Text style={styles.selectedRatingText}>{selectedPlace.rating}</Text>
            </View>
            <Text style={styles.selectedPlaceDistance}>
              {formatDistance(selectedPlace.distance)}
            </Text>
          </View>
          
          <View style={styles.selectedPlaceActions}>
            <TouchableOpacity
              style={styles.selectedActionButton}
              onPress={() => navigation.navigate('PlaceDetail', { placeId: selectedPlace.id })}
            >
              <Ionicons name="information-circle" size={18} color={colors.primary} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.selectedActionButton}
              onPress={() => handleToggleFavorite(selectedPlace.id)}
            >
              <Ionicons
                name={selectedPlace.isFavorite ? 'heart' : 'heart-outline'}
                size={18}
                color={selectedPlace.isFavorite ? colors.error : colors.primary}
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.selectedActionButton}
              onPress={() => navigation.navigate('ARNavigation', { placeId: selectedPlace.id })}
            >
              <Ionicons name="navigate" size={18} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  if (isLoading && !places) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Ищем интересные места...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="location-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка геолокации</Text>
          <Text style={styles.errorText}>
            Не удалось определить ваше местоположение
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={requestLocation}>
            <Text style={styles.retryButtonText}>Разрешить доступ</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Места</Text>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('PlaceFilters')}
          >
            <Ionicons name="options-outline" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <View style={styles.viewModeToggle}>
            <TouchableOpacity
              style={[styles.toggleButton, viewMode === 'list' && styles.toggleButtonActive]}
              onPress={() => setViewMode('list')}
            >
              <Ionicons name="list" size={20} color={viewMode === 'list' ? colors.white : colors.text} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.toggleButton, viewMode === 'map' && styles.toggleButtonActive]}
              onPress={() => setViewMode('map')}
            >
              <Ionicons name="map" size={20} color={viewMode === 'map' ? colors.white : colors.text} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Поиск */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search-outline" size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск мест..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Категории */}
      {categories && (
        <View style={styles.categoriesContainer}>
          <FlatList
            data={[{ id: 'all', name: 'Все', icon: 'grid-outline' }, ...categories]}
            renderItem={({ item }) => renderCategoryButton(item)}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>
      )}

      {/* Контент */}
      {viewMode === 'list' ? (
        <FlatList
          data={places}
          renderItem={renderPlaceCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="location-outline" size={64} color={colors.textSecondary} />
              <Text style={styles.emptyTitle}>Места не найдены</Text>
              <Text style={styles.emptyText}>
                Попробуйте изменить фильтры или поисковый запрос
              </Text>
            </View>
          }
        />
      ) : (
        <View style={styles.mapContainer}>
          {location && (
            <MapView
              provider={PROVIDER_GOOGLE}
              style={styles.map}
              region={{
                latitude: location.latitude,
                longitude: location.longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              }}
              showsUserLocation
              showsMyLocationButton
            >
              {places?.map(renderMapMarker)}
            </MapView>
          )}
          
          {renderSelectedPlaceInfo()}
        </View>
      )}

      {/* Счетчик мест */}
      {places && places.length > 0 && (
        <View style={styles.counterContainer}>
          <Text style={styles.counterText}>
            Найдено {places.length} {places.length === 1 ? 'место' : 'мест'}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: 20,
    padding: 2,
  },
  toggleButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: colors.primary,
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  categoriesContainer: {
    paddingVertical: spacing.sm,
  },
  categoriesList: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    gap: spacing.xs,
  },
  categoryButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  categoryButtonTextActive: {
    color: colors.white,
  },
  listContainer: {
    padding: spacing.md,
  },
  placeCard: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    marginBottom: spacing.md,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  placeImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  placeImageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  favoriteButton: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  ratingBadge: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  placeInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.md,
  },
  placeName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  placeCategory: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  placeDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  placeDistance: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  priceRange: {
    fontSize: typography.sizes.sm,
    color: colors.success,
    fontWeight: typography.weights.bold,
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  featureTag: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  featureText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  moreFeatures: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium,
  },
  placeActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.surface,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    backgroundColor: colors.background,
    gap: spacing.xs,
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.primary,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  markerContainer: {
    alignItems: 'center',
  },
  marker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.white,
  },
  selectedPlaceCard: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.md,
    right: spacing.md,
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  closeButton: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  selectedPlaceImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
    marginBottom: spacing.sm,
  },
  selectedPlaceInfo: {
    flex: 1,
  },
  selectedPlaceName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  selectedPlaceCategory: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  selectedPlaceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  selectedPlaceRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  selectedRatingText: {
    fontSize: typography.sizes.sm,
    color: colors.text,
  },
  selectedPlaceDistance: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  selectedPlaceActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  selectedActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  counterContainer: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.lg,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  },
  counterText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default PlacesScreen;
