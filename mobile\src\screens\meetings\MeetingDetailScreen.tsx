import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  Dimensions,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { meetingsService } from '../../services/meetingsService';
import { useAuth } from '../../hooks/useAuth';
import { Meeting, MeetingParticipant } from '../../types/meetings.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MeetingDetailScreenProps {}

const MeetingDetailScreen: React.FC<MeetingDetailScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const { meetingId } = route.params as { meetingId: string };
  const [showFullDescription, setShowFullDescription] = useState(false);

  // Загрузка деталей встречи
  const {
    data: meeting,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['meeting', meetingId],
    queryFn: () => meetingsService.getMeetingDetails(meetingId),
  });

  // Мутация для присоединения к встрече
  const joinMeetingMutation = useMutation({
    mutationFn: () => meetingsService.joinMeeting(meetingId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meeting', meetingId] });
      queryClient.invalidateQueries({ queryKey: ['meetings'] });
    },
  });

  // Мутация для выхода из встречи
  const leaveMeetingMutation = useMutation({
    mutationFn: () => meetingsService.leaveMeeting(meetingId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meeting', meetingId] });
      queryClient.invalidateQueries({ queryKey: ['meetings'] });
    },
  });

  // Мутация для отмены встречи
  const cancelMeetingMutation = useMutation({
    mutationFn: () => meetingsService.cancelMeeting(meetingId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['meeting', meetingId] });
      queryClient.invalidateQueries({ queryKey: ['meetings'] });
      navigation.goBack();
    },
  });

  const handleJoinMeeting = () => {
    Alert.alert(
      'Присоединиться к встрече',
      'Вы уверены, что хотите присоединиться к этой встрече?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Присоединиться',
          onPress: () => joinMeetingMutation.mutate(),
        },
      ]
    );
  };

  const handleLeaveMeeting = () => {
    Alert.alert(
      'Покинуть встречу',
      'Вы уверены, что хотите покинуть эту встречу?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Покинуть',
          style: 'destructive',
          onPress: () => leaveMeetingMutation.mutate(),
        },
      ]
    );
  };

  const handleCancelMeeting = () => {
    Alert.alert(
      'Отменить встречу',
      'Вы уверены, что хотите отменить эту встречу? Это действие нельзя отменить.',
      [
        { text: 'Нет', style: 'cancel' },
        {
          text: 'Отменить встречу',
          style: 'destructive',
          onPress: () => cancelMeetingMutation.mutate(),
        },
      ]
    );
  };

  const handleShareMeeting = async () => {
    if (!meeting) return;

    try {
      await Share.share({
        message: `Присоединяйтесь к встрече "${meeting.title}"!\n\nДата: ${formatDate(meeting.dateTime)}\nВремя: ${formatTime(meeting.dateTime)}\nМесто: ${meeting.location.name}\n\nПодробности в приложении Likes & Love`,
        title: meeting.title,
      });
    } catch (error) {
      console.error('Error sharing meeting:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return colors.success;
      case 'pending':
        return colors.warning;
      case 'cancelled':
        return colors.error;
      case 'completed':
        return colors.primary;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Подтверждена';
      case 'pending':
        return 'Ожидает подтверждения';
      case 'cancelled':
        return 'Отменена';
      case 'completed':
        return 'Завершена';
      default:
        return 'Неизвестно';
    }
  };

  const getMeetingTypeIcon = (type: string) => {
    switch (type) {
      case 'casual':
        return 'cafe-outline';
      case 'dinner':
        return 'restaurant-outline';
      case 'activity':
        return 'fitness-outline';
      case 'cultural':
        return 'library-outline';
      case 'party':
        return 'musical-notes-outline';
      default:
        return 'calendar-outline';
    }
  };

  const getMeetingTypeLabel = (type: string) => {
    switch (type) {
      case 'casual':
        return 'Неформальная встреча';
      case 'dinner':
        return 'Ужин';
      case 'activity':
        return 'Активность';
      case 'cultural':
        return 'Культурное мероприятие';
      case 'party':
        return 'Вечеринка';
      default:
        return 'Встреча';
    }
  };

  const isParticipant = meeting?.participants.some(p => p.id === user?.id);
  const isOrganizer = meeting?.organizerId === user?.id;
  const canJoin = meeting && !isParticipant && meeting.participants.length < meeting.maxParticipants;
  const isUpcoming = meeting && new Date(meeting.dateTime) > new Date();

  const renderParticipant = (participant: MeetingParticipant, index: number) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.8, 1]),
          },
        ],
      };
    });

    return (
      <Animated.View key={participant.id} style={[styles.participantCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('UserProfile', { userId: participant.id })}
          activeOpacity={0.8}
        >
          <Image source={{ uri: participant.avatar }} style={styles.participantAvatar} />
          <Text style={styles.participantName}>{participant.name}</Text>
          {participant.id === meeting?.organizerId && (
            <View style={styles.organizerBadge}>
              <Text style={styles.organizerBadgeText}>Организатор</Text>
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Загружаем детали встречи...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !meeting) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить детали встречи
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Детали встречи</Text>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleShareMeeting}
          >
            <Ionicons name="share-outline" size={24} color={colors.text} />
          </TouchableOpacity>
          
          {isOrganizer && (
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => navigation.navigate('EditMeeting', { meetingId })}
            >
              <Ionicons name="create-outline" size={24} color={colors.text} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Основная информация */}
        <View style={styles.mainInfo}>
          <View style={styles.titleSection}>
            <Text style={styles.meetingTitle}>{meeting.title}</Text>
            <View style={styles.statusContainer}>
              <View
                style={[
                  styles.statusIndicator,
                  { backgroundColor: getStatusColor(meeting.status) },
                ]}
              />
              <Text style={[styles.statusText, { color: getStatusColor(meeting.status) }]}>
                {getStatusText(meeting.status)}
              </Text>
            </View>
          </View>

          <View style={styles.typeContainer}>
            <Ionicons
              name={getMeetingTypeIcon(meeting.type) as any}
              size={20}
              color={colors.primary}
            />
            <Text style={styles.typeText}>{getMeetingTypeLabel(meeting.type)}</Text>
          </View>
        </View>

        {/* Дата и время */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Ionicons name="calendar-outline" size={24} color={colors.primary} />
            <Text style={styles.sectionTitle}>Дата и время</Text>
          </View>
          <Text style={styles.dateText}>{formatDate(meeting.dateTime)}</Text>
          <Text style={styles.timeText}>{formatTime(meeting.dateTime)}</Text>
        </View>

        {/* Место */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Ionicons name="location-outline" size={24} color={colors.primary} />
            <Text style={styles.sectionTitle}>Место встречи</Text>
          </View>
          <Text style={styles.locationName}>{meeting.location.name}</Text>
          <Text style={styles.locationAddress}>{meeting.location.address}</Text>
          
          {meeting.location.coordinates && (
            <View style={styles.mapContainer}>
              <MapView
                provider={PROVIDER_GOOGLE}
                style={styles.map}
                region={{
                  latitude: meeting.location.coordinates.latitude,
                  longitude: meeting.location.coordinates.longitude,
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.01,
                }}
                scrollEnabled={false}
                zoomEnabled={false}
              >
                <Marker
                  coordinate={{
                    latitude: meeting.location.coordinates.latitude,
                    longitude: meeting.location.coordinates.longitude,
                  }}
                  title={meeting.location.name}
                />
              </MapView>
              <TouchableOpacity
                style={styles.mapOverlay}
                onPress={() => navigation.navigate('MeetingMap', { meetingId })}
              >
                <Text style={styles.mapOverlayText}>Открыть карту</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Описание */}
        {meeting.description && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Ionicons name="document-text-outline" size={24} color={colors.primary} />
              <Text style={styles.sectionTitle}>Описание</Text>
            </View>
            <Text
              style={styles.descriptionText}
              numberOfLines={showFullDescription ? undefined : 3}
            >
              {meeting.description}
            </Text>
            {meeting.description.length > 150 && (
              <TouchableOpacity
                style={styles.showMoreButton}
                onPress={() => setShowFullDescription(!showFullDescription)}
              >
                <Text style={styles.showMoreText}>
                  {showFullDescription ? 'Скрыть' : 'Показать полностью'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Участники */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Ionicons name="people-outline" size={24} color={colors.primary} />
            <Text style={styles.sectionTitle}>
              Участники ({meeting.participants.length}/{meeting.maxParticipants})
            </Text>
          </View>
          
          <View style={styles.participantsGrid}>
            {meeting.participants.map(renderParticipant)}
            
            {canJoin && (
              <View style={styles.joinSlot}>
                <TouchableOpacity
                  style={styles.joinButton}
                  onPress={handleJoinMeeting}
                  disabled={joinMeetingMutation.isPending}
                >
                  <Ionicons name="add" size={32} color={colors.primary} />
                  <Text style={styles.joinButtonText}>Присоединиться</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Нижние действия */}
      {isUpcoming && (
        <View style={styles.bottomActions}>
          {isParticipant && !isOrganizer && (
            <TouchableOpacity
              style={[styles.actionButton, styles.leaveButton]}
              onPress={handleLeaveMeeting}
              disabled={leaveMeetingMutation.isPending}
            >
              <Ionicons name="exit-outline" size={20} color={colors.error} />
              <Text style={[styles.actionButtonText, { color: colors.error }]}>
                Покинуть
              </Text>
            </TouchableOpacity>
          )}
          
          {isOrganizer && (
            <TouchableOpacity
              style={[styles.actionButton, styles.cancelButton]}
              onPress={handleCancelMeeting}
              disabled={cancelMeetingMutation.isPending}
            >
              <Ionicons name="trash-outline" size={20} color={colors.error} />
              <Text style={[styles.actionButtonText, { color: colors.error }]}>
                Отменить встречу
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, styles.chatButton]}
            onPress={() => navigation.navigate('MeetingChat', { meetingId })}
          >
            <Ionicons name="chatbubble-outline" size={20} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>
              Чат встречи
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Индикатор загрузки */}
      {(joinMeetingMutation.isPending || leaveMeetingMutation.isPending || cancelMeetingMutation.isPending) && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  mainInfo: {
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  titleSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  meetingTitle: {
    flex: 1,
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginRight: spacing.md,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  statusText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  typeText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
  },
  section: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    gap: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  dateText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  timeText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
  },
  locationName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  locationAddress: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  mapContainer: {
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  mapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapOverlayText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  descriptionText: {
    fontSize: typography.sizes.md,
    color: colors.text,
    lineHeight: 22,
  },
  showMoreButton: {
    marginTop: spacing.sm,
  },
  showMoreText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium,
  },
  participantsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  participantCard: {
    alignItems: 'center',
    width: (screenWidth - spacing.lg * 2 - spacing.md * 2) / 3,
  },
  participantAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: spacing.sm,
  },
  participantName: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  organizerBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  },
  organizerBadgeText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  joinSlot: {
    alignItems: 'center',
    width: (screenWidth - spacing.lg * 2 - spacing.md * 2) / 3,
  },
  joinButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.surface,
    borderWidth: 2,
    borderColor: colors.primary,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  joinButtonText: {
    fontSize: typography.sizes.xs,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    textAlign: 'center',
  },
  bottomActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 12,
    backgroundColor: colors.background,
    gap: spacing.xs,
  },
  leaveButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  cancelButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  chatButton: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MeetingDetailScreen;
