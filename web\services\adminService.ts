import axios from 'axios';
import {
  DashboardStats,
  SystemHealth,
  RecentActivity,
  AdminUser,
  UserFilters,
  UserListResponse,
  Report,
  ReportFilters,
  ReportListResponse,
  ModerationItem,
  ModerationFilters,
  ModerationListResponse,
  AnalyticsData,
  SystemSettings,
  ApiResponse,
  PaginationParams,
} from '../types/admin.types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

class AdminService {
  private api = axios.create({
    baseURL: `${API_BASE_URL}/admin`,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  constructor() {
    // Добавляем токен авторизации к каждому запросу
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Обработка ошибок
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Перенаправляем на страницу входа при ошибке авторизации
          window.location.href = '/auth/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Дашборд
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await this.api.get<ApiResponse<DashboardStats>>('/dashboard/stats');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки статистики');
    }
    return response.data.data!;
  }

  async getSystemHealth(): Promise<SystemHealth> {
    const response = await this.api.get<ApiResponse<SystemHealth>>('/system/health');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки состояния системы');
    }
    return response.data.data!;
  }

  async getRecentActivity(): Promise<RecentActivity[]> {
    const response = await this.api.get<ApiResponse<RecentActivity[]>>('/activity/recent');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки активности');
    }
    return response.data.data!;
  }

  // Управление пользователями
  async getUsers(
    filters: UserFilters = {},
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<UserListResponse> {
    const params = new URLSearchParams();
    
    // Добавляем фильтры
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    // Добавляем пагинацию
    params.append('page', pagination.page.toString());
    params.append('limit', pagination.limit.toString());

    const response = await this.api.get<ApiResponse<UserListResponse>>(`/users?${params}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки пользователей');
    }
    return response.data.data!;
  }

  async getUser(userId: string): Promise<AdminUser> {
    const response = await this.api.get<ApiResponse<AdminUser>>(`/users/${userId}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки пользователя');
    }
    return response.data.data!;
  }

  async blockUser(userId: string, reason: string): Promise<void> {
    const response = await this.api.post<ApiResponse<void>>(`/users/${userId}/block`, { reason });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка блокировки пользователя');
    }
  }

  async unblockUser(userId: string): Promise<void> {
    const response = await this.api.post<ApiResponse<void>>(`/users/${userId}/unblock`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка разблокировки пользователя');
    }
  }

  async verifyUser(userId: string): Promise<void> {
    const response = await this.api.post<ApiResponse<void>>(`/users/${userId}/verify`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка верификации пользователя');
    }
  }

  async deleteUser(userId: string): Promise<void> {
    const response = await this.api.delete<ApiResponse<void>>(`/users/${userId}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка удаления пользователя');
    }
  }

  // Отчеты и жалобы
  async getReports(
    filters: ReportFilters = {},
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ReportListResponse> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    params.append('page', pagination.page.toString());
    params.append('limit', pagination.limit.toString());

    const response = await this.api.get<ApiResponse<ReportListResponse>>(`/reports?${params}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки отчетов');
    }
    return response.data.data!;
  }

  async getReport(reportId: string): Promise<Report> {
    const response = await this.api.get<ApiResponse<Report>>(`/reports/${reportId}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки отчета');
    }
    return response.data.data!;
  }

  async updateReportStatus(
    reportId: string,
    status: Report['status'],
    resolution?: string,
    actionTaken?: Report['actionTaken']
  ): Promise<void> {
    const response = await this.api.patch<ApiResponse<void>>(`/reports/${reportId}`, {
      status,
      resolution,
      actionTaken,
    });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка обновления отчета');
    }
  }

  async assignReport(reportId: string, assignedTo: string): Promise<void> {
    const response = await this.api.patch<ApiResponse<void>>(`/reports/${reportId}/assign`, {
      assignedTo,
    });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка назначения отчета');
    }
  }

  // Модерация контента
  async getModerationItems(
    filters: ModerationFilters = {},
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ModerationListResponse> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    params.append('page', pagination.page.toString());
    params.append('limit', pagination.limit.toString());

    const response = await this.api.get<ApiResponse<ModerationListResponse>>(`/moderation?${params}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки элементов модерации');
    }
    return response.data.data!;
  }

  async approveModerationItem(itemId: string): Promise<void> {
    const response = await this.api.post<ApiResponse<void>>(`/moderation/${itemId}/approve`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка одобрения элемента');
    }
  }

  async rejectModerationItem(itemId: string, reason: string): Promise<void> {
    const response = await this.api.post<ApiResponse<void>>(`/moderation/${itemId}/reject`, {
      reason,
    });
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка отклонения элемента');
    }
  }

  // Аналитика
  async getAnalytics(dateRange?: { from: string; to: string }): Promise<AnalyticsData> {
    const params = new URLSearchParams();
    if (dateRange) {
      params.append('from', dateRange.from);
      params.append('to', dateRange.to);
    }

    const response = await this.api.get<ApiResponse<AnalyticsData>>(`/analytics?${params}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки аналитики');
    }
    return response.data.data!;
  }

  async exportAnalytics(
    type: 'users' | 'engagement' | 'revenue',
    format: 'csv' | 'xlsx',
    dateRange?: { from: string; to: string }
  ): Promise<Blob> {
    const params = new URLSearchParams();
    params.append('type', type);
    params.append('format', format);
    if (dateRange) {
      params.append('from', dateRange.from);
      params.append('to', dateRange.to);
    }

    const response = await this.api.get(`/analytics/export?${params}`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // Настройки системы
  async getSystemSettings(): Promise<SystemSettings> {
    const response = await this.api.get<ApiResponse<SystemSettings>>('/settings');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки настроек');
    }
    return response.data.data!;
  }

  async updateSystemSettings(settings: Partial<SystemSettings>): Promise<void> {
    const response = await this.api.patch<ApiResponse<void>>('/settings', settings);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка обновления настроек');
    }
  }

  // Системные операции
  async clearCache(): Promise<void> {
    const response = await this.api.post<ApiResponse<void>>('/system/clear-cache');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка очистки кеша');
    }
  }

  async restartServices(): Promise<void> {
    const response = await this.api.post<ApiResponse<void>>('/system/restart-services');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка перезапуска сервисов');
    }
  }

  async backupDatabase(): Promise<void> {
    const response = await this.api.post<ApiResponse<void>>('/system/backup-database');
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка создания резервной копии');
    }
  }

  async getSystemLogs(
    level: 'error' | 'warn' | 'info' | 'debug' = 'error',
    limit: number = 100
  ): Promise<string[]> {
    const response = await this.api.get<ApiResponse<string[]>>(`/system/logs?level=${level}&limit=${limit}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Ошибка загрузки логов');
    }
    return response.data.data!;
  }
}

export const adminService = new AdminService();
