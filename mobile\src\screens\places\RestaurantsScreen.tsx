import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { placesService } from '../../services/placesService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { Place, PlaceFilters } from '../../types/places.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface RestaurantsScreenProps {}

const RestaurantsScreen: React.FC<RestaurantsScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location, requestLocation } = useLocation();
  const queryClient = useQueryClient();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCuisine, setSelectedCuisine] = useState<string>('all');
  const [filters, setFilters] = useState<PlaceFilters>({
    category: 'restaurant',
    rating: undefined,
    distance: 10,
    priceRange: undefined,
    features: [],
  });

  // Загрузка ресторанов
  const {
    data: restaurants,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['places', 'restaurants', searchQuery, selectedCuisine, filters, location],
    queryFn: () => placesService.getRestaurants(searchQuery, selectedCuisine, filters, location),
    enabled: !!location,
  });

  // Загрузка типов кухни
  const {
    data: cuisines,
  } = useQuery({
    queryKey: ['cuisines'],
    queryFn: () => placesService.getCuisineTypes(),
  });

  // Мутация для добавления в избранное
  const toggleFavoriteMutation = useMutation({
    mutationFn: (placeId: string) => placesService.toggleFavorite(placeId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['places'] });
      queryClient.invalidateQueries({ queryKey: ['favorites'] });
    },
  });

  // Запрос геолокации при загрузке
  useEffect(() => {
    if (!location) {
      requestLocation();
    }
  }, [location, requestLocation]);

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      if (location) {
        refetch();
      }
    }, [location, refetch])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleToggleFavorite = (placeId: string) => {
    toggleFavoriteMutation.mutate(placeId);
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}м`;
    }
    return `${distance.toFixed(1)}км`;
  };

  const formatPrice = (priceRange: number) => {
    return '₽'.repeat(priceRange);
  };

  const isOpenNow = (hours: any) => {
    if (!hours) return false;
    
    const now = new Date();
    const today = now.getDay();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const todayHours = hours.schedule[today];
    if (!todayHours || todayHours.closed) return false;
    
    const openTime = parseInt(todayHours.open.split(':')[0]) * 60 + parseInt(todayHours.open.split(':')[1]);
    const closeTime = parseInt(todayHours.close.split(':')[0]) * 60 + parseInt(todayHours.close.split(':')[1]);
    
    return currentTime >= openTime && currentTime <= closeTime;
  };

  const renderCuisineButton = (cuisine: { id: string; name: string; icon: string }) => (
    <TouchableOpacity
      key={cuisine.id}
      style={[
        styles.cuisineButton,
        selectedCuisine === cuisine.id && styles.cuisineButtonActive,
      ]}
      onPress={() => setSelectedCuisine(cuisine.id)}
    >
      <Text style={styles.cuisineIcon}>{cuisine.icon}</Text>
      <Text
        style={[
          styles.cuisineButtonText,
          selectedCuisine === cuisine.id && styles.cuisineButtonTextActive,
        ]}
      >
        {cuisine.name}
      </Text>
    </TouchableOpacity>
  );

  const renderRestaurantCard = ({ item, index }: { item: Place; index: number }) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateY: interpolate(animatedValue.value, [0, 1], [30, 0]),
          },
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.95, 1]),
          },
        ],
      };
    });

    const isOpen = isOpenNow(item.hours);

    return (
      <Animated.View style={[styles.restaurantCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('PlaceDetail', { placeId: item.id })}
          activeOpacity={0.8}
        >
          <Image source={{ uri: item.photos[0] }} style={styles.restaurantImage} />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.restaurantImageGradient}
          />
          
          {/* Кнопка избранного */}
          <TouchableOpacity
            style={styles.favoriteButton}
            onPress={() => handleToggleFavorite(item.id)}
            disabled={toggleFavoriteMutation.isPending}
          >
            <Ionicons
              name={item.isFavorite ? 'heart' : 'heart-outline'}
              size={24}
              color={item.isFavorite ? colors.error : colors.white}
            />
          </TouchableOpacity>
          
          {/* Статус работы */}
          <View style={[styles.statusBadge, { backgroundColor: isOpen ? colors.success : colors.error }]}>
            <Text style={styles.statusText}>{isOpen ? 'Открыто' : 'Закрыто'}</Text>
          </View>
          
          {/* Рейтинг */}
          <View style={styles.ratingBadge}>
            <Ionicons name="star" size={14} color={colors.warning} />
            <Text style={styles.ratingText}>{item.rating}</Text>
          </View>
          
          {/* Тип кухни */}
          {item.cuisine && (
            <View style={styles.cuisineBadge}>
              <Text style={styles.cuisineBadgeText}>{item.cuisine}</Text>
            </View>
          )}
          
          <View style={styles.restaurantInfo}>
            <Text style={styles.restaurantName}>{item.name}</Text>
            <Text style={styles.restaurantAddress}>{item.address}</Text>
            
            <View style={styles.restaurantDetails}>
              <Text style={styles.restaurantDistance}>
                <Ionicons name="location-outline" size={14} color={colors.textSecondary} />
                {' '}{formatDistance(item.distance)}
              </Text>
              
              {item.priceRange && (
                <Text style={styles.priceRange}>
                  {formatPrice(item.priceRange)}
                </Text>
              )}
              
              {item.deliveryTime && (
                <Text style={styles.deliveryTime}>
                  <Ionicons name="time-outline" size={14} color={colors.textSecondary} />
                  {' '}{item.deliveryTime} мин
                </Text>
              )}
            </View>
            
            {item.features.length > 0 && (
              <View style={styles.featuresContainer}>
                {item.features.slice(0, 3).map((feature, idx) => (
                  <View key={idx} style={styles.featureTag}>
                    <Text style={styles.featureText}>{feature}</Text>
                  </View>
                ))}
                {item.features.length > 3 && (
                  <Text style={styles.moreFeatures}>+{item.features.length - 3}</Text>
                )}
              </View>
            )}
          </View>
        </TouchableOpacity>
        
        <View style={styles.restaurantActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('PlaceDetail', { placeId: item.id })}
          >
            <Ionicons name="information-circle-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Меню</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('BookTable', { placeId: item.id })}
          >
            <Ionicons name="calendar-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Бронь</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('CreateMeeting', { placeId: item.id })}
          >
            <Ionicons name="people-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Встреча</Text>
          </TouchableOpacity>
          
          {item.hasDelivery && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('OrderDelivery', { placeId: item.id })}
            >
              <Ionicons name="bicycle-outline" size={20} color={colors.success} />
              <Text style={[styles.actionButtonText, { color: colors.success }]}>Доставка</Text>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>
    );
  };

  if (isLoading && !restaurants) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Ищем лучшие рестораны...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="restaurant-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить список ресторанов
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Рестораны</Text>
        
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('PlaceFilters', { category: 'restaurant' })}
        >
          <Ionicons name="options-outline" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Поиск */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search-outline" size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск ресторанов..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Типы кухни */}
      {cuisines && (
        <View style={styles.cuisinesContainer}>
          <FlatList
            data={[{ id: 'all', name: 'Все', icon: '🍽️' }, ...cuisines]}
            renderItem={({ item }) => renderCuisineButton(item)}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.cuisinesList}
          />
        </View>
      )}

      {/* Список ресторанов */}
      <FlatList
        data={restaurants}
        renderItem={renderRestaurantCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="restaurant-outline" size={64} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>Рестораны не найдены</Text>
            <Text style={styles.emptyText}>
              Попробуйте изменить фильтры или поисковый запрос
            </Text>
          </View>
        }
        ListHeaderComponent={
          restaurants && restaurants.length > 0 ? (
            <View style={styles.listHeader}>
              <Text style={styles.resultsCount}>
                Найдено {restaurants.length} {restaurants.length === 1 ? 'ресторан' : 'ресторанов'}
              </Text>
            </View>
          ) : null
        }
      />

      {/* Плавающая кнопка карты */}
      <TouchableOpacity
        style={styles.mapButton}
        onPress={() => navigation.navigate('RestaurantsMap')}
      >
        <Ionicons name="map" size={24} color={colors.white} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  cuisinesContainer: {
    paddingVertical: spacing.sm,
  },
  cuisinesList: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  cuisineButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    gap: spacing.xs,
  },
  cuisineButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  cuisineIcon: {
    fontSize: 16,
  },
  cuisineButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  cuisineButtonTextActive: {
    color: colors.white,
  },
  listContainer: {
    padding: spacing.md,
  },
  listHeader: {
    paddingHorizontal: spacing.sm,
    paddingBottom: spacing.md,
  },
  resultsCount: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium,
  },
  restaurantCard: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    marginBottom: spacing.md,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  restaurantImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  restaurantImageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  favoriteButton: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  ratingBadge: {
    position: 'absolute',
    top: spacing.md + 40,
    left: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  cuisineBadge: {
    position: 'absolute',
    top: spacing.md + 80,
    left: spacing.md,
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  cuisineBadgeText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  restaurantInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.md,
  },
  restaurantName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  restaurantAddress: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  restaurantDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  restaurantDistance: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  priceRange: {
    fontSize: typography.sizes.sm,
    color: colors.success,
    fontWeight: typography.weights.bold,
  },
  deliveryTime: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  featureTag: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  featureText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  moreFeatures: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium,
  },
  restaurantActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.surface,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    backgroundColor: colors.background,
    gap: spacing.xs,
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.primary,
  },
  mapButton: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default RestaurantsScreen;
