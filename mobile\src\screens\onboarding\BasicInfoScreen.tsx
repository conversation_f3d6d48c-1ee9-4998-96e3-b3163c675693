import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  StatusBar,
  Animated,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatePicker from 'react-native-date-picker';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface BasicInfoScreenProps {
  navigation: any;
}

interface BasicInfo {
  firstName: string;
  lastName: string;
  birthDate: Date;
  gender: 'male' | 'female' | 'other' | '';
  city: string;
}

const BasicInfoScreen: React.FC<BasicInfoScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { updateProfile } = useAuth();

  const [basicInfo, setBasicInfo] = useState<BasicInfo>({
    firstName: '',
    lastName: '',
    birthDate: new Date(2000, 0, 1),
    gender: '',
    city: '',
  });

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Анимации
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);
  const progressAnim = new Animated.Value(0);

  useEffect(() => {
    // Анимация появления
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(progressAnim, {
        toValue: 0.2, // 20% прогресса (1 из 5 шагов)
        duration: 1000,
        useNativeDriver: false,
      }),
    ]).start();
  }, []);

  const calculateAge = (birthDate: Date) => {
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1;
    }
    return age;
  };

  const validateForm = () => {
    if (!basicInfo.firstName.trim()) {
      Alert.alert('Ошибка', 'Пожалуйста, введите ваше имя');
      return false;
    }

    if (!basicInfo.lastName.trim()) {
      Alert.alert('Ошибка', 'Пожалуйста, введите вашу фамилию');
      return false;
    }

    const age = calculateAge(basicInfo.birthDate);
    if (age < 18) {
      Alert.alert('Ошибка', 'Вам должно быть не менее 18 лет');
      return false;
    }

    if (age > 100) {
      Alert.alert('Ошибка', 'Пожалуйста, укажите корректную дату рождения');
      return false;
    }

    if (!basicInfo.gender) {
      Alert.alert('Ошибка', 'Пожалуйста, выберите ваш пол');
      return false;
    }

    if (!basicInfo.city.trim()) {
      Alert.alert('Ошибка', 'Пожалуйста, укажите ваш город');
      return false;
    }

    return true;
  };

  const handleNext = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      const result = await updateProfile({
        firstName: basicInfo.firstName.trim(),
        lastName: basicInfo.lastName.trim(),
        birthDate: basicInfo.birthDate.toISOString(),
        gender: basicInfo.gender,
        city: basicInfo.city.trim(),
      });

      if (result.success) {
        navigation.navigate('InterestsScreen', { basicInfo });
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось сохранить информацию');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при сохранении данных');
    } finally {
      setIsLoading(false);
    }
  };

  const genderOptions = [
    { id: 'male', label: 'Мужской', icon: 'male' },
    { id: 'female', label: 'Женский', icon: 'female' },
    { id: 'other', label: 'Другой', icon: 'transgender' },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Прогресс бар */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <Animated.View
                style={[
                  styles.progressFill,
                  {
                    width: progressAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'],
                    }),
                  },
                ]}
              />
            </View>
            <Text style={styles.progressText}>Шаг 1 из 5</Text>
          </View>

          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Animated.View
              style={[
                styles.content,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              {/* Заголовок */}
              <View style={styles.headerContainer}>
                <Icon name="person" size={60} color="#FFFFFF" />
                <Text style={styles.title}>Расскажите о себе</Text>
                <Text style={styles.subtitle}>
                  Эта информация поможет другим пользователям лучше вас узнать
                </Text>
              </View>

              {/* Форма */}
              <View style={styles.formContainer}>
                {/* Имя */}
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Имя</Text>
                  <TextInput
                    style={styles.textInput}
                    placeholder="Введите ваше имя"
                    placeholderTextColor="rgba(255, 255, 255, 0.7)"
                    value={basicInfo.firstName}
                    onChangeText={(text) =>
                      setBasicInfo({ ...basicInfo, firstName: text })
                    }
                    autoCapitalize="words"
                  />
                </View>

                {/* Фамилия */}
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Фамилия</Text>
                  <TextInput
                    style={styles.textInput}
                    placeholder="Введите вашу фамилию"
                    placeholderTextColor="rgba(255, 255, 255, 0.7)"
                    value={basicInfo.lastName}
                    onChangeText={(text) =>
                      setBasicInfo({ ...basicInfo, lastName: text })
                    }
                    autoCapitalize="words"
                  />
                </View>

                {/* Дата рождения */}
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>
                    Дата рождения (возраст: {calculateAge(basicInfo.birthDate)} лет)
                  </Text>
                  <TouchableOpacity
                    style={styles.dateButton}
                    onPress={() => setShowDatePicker(true)}
                  >
                    <Text style={styles.dateButtonText}>
                      {basicInfo.birthDate.toLocaleDateString('ru-RU')}
                    </Text>
                    <Icon name="calendar-today" size={20} color="#FFFFFF" />
                  </TouchableOpacity>
                </View>

                {/* Пол */}
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Пол</Text>
                  <View style={styles.genderContainer}>
                    {genderOptions.map((option) => (
                      <TouchableOpacity
                        key={option.id}
                        style={[
                          styles.genderButton,
                          basicInfo.gender === option.id && styles.genderButtonActive,
                        ]}
                        onPress={() =>
                          setBasicInfo({ ...basicInfo, gender: option.id as any })
                        }
                      >
                        <Icon
                          name={option.icon}
                          size={24}
                          color={
                            basicInfo.gender === option.id
                              ? '#FF6B9D'
                              : 'rgba(255, 255, 255, 0.8)'
                          }
                        />
                        <Text
                          style={[
                            styles.genderButtonText,
                            basicInfo.gender === option.id && styles.genderButtonTextActive,
                          ]}
                        >
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                {/* Город */}
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Город</Text>
                  <TextInput
                    style={styles.textInput}
                    placeholder="Введите ваш город"
                    placeholderTextColor="rgba(255, 255, 255, 0.7)"
                    value={basicInfo.city}
                    onChangeText={(text) =>
                      setBasicInfo({ ...basicInfo, city: text })
                    }
                    autoCapitalize="words"
                  />
                </View>
              </View>
            </Animated.View>
          </ScrollView>

          {/* Кнопки */}
          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Icon name="arrow-back" size={24} color="#FFFFFF" />
              <Text style={styles.backButtonText}>Назад</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.nextButton}
              onPress={handleNext}
              disabled={isLoading}
            >
              <View style={styles.nextButtonContent}>
                {isLoading ? (
                  <Text style={styles.nextButtonText}>Сохранение...</Text>
                ) : (
                  <>
                    <Text style={styles.nextButtonText}>Далее</Text>
                    <Icon name="arrow-forward" size={20} color="#FF6B9D" />
                  </>
                )}
              </View>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>

        {/* Date Picker */}
        <DatePicker
          modal
          open={showDatePicker}
          date={basicInfo.birthDate}
          mode="date"
          maximumDate={new Date()}
          minimumDate={new Date(1920, 0, 1)}
          onConfirm={(date) => {
            setShowDatePicker(false);
            setBasicInfo({ ...basicInfo, birthDate: date });
          }}
          onCancel={() => setShowDatePicker(false)}
          title="Выберите дату рождения"
          confirmText="Подтвердить"
          cancelText="Отмена"
        />
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  keyboardAvoid: {
    flex: 1,
  },
  progressContainer: {
    paddingHorizontal: 30,
    paddingTop: 60,
    paddingBottom: 20,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
  progressText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    paddingHorizontal: 30,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  formContainer: {
    marginBottom: 40,
  },
  inputContainer: {
    marginBottom: 25,
  },
  inputLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  textInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 16,
    paddingHorizontal: 20,
    fontSize: 16,
    color: '#FFFFFF',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  dateButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  dateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  genderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  genderButton: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 16,
    paddingHorizontal: 12,
    alignItems: 'center',
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  genderButtonActive: {
    backgroundColor: '#FFFFFF',
  },
  genderButtonText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
  genderButtonTextActive: {
    color: '#FF6B9D',
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingBottom: 40,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
  nextButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingVertical: 16,
    paddingHorizontal: 30,
  },
  nextButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextButtonText: {
    color: '#FF6B9D',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
});

export default BasicInfoScreen;
