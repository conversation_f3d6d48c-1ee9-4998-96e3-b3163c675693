import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  StatusBar,
  Animated,
  ScrollView,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface InterestsScreenProps {
  navigation: any;
  route: any;
}

interface Interest {
  id: string;
  name: string;
  icon: string;
  category: string;
}

const InterestsScreen: React.FC<InterestsScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { updateProfile } = useAuth();
  const { basicInfo } = route.params || {};

  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Анимации
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);
  const progressAnim = new Animated.Value(0);

  const interests: Interest[] = [
    // Спорт и активность
    { id: 'fitness', name: 'Фитнес', icon: 'fitness-center', category: 'Спорт' },
    { id: 'running', name: 'Бег', icon: 'directions-run', category: 'Спорт' },
    { id: 'yoga', name: 'Йога', icon: 'self-improvement', category: 'Спорт' },
    { id: 'swimming', name: 'Плавание', icon: 'pool', category: 'Спорт' },
    { id: 'cycling', name: 'Велоспорт', icon: 'directions-bike', category: 'Спорт' },
    { id: 'hiking', name: 'Походы', icon: 'terrain', category: 'Спорт' },

    // Творчество
    { id: 'music', name: 'Музыка', icon: 'music-note', category: 'Творчество' },
    { id: 'art', name: 'Искусство', icon: 'palette', category: 'Творчество' },
    { id: 'photography', name: 'Фотография', icon: 'camera-alt', category: 'Творчество' },
    { id: 'dancing', name: 'Танцы', icon: 'music-video', category: 'Творчество' },
    { id: 'writing', name: 'Писательство', icon: 'edit', category: 'Творчество' },
    { id: 'cooking', name: 'Кулинария', icon: 'restaurant', category: 'Творчество' },

    // Развлечения
    { id: 'movies', name: 'Кино', icon: 'movie', category: 'Развлечения' },
    { id: 'gaming', name: 'Игры', icon: 'sports-esports', category: 'Развлечения' },
    { id: 'reading', name: 'Чтение', icon: 'menu-book', category: 'Развлечения' },
    { id: 'theater', name: 'Театр', icon: 'theater-comedy', category: 'Развлечения' },
    { id: 'concerts', name: 'Концерты', icon: 'library-music', category: 'Развлечения' },
    { id: 'comedy', name: 'Комедия', icon: 'sentiment-very-satisfied', category: 'Развлечения' },

    // Путешествия
    { id: 'travel', name: 'Путешествия', icon: 'flight', category: 'Путешествия' },
    { id: 'adventure', name: 'Приключения', icon: 'explore', category: 'Путешествия' },
    { id: 'camping', name: 'Кемпинг', icon: 'nature', category: 'Путешествия' },
    { id: 'beach', name: 'Пляж', icon: 'beach-access', category: 'Путешествия' },

    // Образование
    { id: 'science', name: 'Наука', icon: 'science', category: 'Образование' },
    { id: 'technology', name: 'Технологии', icon: 'computer', category: 'Образование' },
    { id: 'languages', name: 'Языки', icon: 'translate', category: 'Образование' },
    { id: 'history', name: 'История', icon: 'account-balance', category: 'Образование' },

    // Социальное
    { id: 'volunteering', name: 'Волонтерство', icon: 'volunteer-activism', category: 'Социальное' },
    { id: 'networking', name: 'Нетворкинг', icon: 'group', category: 'Социальное' },
    { id: 'pets', name: 'Животные', icon: 'pets', category: 'Социальное' },
    { id: 'family', name: 'Семья', icon: 'family-restroom', category: 'Социальное' },
  ];

  useEffect(() => {
    // Анимация появления
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(progressAnim, {
        toValue: 0.4, // 40% прогресса (2 из 5 шагов)
        duration: 1000,
        useNativeDriver: false,
      }),
    ]).start();
  }, []);

  const toggleInterest = (interestId: string) => {
    setSelectedInterests(prev => {
      if (prev.includes(interestId)) {
        return prev.filter(id => id !== interestId);
      } else {
        if (prev.length >= 10) {
          Alert.alert('Ограничение', 'Можно выбрать максимум 10 интересов');
          return prev;
        }
        return [...prev, interestId];
      }
    });
  };

  const handleNext = async () => {
    if (selectedInterests.length < 3) {
      Alert.alert('Выберите интересы', 'Пожалуйста, выберите минимум 3 интереса');
      return;
    }

    setIsLoading(true);

    try {
      const result = await updateProfile({
        interests: selectedInterests,
      });

      if (result.success) {
        navigation.navigate('PhotoUploadScreen', { 
          basicInfo, 
          interests: selectedInterests 
        });
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось сохранить интересы');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при сохранении данных');
    } finally {
      setIsLoading(false);
    }
  };

  const renderInterest = ({ item }: { item: Interest }) => (
    <TouchableOpacity
      style={[
        styles.interestButton,
        selectedInterests.includes(item.id) && styles.interestButtonSelected,
      ]}
      onPress={() => toggleInterest(item.id)}
    >
      <Icon
        name={item.icon}
        size={24}
        color={
          selectedInterests.includes(item.id)
            ? '#FF6B9D'
            : 'rgba(255, 255, 255, 0.8)'
        }
      />
      <Text
        style={[
          styles.interestButtonText,
          selectedInterests.includes(item.id) && styles.interestButtonTextSelected,
        ]}
      >
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const groupedInterests = interests.reduce((acc, interest) => {
    if (!acc[interest.category]) {
      acc[interest.category] = [];
    }
    acc[interest.category].push(interest);
    return acc;
  }, {} as Record<string, Interest[]>);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Прогресс бар */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>Шаг 2 из 5</Text>
        </View>

        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Заголовок */}
            <View style={styles.headerContainer}>
              <Icon name="favorite" size={60} color="#FFFFFF" />
              <Text style={styles.title}>Ваши интересы</Text>
              <Text style={styles.subtitle}>
                Выберите то, что вам нравится. Это поможет найти людей с похожими увлечениями
              </Text>
              <Text style={styles.counter}>
                Выбрано: {selectedInterests.length}/10 (минимум 3)
              </Text>
            </View>

            {/* Интересы по категориям */}
            <View style={styles.interestsContainer}>
              {Object.entries(groupedInterests).map(([category, categoryInterests]) => (
                <View key={category} style={styles.categoryContainer}>
                  <Text style={styles.categoryTitle}>{category}</Text>
                  <View style={styles.categoryInterests}>
                    {categoryInterests.map((interest) => (
                      <TouchableOpacity
                        key={interest.id}
                        style={[
                          styles.interestButton,
                          selectedInterests.includes(interest.id) && styles.interestButtonSelected,
                        ]}
                        onPress={() => toggleInterest(interest.id)}
                      >
                        <Icon
                          name={interest.icon}
                          size={20}
                          color={
                            selectedInterests.includes(interest.id)
                              ? '#FF6B9D'
                              : 'rgba(255, 255, 255, 0.8)'
                          }
                        />
                        <Text
                          style={[
                            styles.interestButtonText,
                            selectedInterests.includes(interest.id) && styles.interestButtonTextSelected,
                          ]}
                        >
                          {interest.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              ))}
            </View>
          </Animated.View>
        </ScrollView>

        {/* Кнопки */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
            <Text style={styles.backButtonText}>Назад</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.nextButton,
              selectedInterests.length < 3 && styles.nextButtonDisabled,
            ]}
            onPress={handleNext}
            disabled={isLoading || selectedInterests.length < 3}
          >
            <View style={styles.nextButtonContent}>
              {isLoading ? (
                <Text style={styles.nextButtonText}>Сохранение...</Text>
              ) : (
                <>
                  <Text style={styles.nextButtonText}>Далее</Text>
                  <Icon name="arrow-forward" size={20} color="#FF6B9D" />
                </>
              )}
            </View>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  progressContainer: {
    paddingHorizontal: 30,
    paddingTop: 60,
    paddingBottom: 20,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
  progressText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 120,
  },
  content: {
    paddingHorizontal: 30,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 15,
  },
  counter: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  interestsContainer: {
    marginBottom: 40,
  },
  categoryContainer: {
    marginBottom: 25,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 15,
  },
  categoryInterests: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  interestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 15,
    margin: 5,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  interestButtonSelected: {
    backgroundColor: '#FFFFFF',
  },
  interestButtonText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  interestButtonTextSelected: {
    color: '#FF6B9D',
  },
  buttonsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingBottom: 40,
    paddingTop: 20,
    backgroundColor: 'rgba(255, 107, 157, 0.1)',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
  nextButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingVertical: 16,
    paddingHorizontal: 30,
  },
  nextButtonDisabled: {
    opacity: 0.5,
  },
  nextButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextButtonText: {
    color: '#FF6B9D',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
});

export default InterestsScreen;
