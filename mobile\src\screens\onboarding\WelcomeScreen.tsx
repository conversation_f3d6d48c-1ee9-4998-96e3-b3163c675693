import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Animated,
  ScrollView,
  Image,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';

const { width, height } = Dimensions.get('window');

interface WelcomeScreenProps {
  navigation: any;
}

interface OnboardingSlide {
  id: number;
  title: string;
  subtitle: string;
  icon: string;
  description: string;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const scrollViewRef = useRef<ScrollView>(null);

  // Анимации
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const logoScale = useRef(new Animated.Value(0)).current;

  const slides: OnboardingSlide[] = [
    {
      id: 1,
      title: 'Добро пожаловать',
      subtitle: 'в Likes Love',
      icon: 'favorite',
      description: 'Найдите свою вторую половинку среди миллионов пользователей',
    },
    {
      id: 2,
      title: 'Безопасные знакомства',
      subtitle: 'Верифицированные профили',
      icon: 'verified-user',
      description: 'Все профили проходят проверку для вашей безопасности',
    },
    {
      id: 3,
      title: 'Умный поиск',
      subtitle: 'Идеальные совпадения',
      icon: 'psychology',
      description: 'Наш алгоритм найдет людей, которые вам действительно подходят',
    },
    {
      id: 4,
      title: 'Начните знакомиться',
      subtitle: 'Прямо сейчас',
      icon: 'rocket-launch',
      description: 'Создайте профиль и начните общаться уже сегодня',
    },
  ];

  useEffect(() => {
    // Анимация появления
    Animated.sequence([
      Animated.timing(logoScale, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    // Автоматическая прокрутка слайдов
    const interval = setInterval(() => {
      // Здесь можно добавить автоматическую прокрутку
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  const handleGetStarted = () => {
    navigation.navigate('BasicInfoScreen');
  };

  const handleLogin = () => {
    navigation.navigate('Login');
  };

  const renderSlide = (slide: OnboardingSlide, index: number) => (
    <View key={slide.id} style={styles.slide}>
      <View style={styles.slideContent}>
        <View style={styles.iconContainer}>
          <Icon name={slide.icon} size={80} color="#FFFFFF" />
        </View>
        
        <Text style={styles.slideTitle}>{slide.title}</Text>
        <Text style={styles.slideSubtitle}>{slide.subtitle}</Text>
        <Text style={styles.slideDescription}>{slide.description}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Логотип */}
        <Animated.View
          style={[
            styles.logoContainer,
            {
              transform: [{ scale: logoScale }],
            },
          ]}
        >
          <View style={styles.logoCircle}>
            <Icon name="favorite" size={40} color="#FFFFFF" />
          </View>
          <Text style={styles.logoText}>Likes Love</Text>
        </Animated.View>

        {/* Слайды */}
        <Animated.View
          style={[
            styles.slidesContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <ScrollView
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            style={styles.scrollView}
          >
            {slides.map((slide, index) => renderSlide(slide, index))}
          </ScrollView>
        </Animated.View>

        {/* Индикаторы */}
        <View style={styles.indicatorsContainer}>
          {slides.map((_, index) => (
            <View
              key={index}
              style={[
                styles.indicator,
                index === 0 && styles.activeIndicator, // Первый слайд активен по умолчанию
              ]}
            />
          ))}
        </View>

        {/* Кнопки */}
        <Animated.View
          style={[
            styles.buttonsContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <TouchableOpacity
            style={styles.getStartedButton}
            onPress={handleGetStarted}
          >
            <LinearGradient
              colors={['#FFFFFF', '#F0F0F0']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.getStartedButtonText}>Начать знакомства</Text>
              <Icon name="arrow-forward" size={20} color="#FF6B9D" />
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.loginButton}
            onPress={handleLogin}
          >
            <Text style={styles.loginButtonText}>Уже есть аккаунт? Войти</Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Дополнительная информация */}
        <View style={styles.footerContainer}>
          <View style={styles.featuresContainer}>
            <View style={styles.feature}>
              <Icon name="security" size={16} color="rgba(255, 255, 255, 0.8)" />
              <Text style={styles.featureText}>Безопасно</Text>
            </View>
            <View style={styles.feature}>
              <Icon name="verified" size={16} color="rgba(255, 255, 255, 0.8)" />
              <Text style={styles.featureText}>Проверено</Text>
            </View>
            <View style={styles.feature}>
              <Icon name="favorite" size={16} color="rgba(255, 255, 255, 0.8)" />
              <Text style={styles.featureText}>Эффективно</Text>
            </View>
          </View>

          <Text style={styles.footerText}>
            Присоединяйтесь к миллионам пользователей, которые уже нашли свою любовь
          </Text>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  logoContainer: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  slidesContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  slide: {
    width: width,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  slideContent: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  slideTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  slideSubtitle: {
    fontSize: 20,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 20,
  },
  slideDescription: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: '90%',
  },
  indicatorsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    marginHorizontal: 4,
  },
  activeIndicator: {
    backgroundColor: '#FFFFFF',
    width: 24,
  },
  buttonsContainer: {
    paddingHorizontal: 30,
    paddingBottom: 20,
  },
  getStartedButton: {
    marginBottom: 15,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 30,
    borderRadius: 15,
  },
  getStartedButtonText: {
    color: '#FF6B9D',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 10,
  },
  loginButton: {
    alignItems: 'center',
    paddingVertical: 15,
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    textDecorationLine: 'underline',
  },
  footerContainer: {
    paddingHorizontal: 30,
    paddingBottom: 40,
  },
  featuresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  feature: {
    alignItems: 'center',
  },
  featureText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 4,
  },
  footerText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default WelcomeScreen;
