import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  StatusBar,
  Animated,
  ScrollView,
  Switch,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Slider from '@react-native-community/slider';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface PreferencesScreenProps {
  navigation: any;
  route: any;
}

interface Preferences {
  ageRange: [number, number];
  maxDistance: number;
  interestedIn: 'male' | 'female' | 'both';
  relationshipGoals: string[];
  showMe: 'everyone' | 'verified' | 'premium';
  allowMessages: 'everyone' | 'matches' | 'premium';
  showOnlineStatus: boolean;
  showDistance: boolean;
  showAge: boolean;
}

const PreferencesScreen: React.FC<PreferencesScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { updateProfile } = useAuth();
  const { basicInfo, interests, photos } = route.params || {};

  const [preferences, setPreferences] = useState<Preferences>({
    ageRange: [18, 35],
    maxDistance: 50,
    interestedIn: 'both',
    relationshipGoals: [],
    showMe: 'everyone',
    allowMessages: 'matches',
    showOnlineStatus: true,
    showDistance: true,
    showAge: true,
  });

  const [isLoading, setIsLoading] = useState(false);

  // Анимации
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);
  const progressAnim = new Animated.Value(0);

  useEffect(() => {
    // Анимация появления
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(progressAnim, {
        toValue: 0.8, // 80% прогресса (4 из 5 шагов)
        duration: 1000,
        useNativeDriver: false,
      }),
    ]).start();
  }, []);

  const relationshipGoalsOptions = [
    { id: 'serious', label: 'Серьезные отношения', icon: 'favorite' },
    { id: 'casual', label: 'Легкое общение', icon: 'chat' },
    { id: 'friendship', label: 'Дружба', icon: 'group' },
    { id: 'networking', label: 'Знакомства', icon: 'people' },
    { id: 'travel', label: 'Попутчики', icon: 'flight' },
    { id: 'activity', label: 'Совместные увлечения', icon: 'sports-tennis' },
  ];

  const toggleRelationshipGoal = (goalId: string) => {
    setPreferences(prev => ({
      ...prev,
      relationshipGoals: prev.relationshipGoals.includes(goalId)
        ? prev.relationshipGoals.filter(id => id !== goalId)
        : [...prev.relationshipGoals, goalId]
    }));
  };

  const handleNext = async () => {
    if (preferences.relationshipGoals.length === 0) {
      Alert.alert('Выберите цели', 'Пожалуйста, выберите хотя бы одну цель знакомства');
      return;
    }

    setIsLoading(true);

    try {
      const result = await updateProfile({
        preferences: preferences,
      });

      if (result.success) {
        navigation.navigate('CompleteScreen', {
          basicInfo,
          interests,
          photos,
          preferences,
        });
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось сохранить предпочтения');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при сохранении данных');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Прогресс бар */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>Шаг 4 из 5</Text>
        </View>

        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Заголовок */}
            <View style={styles.headerContainer}>
              <Icon name="tune" size={60} color="#FFFFFF" />
              <Text style={styles.title}>Ваши предпочтения</Text>
              <Text style={styles.subtitle}>
                Настройте параметры поиска, чтобы находить подходящих людей
              </Text>
            </View>

            {/* Возрастной диапазон */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Возраст</Text>
              <Text style={styles.sectionSubtitle}>
                {preferences.ageRange[0]} - {preferences.ageRange[1]} лет
              </Text>
              <View style={styles.sliderContainer}>
                <Text style={styles.sliderLabel}>18</Text>
                <Slider
                  style={styles.slider}
                  minimumValue={18}
                  maximumValue={65}
                  value={preferences.ageRange[1]}
                  onValueChange={(value) =>
                    setPreferences(prev => ({
                      ...prev,
                      ageRange: [Math.min(prev.ageRange[0], value), value]
                    }))
                  }
                  minimumTrackTintColor="#FFFFFF"
                  maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
                  thumbStyle={styles.sliderThumb}
                />
                <Text style={styles.sliderLabel}>65</Text>
              </View>
            </View>

            {/* Расстояние */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Максимальное расстояние</Text>
              <Text style={styles.sectionSubtitle}>
                {preferences.maxDistance} км
              </Text>
              <View style={styles.sliderContainer}>
                <Text style={styles.sliderLabel}>1</Text>
                <Slider
                  style={styles.slider}
                  minimumValue={1}
                  maximumValue={100}
                  value={preferences.maxDistance}
                  onValueChange={(value) =>
                    setPreferences(prev => ({ ...prev, maxDistance: Math.round(value) }))
                  }
                  minimumTrackTintColor="#FFFFFF"
                  maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
                  thumbStyle={styles.sliderThumb}
                />
                <Text style={styles.sliderLabel}>100</Text>
              </View>
            </View>

            {/* Кого ищете */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Кого вы ищете?</Text>
              <View style={styles.optionsContainer}>
                {[
                  { id: 'male', label: 'Мужчин', icon: 'male' },
                  { id: 'female', label: 'Женщин', icon: 'female' },
                  { id: 'both', label: 'Всех', icon: 'people' },
                ].map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.optionButton,
                      preferences.interestedIn === option.id && styles.optionButtonActive,
                    ]}
                    onPress={() =>
                      setPreferences(prev => ({ ...prev, interestedIn: option.id as any }))
                    }
                  >
                    <Icon
                      name={option.icon}
                      size={24}
                      color={
                        preferences.interestedIn === option.id
                          ? '#FF6B9D'
                          : 'rgba(255, 255, 255, 0.8)'
                      }
                    />
                    <Text
                      style={[
                        styles.optionButtonText,
                        preferences.interestedIn === option.id && styles.optionButtonTextActive,
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Цели знакомства */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Цели знакомства</Text>
              <Text style={styles.sectionSubtitle}>Выберите одну или несколько</Text>
              <View style={styles.goalsContainer}>
                {relationshipGoalsOptions.map((goal) => (
                  <TouchableOpacity
                    key={goal.id}
                    style={[
                      styles.goalButton,
                      preferences.relationshipGoals.includes(goal.id) && styles.goalButtonActive,
                    ]}
                    onPress={() => toggleRelationshipGoal(goal.id)}
                  >
                    <Icon
                      name={goal.icon}
                      size={20}
                      color={
                        preferences.relationshipGoals.includes(goal.id)
                          ? '#FF6B9D'
                          : 'rgba(255, 255, 255, 0.8)'
                      }
                    />
                    <Text
                      style={[
                        styles.goalButtonText,
                        preferences.relationshipGoals.includes(goal.id) && styles.goalButtonTextActive,
                      ]}
                    >
                      {goal.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Настройки приватности */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Настройки приватности</Text>
              
              <View style={styles.switchContainer}>
                <View style={styles.switchItem}>
                  <Text style={styles.switchLabel}>Показывать статус онлайн</Text>
                  <Switch
                    value={preferences.showOnlineStatus}
                    onValueChange={(value) =>
                      setPreferences(prev => ({ ...prev, showOnlineStatus: value }))
                    }
                    trackColor={{ false: 'rgba(255, 255, 255, 0.3)', true: '#FFFFFF' }}
                    thumbColor={preferences.showOnlineStatus ? '#FF6B9D' : '#f4f3f4'}
                  />
                </View>

                <View style={styles.switchItem}>
                  <Text style={styles.switchLabel}>Показывать расстояние</Text>
                  <Switch
                    value={preferences.showDistance}
                    onValueChange={(value) =>
                      setPreferences(prev => ({ ...prev, showDistance: value }))
                    }
                    trackColor={{ false: 'rgba(255, 255, 255, 0.3)', true: '#FFFFFF' }}
                    thumbColor={preferences.showDistance ? '#FF6B9D' : '#f4f3f4'}
                  />
                </View>

                <View style={styles.switchItem}>
                  <Text style={styles.switchLabel}>Показывать возраст</Text>
                  <Switch
                    value={preferences.showAge}
                    onValueChange={(value) =>
                      setPreferences(prev => ({ ...prev, showAge: value }))
                    }
                    trackColor={{ false: 'rgba(255, 255, 255, 0.3)', true: '#FFFFFF' }}
                    thumbColor={preferences.showAge ? '#FF6B9D' : '#f4f3f4'}
                  />
                </View>
              </View>
            </View>
          </Animated.View>
        </ScrollView>

        {/* Кнопки */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
            <Text style={styles.backButtonText}>Назад</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.nextButton,
              preferences.relationshipGoals.length === 0 && styles.nextButtonDisabled,
            ]}
            onPress={handleNext}
            disabled={isLoading || preferences.relationshipGoals.length === 0}
          >
            <View style={styles.nextButtonContent}>
              {isLoading ? (
                <Text style={styles.nextButtonText}>Сохранение...</Text>
              ) : (
                <>
                  <Text style={styles.nextButtonText}>Завершить</Text>
                  <Icon name="arrow-forward" size={20} color="#FF6B9D" />
                </>
              )}
            </View>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  progressContainer: {
    paddingHorizontal: 30,
    paddingTop: 60,
    paddingBottom: 20,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
  progressText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 120,
  },
  content: {
    paddingHorizontal: 30,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  sectionContainer: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 15,
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  slider: {
    flex: 1,
    height: 40,
    marginHorizontal: 15,
  },
  sliderThumb: {
    backgroundColor: '#FFFFFF',
    width: 20,
    height: 20,
  },
  sliderLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    minWidth: 20,
    textAlign: 'center',
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  optionButton: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 16,
    paddingHorizontal: 12,
    alignItems: 'center',
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  optionButtonActive: {
    backgroundColor: '#FFFFFF',
  },
  optionButtonText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
  optionButtonTextActive: {
    color: '#FF6B9D',
  },
  goalsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  goalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 15,
    margin: 5,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  goalButtonActive: {
    backgroundColor: '#FFFFFF',
  },
  goalButtonText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  goalButtonTextActive: {
    color: '#FF6B9D',
  },
  switchContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 15,
    padding: 15,
  },
  switchItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    flex: 1,
  },
  buttonsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingBottom: 40,
    paddingTop: 20,
    backgroundColor: 'rgba(255, 107, 157, 0.1)',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
  nextButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingVertical: 16,
    paddingHorizontal: 30,
  },
  nextButtonDisabled: {
    opacity: 0.5,
  },
  nextButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextButtonText: {
    color: '#FF6B9D',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
});

export default PreferencesScreen;
