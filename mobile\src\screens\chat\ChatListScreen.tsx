import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  RefreshControl,
  Alert,
  TextInput,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface ChatListScreenProps {
  navigation: any;
}

interface Chat {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  avatar: string;
  isOnline: boolean;
  isVerified: boolean;
  isPremium: boolean;
  lastMessage: {
    id: string;
    text: string;
    timestamp: string;
    senderId: string;
    isRead: boolean;
    messageType: 'text' | 'image' | 'voice' | 'video' | 'file';
  };
  unreadCount: number;
  isTyping: boolean;
  isPinned: boolean;
  isMuted: boolean;
  isArchived: boolean;
  matchedAt: string;
  lastSeen?: string;
}

const ChatListScreen: React.FC<ChatListScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { getChats, markChatAsRead, deleteChat, archiveChat, pinChat, muteChat } = useAuth();

  const [chats, setChats] = useState<Chat[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'unread' | 'pinned' | 'archived'>('all');
  const [selectedChats, setSelectedChats] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  useEffect(() => {
    loadChats();
  }, [filter]);

  const loadChats = async (refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const result = await getChats({
        filter,
        search: searchQuery,
      });

      if (result.success) {
        setChats(result.chats || []);
      }
    } catch (error) {
      console.error('Error loading chats:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = useCallback(() => {
    loadChats(true);
  }, [filter, searchQuery]);

  const handleChatPress = (chat: Chat) => {
    if (isSelectionMode) {
      toggleChatSelection(chat.id);
    } else {
      // Отмечаем чат как прочитанный
      if (chat.unreadCount > 0) {
        markChatAsRead(chat.id);
        setChats(prev =>
          prev.map(c =>
            c.id === chat.id
              ? { ...c, unreadCount: 0, lastMessage: { ...c.lastMessage, isRead: true } }
              : c
          )
        );
      }

      navigation.navigate('ChatScreen', {
        userId: chat.userId,
        userName: `${chat.firstName} ${chat.lastName}`,
        userPhoto: chat.avatar,
        chatId: chat.id,
      });
    }
  };

  const handleChatLongPress = (chat: Chat) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedChats([chat.id]);
    }
  };

  const toggleChatSelection = (chatId: string) => {
    setSelectedChats(prev =>
      prev.includes(chatId)
        ? prev.filter(id => id !== chatId)
        : [...prev, chatId]
    );
  };

  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedChats([]);
  };

  const handleBulkAction = async (action: 'delete' | 'archive' | 'pin' | 'mute') => {
    if (selectedChats.length === 0) return;

    try {
      switch (action) {
        case 'delete':
          Alert.alert(
            'Удалить чаты',
            `Вы уверены, что хотите удалить ${selectedChats.length} чат(ов)?`,
            [
              { text: 'Отмена', style: 'cancel' },
              {
                text: 'Удалить',
                style: 'destructive',
                onPress: async () => {
                  for (const chatId of selectedChats) {
                    await deleteChat(chatId);
                  }
                  setChats(prev => prev.filter(c => !selectedChats.includes(c.id)));
                  exitSelectionMode();
                },
              },
            ]
          );
          break;

        case 'archive':
          for (const chatId of selectedChats) {
            await archiveChat(chatId);
          }
          setChats(prev =>
            prev.map(c =>
              selectedChats.includes(c.id) ? { ...c, isArchived: true } : c
            )
          );
          exitSelectionMode();
          break;

        case 'pin':
          for (const chatId of selectedChats) {
            await pinChat(chatId);
          }
          setChats(prev =>
            prev.map(c =>
              selectedChats.includes(c.id) ? { ...c, isPinned: !c.isPinned } : c
            )
          );
          exitSelectionMode();
          break;

        case 'mute':
          for (const chatId of selectedChats) {
            await muteChat(chatId);
          }
          setChats(prev =>
            prev.map(c =>
              selectedChats.includes(c.id) ? { ...c, isMuted: !c.isMuted } : c
            )
          );
          exitSelectionMode();
          break;
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось выполнить действие');
    }
  };

  const formatLastMessageTime = (timestamp: string) => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Только что';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} мин`;
    } else if (diffInMinutes < 24 * 60) {
      const diffInHours = Math.floor(diffInMinutes / 60);
      return `${diffInHours} ч`;
    } else {
      const diffInDays = Math.floor(diffInMinutes / (24 * 60));
      return `${diffInDays} дн`;
    }
  };

  const getMessageTypeIcon = (messageType: string) => {
    switch (messageType) {
      case 'image':
        return 'photo';
      case 'voice':
        return 'mic';
      case 'video':
        return 'videocam';
      case 'file':
        return 'attach-file';
      default:
        return null;
    }
  };

  const formatLastMessageText = (message: Chat['lastMessage'], isCurrentUser: boolean) => {
    const prefix = isCurrentUser ? 'Вы: ' : '';
    
    switch (message.messageType) {
      case 'image':
        return `${prefix}Фото`;
      case 'voice':
        return `${prefix}Голосовое сообщение`;
      case 'video':
        return `${prefix}Видео`;
      case 'file':
        return `${prefix}Файл`;
      default:
        return `${prefix}${message.text}`;
    }
  };

  const filteredChats = chats.filter(chat => {
    const matchesSearch = searchQuery === '' || 
      `${chat.firstName} ${chat.lastName}`.toLowerCase().includes(searchQuery.toLowerCase());
    
    switch (filter) {
      case 'unread':
        return matchesSearch && chat.unreadCount > 0;
      case 'pinned':
        return matchesSearch && chat.isPinned;
      case 'archived':
        return matchesSearch && chat.isArchived;
      default:
        return matchesSearch && !chat.isArchived;
    }
  });

  const renderChat = ({ item }: { item: Chat }) => {
    const isSelected = selectedChats.includes(item.id);
    const isCurrentUser = item.lastMessage.senderId === 'current_user_id'; // Заменить на реальный ID

    return (
      <TouchableOpacity
        style={[
          styles.chatItem,
          isSelected && styles.selectedChatItem,
          item.isPinned && styles.pinnedChatItem,
        ]}
        onPress={() => handleChatPress(item)}
        onLongPress={() => handleChatLongPress(item)}
      >
        <View style={styles.avatarContainer}>
          <Image source={{ uri: item.avatar }} style={styles.avatar} />
          
          {/* Онлайн статус */}
          {item.isOnline && (
            <View style={styles.onlineIndicator} />
          )}

          {/* Верификация */}
          {item.isVerified && (
            <View style={styles.verifiedBadge}>
              <Icon name="verified" size={12} color="#4CAF50" />
            </View>
          )}

          {/* Премиум */}
          {item.isPremium && (
            <View style={styles.premiumBadge}>
              <Icon name="star" size={12} color="#FFD700" />
            </View>
          )}

          {/* Закрепленный чат */}
          {item.isPinned && (
            <View style={styles.pinBadge}>
              <Icon name="push-pin" size={10} color="#FF6B9D" />
            </View>
          )}
        </View>

        <View style={styles.chatContent}>
          <View style={styles.chatHeader}>
            <Text style={styles.chatName} numberOfLines={1}>
              {item.firstName} {item.lastName}
            </Text>
            <View style={styles.chatMeta}>
              {item.isMuted && (
                <Icon name="volume-off" size={14} color="#999" style={styles.muteIcon} />
              )}
              <Text style={styles.messageTime}>
                {formatLastMessageTime(item.lastMessage.timestamp)}
              </Text>
            </View>
          </View>

          <View style={styles.messageContainer}>
            <View style={styles.lastMessage}>
              {getMessageTypeIcon(item.lastMessage.messageType) && (
                <Icon
                  name={getMessageTypeIcon(item.lastMessage.messageType)!}
                  size={14}
                  color="#666"
                  style={styles.messageTypeIcon}
                />
              )}
              
              {item.isTyping ? (
                <Text style={styles.typingText}>печатает...</Text>
              ) : (
                <Text
                  style={[
                    styles.messageText,
                    !item.lastMessage.isRead && !isCurrentUser && styles.unreadMessageText,
                  ]}
                  numberOfLines={1}
                >
                  {formatLastMessageText(item.lastMessage, isCurrentUser)}
                </Text>
              )}
            </View>

            <View style={styles.messageStatus}>
              {/* Статус доставки для отправленных сообщений */}
              {isCurrentUser && (
                <Icon
                  name={item.lastMessage.isRead ? 'done-all' : 'done'}
                  size={14}
                  color={item.lastMessage.isRead ? '#4CAF50' : '#999'}
                  style={styles.deliveryIcon}
                />
              )}

              {/* Счетчик непрочитанных */}
              {item.unreadCount > 0 && (
                <View style={styles.unreadBadge}>
                  <Text style={styles.unreadCount}>
                    {item.unreadCount > 99 ? '99+' : item.unreadCount}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Чекбокс для режима выбора */}
        {isSelectionMode && (
          <View style={styles.selectionContainer}>
            <View style={[styles.checkbox, isSelected && styles.checkedCheckbox]}>
              {isSelected && <Icon name="check" size={16} color="#FFFFFF" />}
            </View>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="chat-bubble-outline" size={80} color="rgba(255, 255, 255, 0.5)" />
      <Text style={styles.emptyTitle}>
        {filter === 'unread' ? 'Нет непрочитанных чатов' :
         filter === 'pinned' ? 'Нет закрепленных чатов' :
         filter === 'archived' ? 'Нет архивных чатов' :
         'Пока нет чатов'}
      </Text>
      <Text style={styles.emptySubtitle}>
        {filter === 'all' 
          ? 'Начните общение с людьми, которые вам понравились'
          : 'Чаты появятся здесь, когда они будут соответствовать фильтру'
        }
      </Text>
      {filter === 'all' && (
        <TouchableOpacity
          style={styles.startChattingButton}
          onPress={() => navigation.navigate('SearchScreen')}
        >
          <Text style={styles.startChattingText}>Найти собеседников</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      {/* Поиск */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="rgba(255, 255, 255, 0.7)" />
        <TextInput
          style={styles.searchInput}
          placeholder="Поиск чатов..."
          placeholderTextColor="rgba(255, 255, 255, 0.7)"
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={() => loadChats()}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Icon name="clear" size={20} color="rgba(255, 255, 255, 0.7)" />
          </TouchableOpacity>
        )}
      </View>

      {/* Фильтры */}
      <View style={styles.filtersContainer}>
        {[
          { key: 'all', label: 'Все', count: chats.filter(c => !c.isArchived).length },
          { key: 'unread', label: 'Непрочитанные', count: chats.filter(c => c.unreadCount > 0).length },
          { key: 'pinned', label: 'Закрепленные', count: chats.filter(c => c.isPinned).length },
          { key: 'archived', label: 'Архив', count: chats.filter(c => c.isArchived).length },
        ].map((filterOption) => (
          <TouchableOpacity
            key={filterOption.key}
            style={[
              styles.filterTab,
              filter === filterOption.key && styles.activeFilterTab,
            ]}
            onPress={() => setFilter(filterOption.key as any)}
          >
            <Text
              style={[
                styles.filterTabText,
                filter === filterOption.key && styles.activeFilterTabText,
              ]}
            >
              {filterOption.label}
            </Text>
            {filterOption.count > 0 && (
              <View style={styles.filterBadge}>
                <Text style={styles.filterBadgeText}>{filterOption.count}</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderSelectionActions = () => {
    if (!isSelectionMode) return null;

    return (
      <View style={styles.selectionActions}>
        <TouchableOpacity
          style={styles.selectionAction}
          onPress={() => handleBulkAction('pin')}
        >
          <Icon name="push-pin" size={20} color="#FFFFFF" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.selectionAction}
          onPress={() => handleBulkAction('mute')}
        >
          <Icon name="volume-off" size={20} color="#FFFFFF" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.selectionAction}
          onPress={() => handleBulkAction('archive')}
        >
          <Icon name="archive" size={20} color="#FFFFFF" />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.selectionAction, styles.deleteAction]}
          onPress={() => handleBulkAction('delete')}
        >
          <Icon name="delete" size={20} color="#FFFFFF" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.cancelSelection}
          onPress={exitSelectionMode}
        >
          <Text style={styles.cancelSelectionText}>Отмена</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {isSelectionMode ? `Выбрано: ${selectedChats.length}` : 'Чаты'}
          </Text>
          <TouchableOpacity
            style={styles.newChatButton}
            onPress={() => navigation.navigate('NewChatScreen')}
          >
            <Icon name="add" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Поиск и фильтры */}
        {renderHeader()}

        {/* Список чатов */}
        <FlatList
          data={filteredChats}
          renderItem={renderChat}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
              colors={['#FFFFFF']}
            />
          }
          ListEmptyComponent={!isLoading ? renderEmptyState : null}
        />

        {/* Действия для выбранных чатов */}
        {renderSelectionActions()}

        {/* Индикатор загрузки */}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator color="#FFFFFF" size="large" />
          </View>
        )}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  newChatButton: {
    padding: 8,
  },
  headerContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginBottom: 15,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 10,
  },
  filtersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  filterTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 2,
  },
  activeFilterTab: {
    backgroundColor: '#FFFFFF',
  },
  filterTabText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 12,
    fontWeight: '600',
  },
  activeFilterTabText: {
    color: '#FF6B9D',
  },
  filterBadge: {
    backgroundColor: '#FF6B9D',
    borderRadius: 8,
    paddingHorizontal: 4,
    paddingVertical: 1,
    marginLeft: 4,
    minWidth: 16,
    alignItems: 'center',
  },
  filterBadgeText: {
    color: '#FFFFFF',
    fontSize: 9,
    fontWeight: 'bold',
  },
  listContainer: {
    paddingBottom: 20,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    marginHorizontal: 20,
    marginVertical: 4,
    borderRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedChatItem: {
    backgroundColor: 'rgba(255, 107, 157, 0.2)',
    borderWidth: 2,
    borderColor: '#FF6B9D',
  },
  pinnedChatItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B9D',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 15,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  verifiedBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 2,
  },
  premiumBadge: {
    position: 'absolute',
    top: -2,
    left: -2,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 2,
  },
  pinBadge: {
    position: 'absolute',
    top: -4,
    left: 15,
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    padding: 2,
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  chatMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  muteIcon: {
    marginRight: 4,
  },
  messageTime: {
    fontSize: 12,
    color: '#999',
  },
  messageContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  messageTypeIcon: {
    marginRight: 4,
  },
  messageText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  unreadMessageText: {
    fontWeight: 'bold',
    color: '#333',
  },
  typingText: {
    fontSize: 14,
    color: '#4ECDC4',
    fontStyle: 'italic',
  },
  messageStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryIcon: {
    marginRight: 4,
  },
  unreadBadge: {
    backgroundColor: '#FF6B9D',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  unreadCount: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  selectionContainer: {
    marginLeft: 10,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#DDD',
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedCheckbox: {
    backgroundColor: '#FF6B9D',
    borderColor: '#FF6B9D',
  },
  selectionActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 20,
    paddingVertical: 15,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  selectionAction: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#4ECDC4',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteAction: {
    backgroundColor: '#F44336',
  },
  cancelSelection: {
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  cancelSelectionText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  startChattingButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
  },
  startChattingText: {
    color: '#FF6B9D',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
});

export default ChatListScreen;
