import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Image,
  Animated,
  Alert,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface AudioCallScreenProps {
  navigation: any;
  route: any;
}

interface CallState {
  status: 'connecting' | 'ringing' | 'connected' | 'ended' | 'failed';
  duration: number;
  isMuted: boolean;
  isSpeakerOn: boolean;
  isIncoming: boolean;
  callId: string;
  userId: string;
  userName: string;
  userPhoto: string;
}

const AudioCallScreen: React.FC<AudioCallScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { startAudioCall, endCall, toggleMute, toggleSpeaker, answerCall, rejectCall } = useAuth();
  
  const { userId, userName, userPhoto, isIncoming = false, callId } = route.params;

  const [callState, setCallState] = useState<CallState>({
    status: isIncoming ? 'ringing' : 'connecting',
    duration: 0,
    isMuted: false,
    isSpeakerOn: false,
    isIncoming,
    callId: callId || '',
    userId,
    userName,
    userPhoto,
  });

  const pulseAnim = useRef(new Animated.Value(1)).current;
  const durationInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    requestAudioPermissions();
    
    if (!isIncoming) {
      initiateCall();
    }

    startPulseAnimation();

    return () => {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
      pulseAnim.stopAnimation();
    };
  }, []);

  useEffect(() => {
    if (callState.status === 'connected') {
      startDurationTimer();
    } else if (callState.status === 'ended' || callState.status === 'failed') {
      stopDurationTimer();
      setTimeout(() => {
        navigation.goBack();
      }, 2000);
    }
  }, [callState.status]);

  const requestAudioPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          PermissionsAndroid.PERMISSIONS.MODIFY_AUDIO_SETTINGS,
        ]);

        if (
          granted[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] !== PermissionsAndroid.RESULTS.GRANTED ||
          granted[PermissionsAndroid.PERMISSIONS.MODIFY_AUDIO_SETTINGS] !== PermissionsAndroid.RESULTS.GRANTED
        ) {
          Alert.alert(
            'Разрешения не предоставлены',
            'Для звонков необходимо разрешение на использование микрофона',
            [{ text: 'OK', onPress: () => navigation.goBack() }]
          );
        }
      } catch (error) {
        console.error('Error requesting permissions:', error);
      }
    }
  };

  const initiateCall = async () => {
    try {
      const result = await startAudioCall(userId);
      if (result.success) {
        setCallState(prev => ({
          ...prev,
          callId: result.callId,
          status: 'ringing',
        }));
      } else {
        setCallState(prev => ({ ...prev, status: 'failed' }));
        Alert.alert('Ошибка', 'Не удалось начать звонок');
      }
    } catch (error) {
      setCallState(prev => ({ ...prev, status: 'failed' }));
      Alert.alert('Ошибка', 'Произошла ошибка при звонке');
    }
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const startDurationTimer = () => {
    durationInterval.current = setInterval(() => {
      setCallState(prev => ({
        ...prev,
        duration: prev.duration + 1,
      }));
    }, 1000);
  };

  const stopDurationTimer = () => {
    if (durationInterval.current) {
      clearInterval(durationInterval.current);
      durationInterval.current = null;
    }
  };

  const handleAnswer = async () => {
    try {
      const result = await answerCall(callState.callId);
      if (result.success) {
        setCallState(prev => ({ ...prev, status: 'connected' }));
      } else {
        setCallState(prev => ({ ...prev, status: 'failed' }));
      }
    } catch (error) {
      setCallState(prev => ({ ...prev, status: 'failed' }));
    }
  };

  const handleReject = async () => {
    try {
      await rejectCall(callState.callId);
      setCallState(prev => ({ ...prev, status: 'ended' }));
    } catch (error) {
      setCallState(prev => ({ ...prev, status: 'ended' }));
    }
  };

  const handleEndCall = async () => {
    try {
      await endCall(callState.callId);
      setCallState(prev => ({ ...prev, status: 'ended' }));
    } catch (error) {
      setCallState(prev => ({ ...prev, status: 'ended' }));
    }
  };

  const handleToggleMute = async () => {
    try {
      await toggleMute(callState.callId);
      setCallState(prev => ({ ...prev, isMuted: !prev.isMuted }));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить состояние микрофона');
    }
  };

  const handleToggleSpeaker = async () => {
    try {
      await toggleSpeaker(callState.callId);
      setCallState(prev => ({ ...prev, isSpeakerOn: !prev.isSpeakerOn }));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить режим динамика');
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusText = () => {
    switch (callState.status) {
      case 'connecting':
        return 'Соединение...';
      case 'ringing':
        return callState.isIncoming ? 'Входящий звонок' : 'Звонок...';
      case 'connected':
        return formatDuration(callState.duration);
      case 'ended':
        return 'Звонок завершен';
      case 'failed':
        return 'Не удалось соединиться';
      default:
        return '';
    }
  };

  const getStatusColor = () => {
    switch (callState.status) {
      case 'connecting':
      case 'ringing':
        return '#FF9800';
      case 'connected':
        return '#4CAF50';
      case 'ended':
        return '#666';
      case 'failed':
        return '#F44336';
      default:
        return '#666';
    }
  };

  const renderIncomingCallActions = () => (
    <View style={styles.incomingActions}>
      <TouchableOpacity
        style={[styles.callAction, styles.rejectAction]}
        onPress={handleReject}
      >
        <Icon name="call-end" size={30} color="#FFFFFF" />
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.callAction, styles.answerAction]}
        onPress={handleAnswer}
      >
        <Icon name="call" size={30} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  const renderConnectedActions = () => (
    <View style={styles.connectedActions}>
      <TouchableOpacity
        style={[styles.controlButton, callState.isMuted && styles.activeControlButton]}
        onPress={handleToggleMute}
      >
        <Icon
          name={callState.isMuted ? 'mic-off' : 'mic'}
          size={24}
          color={callState.isMuted ? '#FFFFFF' : '#666'}
        />
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.controlButton, callState.isSpeakerOn && styles.activeControlButton]}
        onPress={handleToggleSpeaker}
      >
        <Icon
          name={callState.isSpeakerOn ? 'volume-up' : 'volume-down'}
          size={24}
          color={callState.isSpeakerOn ? '#FFFFFF' : '#666'}
        />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.controlButton}
        onPress={() => navigation.navigate('ChatScreen', { userId: callState.userId })}
      >
        <Icon name="chat" size={24} color="#666" />
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.callAction, styles.endCallAction]}
        onPress={handleEndCall}
      >
        <Icon name="call-end" size={30} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  const renderDefaultActions = () => (
    <View style={styles.defaultActions}>
      <TouchableOpacity
        style={[styles.callAction, styles.endCallAction]}
        onPress={handleEndCall}
      >
        <Icon name="call-end" size={30} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.minimizeButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="keyboard-arrow-down" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Аудиозвонок</Text>
          <View style={styles.headerRight} />
        </View>

        {/* Основной контент */}
        <View style={styles.content}>
          {/* Аватар пользователя */}
          <View style={styles.avatarContainer}>
            <Animated.View
              style={[
                styles.avatarPulse,
                {
                  transform: [{ scale: pulseAnim }],
                  opacity: callState.status === 'ringing' || callState.status === 'connecting' ? 0.7 : 0,
                },
              ]}
            />
            <Image source={{ uri: callState.userPhoto }} style={styles.avatar} />
            
            {/* Индикатор статуса */}
            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]} />
          </View>

          {/* Информация о пользователе */}
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{callState.userName}</Text>
            <Text style={[styles.statusText, { color: getStatusColor() }]}>
              {getStatusText()}
            </Text>
          </View>

          {/* Индикаторы состояния */}
          <View style={styles.indicators}>
            {callState.isMuted && (
              <View style={styles.indicator}>
                <Icon name="mic-off" size={16} color="#FFFFFF" />
                <Text style={styles.indicatorText}>Микрофон выключен</Text>
              </View>
            )}

            {callState.isSpeakerOn && (
              <View style={styles.indicator}>
                <Icon name="volume-up" size={16} color="#FFFFFF" />
                <Text style={styles.indicatorText}>Громкая связь</Text>
              </View>
            )}
          </View>

          {/* Визуализация звука */}
          {callState.status === 'connected' && (
            <View style={styles.audioVisualization}>
              {[...Array(5)].map((_, index) => (
                <Animated.View
                  key={index}
                  style={[
                    styles.audioBar,
                    {
                      height: Math.random() * 40 + 10,
                      opacity: callState.isMuted ? 0.3 : 1,
                    },
                  ]}
                />
              ))}
            </View>
          )}
        </View>

        {/* Действия */}
        <View style={styles.actionsContainer}>
          {callState.status === 'ringing' && callState.isIncoming
            ? renderIncomingCallActions()
            : callState.status === 'connected'
            ? renderConnectedActions()
            : renderDefaultActions()}
        </View>

        {/* Дополнительная информация */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            {callState.status === 'connecting' && 'Проверьте подключение к интернету'}
            {callState.status === 'ringing' && !callState.isIncoming && 'Ожидание ответа...'}
            {callState.status === 'connected' && 'Звонок активен'}
            {callState.status === 'ended' && 'Возвращение к чату...'}
            {callState.status === 'failed' && 'Попробуйте позвонить позже'}
          </Text>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  minimizeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  avatarContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 40,
  },
  avatarPulse: {
    position: 'absolute',
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  avatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: 30,
  },
  userName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  statusText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  indicators: {
    alignItems: 'center',
    marginBottom: 30,
  },
  indicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginBottom: 8,
  },
  indicatorText: {
    color: '#FFFFFF',
    fontSize: 14,
    marginLeft: 8,
  },
  audioVisualization: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    height: 60,
    marginBottom: 20,
  },
  audioBar: {
    width: 4,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 2,
    borderRadius: 2,
  },
  actionsContainer: {
    paddingHorizontal: 40,
    paddingBottom: 40,
  },
  incomingActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  connectedActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  defaultActions: {
    alignItems: 'center',
  },
  callAction: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  answerAction: {
    backgroundColor: '#4CAF50',
  },
  rejectAction: {
    backgroundColor: '#F44336',
  },
  endCallAction: {
    backgroundColor: '#F44336',
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
    marginVertical: 10,
  },
  activeControlButton: {
    backgroundColor: '#F44336',
  },
  footer: {
    paddingHorizontal: 40,
    paddingBottom: 20,
    alignItems: 'center',
  },
  footerText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
  },
});

export default AudioCallScreen;
