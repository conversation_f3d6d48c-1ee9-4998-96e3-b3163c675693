import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface BlockedUsersScreenProps {
  navigation: any;
}

interface BlockedUser {
  id: string;
  firstName: string;
  lastName: string;
  age: number;
  city: string;
  mainPhoto: string;
  isVerified: boolean;
  isPremium: boolean;
  blockedAt: string;
  reason: 'spam' | 'inappropriate' | 'harassment' | 'fake' | 'other';
  customReason?: string;
  mutualFriends: number;
  commonInterests: string[];
}

const BlockedUsersScreen: React.FC<BlockedUsersScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { getBlockedUsers, unblockUser, blockUser } = useAuth();

  const [blockedUsers, setBlockedUsers] = useState<BlockedUser[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadBlockedUsers();
  }, []);

  const loadBlockedUsers = async (pageNum = 1, refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const result = await getBlockedUsers({
        page: pageNum,
        limit: 20,
      });

      if (result.success) {
        const newBlockedUsers = result.blockedUsers || [];
        
        if (refresh || pageNum === 1) {
          setBlockedUsers(newBlockedUsers);
        } else {
          setBlockedUsers(prev => [...prev, ...newBlockedUsers]);
        }

        setHasMore(newBlockedUsers.length === 20);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('Error loading blocked users:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = useCallback(() => {
    loadBlockedUsers(1, true);
  }, []);

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      loadBlockedUsers(page + 1);
    }
  };

  const handleUserPress = (user: BlockedUser) => {
    Alert.alert(
      'Заблокированный пользователь',
      `${user.firstName} ${user.lastName} заблокирован по причине: ${getReasonText(user.reason)}`,
      [
        { text: 'Отмена', style: 'cancel' },
        { text: 'Разблокировать', onPress: () => handleUnblockUser(user) },
      ]
    );
  };

  const handleUnblockUser = async (user: BlockedUser) => {
    Alert.alert(
      'Разблокировать пользователя',
      `Вы уверены, что хотите разблокировать ${user.firstName} ${user.lastName}?`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Разблокировать',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await unblockUser(user.id);
              if (result.success) {
                // Удаляем пользователя из списка
                setBlockedUsers(prev => prev.filter(u => u.id !== user.id));
                Alert.alert('Успешно', 'Пользователь разблокирован');
              } else {
                Alert.alert('Ошибка', result.message || 'Не удалось разблокировать пользователя');
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Произошла ошибка при разблокировке пользователя');
            }
          },
        },
      ]
    );
  };

  const getReasonText = (reason: string) => {
    switch (reason) {
      case 'spam':
        return 'Спам';
      case 'inappropriate':
        return 'Неподобающее поведение';
      case 'harassment':
        return 'Домогательства';
      case 'fake':
        return 'Фейковый профиль';
      case 'other':
        return 'Другое';
      default:
        return 'Неизвестная причина';
    }
  };

  const formatBlockTime = (blockedAt: string) => {
    const now = new Date();
    const blocked = new Date(blockedAt);
    const diffInDays = Math.floor((now.getTime() - blocked.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays < 1) {
      return 'Сегодня';
    } else if (diffInDays === 1) {
      return 'Вчера';
    } else if (diffInDays < 30) {
      return `${diffInDays} дн. назад`;
    } else {
      const diffInMonths = Math.floor(diffInDays / 30);
      return `${diffInMonths} мес. назад`;
    }
  };

  const renderBlockedUser = ({ item }: { item: BlockedUser }) => (
    <TouchableOpacity
      style={styles.userCard}
      onPress={() => handleUserPress(item)}
    >
      <View style={styles.userImageContainer}>
        <Image source={{ uri: item.mainPhoto }} style={styles.userImage} />
        
        {/* Верификация */}
        {item.isVerified && (
          <View style={styles.verifiedBadge}>
            <Icon name="verified" size={16} color="#4CAF50" />
          </View>
        )}

        {/* Премиум */}
        {item.isPremium && (
          <View style={styles.premiumBadge}>
            <Icon name="star" size={16} color="#FFD700" />
          </View>
        )}

        {/* Иконка блокировки */}
        <View style={styles.blockedOverlay}>
          <Icon name="block" size={24} color="#F44336" />
        </View>
      </View>

      <View style={styles.userInfo}>
        <View style={styles.userHeader}>
          <Text style={styles.userName}>
            {item.firstName} {item.lastName}
          </Text>
          <Text style={styles.blockTime}>
            {formatBlockTime(item.blockedAt)}
          </Text>
        </View>

        <View style={styles.userDetails}>
          <Text style={styles.userAge}>{item.age} лет</Text>
          <Text style={styles.userLocation}>
            <Icon name="location-on" size={12} color="#666" />
            {' '}{item.city}
          </Text>
        </View>

        {/* Причина блокировки */}
        <View style={styles.blockReason}>
          <Icon name="report" size={14} color="#F44336" />
          <Text style={styles.blockReasonText}>
            {getReasonText(item.reason)}
            {item.customReason && `: ${item.customReason}`}
          </Text>
        </View>

        {/* Общие интересы */}
        {item.commonInterests.length > 0 && (
          <View style={styles.commonInterests}>
            <Icon name="favorite" size={12} color="#FF6B9D" />
            <Text style={styles.commonInterestsText}>
              {item.commonInterests.length} общих интереса
            </Text>
          </View>
        )}

        {/* Взаимные друзья */}
        {item.mutualFriends > 0 && (
          <View style={styles.mutualFriends}>
            <Icon name="group" size={12} color="#4ECDC4" />
            <Text style={styles.mutualFriendsText}>
              {item.mutualFriends} общих знакомых
            </Text>
          </View>
        )}
      </View>

      {/* Действия */}
      <View style={styles.userActions}>
        <TouchableOpacity
          style={styles.unblockButton}
          onPress={() => handleUnblockUser(item)}
        >
          <Icon name="lock-open" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="block" size={80} color="rgba(255, 255, 255, 0.5)" />
      <Text style={styles.emptyTitle}>Нет заблокированных пользователей</Text>
      <Text style={styles.emptySubtitle}>
        Здесь будут отображаться пользователи, которых вы заблокировали
      </Text>
      <TouchableOpacity
        style={styles.helpButton}
        onPress={() => navigation.navigate('HelpScreen', { topic: 'blocking' })}
      >
        <Text style={styles.helpButtonText}>Как заблокировать пользователя?</Text>
      </TouchableOpacity>
    </View>
  );

  const renderFooter = () => {
    if (!isLoading || isRefreshing) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator color="#FFFFFF" size="small" />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Заблокированные</Text>
          <TouchableOpacity
            style={styles.helpButton}
            onPress={() => navigation.navigate('HelpScreen', { topic: 'blocking' })}
          >
            <Icon name="help" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Статистика */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{blockedUsers.length}</Text>
            <Text style={styles.statLabel}>Заблокировано</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {blockedUsers.filter(u => u.reason === 'spam').length}
            </Text>
            <Text style={styles.statLabel}>За спам</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {blockedUsers.filter(u => u.reason === 'inappropriate').length}
            </Text>
            <Text style={styles.statLabel}>За поведение</Text>
          </View>
        </View>

        {/* Список заблокированных пользователей */}
        <FlatList
          data={blockedUsers}
          renderItem={renderBlockedUser}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
              colors={['#FFFFFF']}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={!isLoading ? renderEmptyState : null}
        />
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  helpButton: {
    padding: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  listContainer: {
    padding: 20,
  },
  userCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  userImageContainer: {
    position: 'relative',
    marginRight: 15,
  },
  userImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    opacity: 0.7,
  },
  verifiedBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 2,
  },
  premiumBadge: {
    position: 'absolute',
    top: -2,
    left: -2,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 2,
  },
  blockedOverlay: {
    position: 'absolute',
    top: 18,
    left: 18,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(244, 67, 54, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  blockTime: {
    fontSize: 12,
    color: '#F44336',
    fontWeight: '600',
  },
  userDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  userAge: {
    fontSize: 14,
    color: '#666',
    marginRight: 10,
  },
  userLocation: {
    fontSize: 12,
    color: '#666',
    flex: 1,
  },
  blockReason: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  blockReasonText: {
    fontSize: 12,
    color: '#F44336',
    marginLeft: 4,
    fontWeight: '600',
  },
  commonInterests: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  commonInterestsText: {
    fontSize: 12,
    color: '#FF6B9D',
    marginLeft: 4,
  },
  mutualFriends: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  mutualFriendsText: {
    fontSize: 12,
    color: '#4ECDC4',
    marginLeft: 4,
  },
  userActions: {
    alignItems: 'center',
  },
  unblockButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  helpButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    textDecorationLine: 'underline',
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

export default BlockedUsersScreen;
