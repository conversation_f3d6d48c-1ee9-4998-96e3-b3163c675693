/* Стили для страницы аналитики админ-панели */

.container {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title {
  display: flex;
  align-items: center;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.titleIcon {
  margin-right: 12px;
  color: #667eea;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dateControl {
  min-width: 120px;
}

.refreshButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: all 0.3s ease;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.exportButton {
  border-color: #667eea;
  color: #667eea;
  transition: all 0.3s ease;
}

.exportButton:hover {
  background-color: #667eea;
  color: white;
  transform: translateY(-2px);
}

.loader {
  margin-bottom: 24px;
  border-radius: 4px;
}

.errorAlert {
  margin-bottom: 24px;
  border-radius: 12px;
}

/* Метрики */
.metricsGrid {
  margin-bottom: 32px;
}

.metricCard {
  height: 100%;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metricCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.metricCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.metricContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.metricIcon {
  font-size: 48px;
  opacity: 0.7;
  color: #667eea;
}

/* Вкладки */
.tabsCard {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tabsHeader {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.tabs {
  padding: 0 24px;
}

/* Графики */
.chartPaper {
  padding: 24px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.chartPaper:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.chartContainer {
  height: 400px;
  position: relative;
  margin-top: 16px;
}

/* Статистические карточки */
.statCard {
  height: 100%;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
}

.statCard .MuiCardContent-root {
  text-align: center;
}

.statCard .MuiTypography-h4 {
  font-weight: 700;
  margin: 16px 0;
}

/* Производительность */
.performanceStats {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.performanceCard {
  flex: 1;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.performanceCard:hover {
  transform: translateX(4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.performanceCard .MuiCardContent-root {
  text-align: center;
  padding: 16px;
}

.performanceCard .MuiTypography-h4 {
  font-weight: 700;
  margin-top: 8px;
}

/* Адаптивность */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .headerActions {
    flex-direction: column;
    width: 100%;
    gap: 12px;
  }

  .dateControl {
    width: 100%;
  }

  .title {
    font-size: 1.5rem;
  }

  .metricContent {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .metricIcon {
    font-size: 36px;
  }

  .chartContainer {
    height: 300px;
  }

  .performanceStats {
    gap: 12px;
  }

  .performanceCard .MuiCardContent-root {
    padding: 12px;
  }

  .performanceCard .MuiTypography-h6 {
    font-size: 0.9rem;
  }

  .performanceCard .MuiTypography-h4 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px;
  }

  .header {
    padding: 16px;
  }

  .title {
    font-size: 1.25rem;
  }

  .chartContainer {
    height: 250px;
  }

  .tabs {
    padding: 0 12px;
  }

  .chartPaper {
    padding: 16px;
  }
}

/* Анимации */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.metricCard,
.chartPaper,
.statCard,
.performanceCard {
  animation: fadeInUp 0.6s ease-out;
}

.metricCard:nth-child(1) { animation-delay: 0.1s; }
.metricCard:nth-child(2) { animation-delay: 0.2s; }
.metricCard:nth-child(3) { animation-delay: 0.3s; }
.metricCard:nth-child(4) { animation-delay: 0.4s; }

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }

  .header,
  .metricCard,
  .chartPaper,
  .tabsCard,
  .performanceCard {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .title {
    color: #ffffff;
  }

  .exportButton {
    border-color: #667eea;
    color: #667eea;
  }

  .exportButton:hover {
    background-color: #667eea;
    color: white;
  }
}

/* Эффекты при наведении */
.metricCard {
  position: relative;
  overflow: hidden;
}

.metricCard::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  transition: all 0.4s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.metricCard:hover::after {
  width: 200px;
  height: 200px;
}

/* Загрузка */
.loader {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* Кастомные скроллбары */
.chartContainer::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.chartContainer::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.chartContainer::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
}

.chartContainer::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
