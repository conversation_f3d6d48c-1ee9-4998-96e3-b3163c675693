import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Dimensions,
  StatusBar,
  Animated,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import TouchID from 'react-native-touch-id';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface BiometricLoginScreenProps {
  navigation: any;
}

const BiometricLoginScreen: React.FC<BiometricLoginScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { biometricLogin } = useAuth();

  const [isLoading, setIsLoading] = useState(false);
  const [biometricType, setBiometricType] = useState<string>('');
  const [isSupported, setIsSupported] = useState(false);

  // Анимации
  const fadeAnim = new Animated.Value(0);
  const pulseAnim = new Animated.Value(1);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    checkBiometricSupport();
    
    // Анимация появления
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Пульсация иконки
    startPulseAnimation();
  }, []);

  const checkBiometricSupport = async () => {
    try {
      const biometryType = await TouchID.isSupported();
      if (biometryType) {
        setIsSupported(true);
        setBiometricType(biometryType);
      } else {
        setIsSupported(false);
        Alert.alert(
          'Биометрия недоступна',
          'На этом устройстве биометрическая аутентификация не поддерживается',
          [
            {
              text: 'Войти с паролем',
              onPress: () => navigation.navigate('Login'),
            },
          ]
        );
      }
    } catch (error) {
      setIsSupported(false);
      Alert.alert(
        'Ошибка',
        'Не удалось проверить поддержку биометрии',
        [
          {
            text: 'Войти с паролем',
            onPress: () => navigation.navigate('Login'),
          },
        ]
      );
    }
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const handleBiometricAuth = async () => {
    if (!isSupported) {
      Alert.alert('Ошибка', 'Биометрическая аутентификация недоступна');
      return;
    }

    setIsLoading(true);

    const optionalConfigObject = {
      title: 'Биометрическая аутентификация',
      subtitle: 'Войдите в Likes Love',
      description: 'Используйте биометрию для быстрого входа',
      fallbackLabel: 'Использовать пароль',
      cancelText: 'Отмена',
      color: '#FF6B9D',
      sensorDescription: 'Приложите палец к сканеру',
      sensorErrorDescription: 'Не удалось распознать отпечаток',
      unifiedErrors: false,
      passcodeFallback: false,
    };

    try {
      const biometricResult = await TouchID.authenticate(
        'Войдите с помощью биометрии',
        optionalConfigObject
      );

      if (biometricResult) {
        // Выполняем биометрический вход через API
        const result = await biometricLogin();

        if (result.success) {
          Alert.alert(
            'Успешно!',
            'Добро пожаловать в Likes Love!',
            [
              {
                text: 'Продолжить',
                onPress: () => navigation.navigate('MainApp'),
              },
            ]
          );
        } else {
          Alert.alert(
            'Ошибка',
            result.message || 'Не удалось войти с помощью биометрии'
          );
        }
      }
    } catch (error: any) {
      if (error.name === 'LAErrorUserCancel') {
        // Пользователь отменил аутентификацию
        return;
      } else if (error.name === 'LAErrorUserFallback') {
        // Пользователь выбрал fallback (пароль)
        navigation.navigate('Login');
        return;
      } else if (error.name === 'LAErrorSystemCancel') {
        // Система отменила аутентификацию
        Alert.alert('Ошибка', 'Аутентификация была прервана системой');
      } else if (error.name === 'LAErrorTouchIDNotAvailable') {
        Alert.alert('Ошибка', 'Биометрическая аутентификация недоступна');
      } else if (error.name === 'LAErrorTouchIDNotEnrolled') {
        Alert.alert(
          'Биометрия не настроена',
          'Настройте биометрическую аутентификацию в настройках устройства',
          [
            {
              text: 'Войти с паролем',
              onPress: () => navigation.navigate('Login'),
            },
          ]
        );
      } else {
        Alert.alert('Ошибка', 'Произошла ошибка при биометрической аутентификации');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const getBiometricIcon = () => {
    switch (biometricType) {
      case 'FaceID':
        return 'face';
      case 'TouchID':
      case 'Fingerprint':
        return 'fingerprint';
      default:
        return 'security';
    }
  };

  const getBiometricTitle = () => {
    switch (biometricType) {
      case 'FaceID':
        return 'Face ID';
      case 'TouchID':
        return 'Touch ID';
      case 'Fingerprint':
        return 'Отпечаток пальца';
      default:
        return 'Биометрия';
    }
  };

  const getBiometricDescription = () => {
    switch (biometricType) {
      case 'FaceID':
        return 'Посмотрите на камеру для входа';
      case 'TouchID':
        return 'Приложите палец к кнопке Home';
      case 'Fingerprint':
        return 'Приложите палец к сканеру';
      default:
        return 'Используйте биометрию для входа';
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Логотип */}
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <Icon name="favorite" size={40} color="#FFFFFF" />
            </View>
            <Text style={styles.logoText}>Likes Love</Text>
          </View>

          {/* Биометрическая иконка */}
          <Animated.View
            style={[
              styles.biometricContainer,
              {
                transform: [{ scale: pulseAnim }],
              },
            ]}
          >
            <TouchableOpacity
              style={styles.biometricButton}
              onPress={handleBiometricAuth}
              disabled={isLoading || !isSupported}
            >
              {isLoading ? (
                <ActivityIndicator color="#FFFFFF" size="large" />
              ) : (
                <Icon name={getBiometricIcon()} size={80} color="#FFFFFF" />
              )}
            </TouchableOpacity>
          </Animated.View>

          {/* Заголовок и описание */}
          <Text style={styles.title}>{getBiometricTitle()}</Text>
          <Text style={styles.description}>{getBiometricDescription()}</Text>

          {/* Кнопка входа с биометрией */}
          <TouchableOpacity
            style={styles.authButton}
            onPress={handleBiometricAuth}
            disabled={isLoading || !isSupported}
          >
            <View style={styles.buttonContent}>
              {isLoading ? (
                <ActivityIndicator color="#FF6B9D" size="small" />
              ) : (
                <Text style={styles.authButtonText}>
                  Войти с {getBiometricTitle()}
                </Text>
              )}
            </View>
          </TouchableOpacity>

          {/* Альтернативные варианты входа */}
          <View style={styles.alternativeContainer}>
            <TouchableOpacity
              style={styles.alternativeButton}
              onPress={() => navigation.navigate('Login')}
            >
              <Text style={styles.alternativeText}>Войти с паролем</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.alternativeButton}
              onPress={() => navigation.navigate('Register')}
            >
              <Text style={styles.alternativeText}>Создать аккаунт</Text>
            </TouchableOpacity>
          </View>

          {/* Информация о безопасности */}
          <View style={styles.securityInfo}>
            <Icon name="security" size={16} color="rgba(255, 255, 255, 0.7)" />
            <Text style={styles.securityText}>
              Ваши биометрические данные хранятся только на устройстве
            </Text>
          </View>
        </Animated.View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  biometricContainer: {
    marginBottom: 40,
  },
  biometricButton: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.4)',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  authButton: {
    width: '100%',
    marginBottom: 30,
  },
  buttonContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  authButtonText: {
    color: '#FF6B9D',
    fontSize: 18,
    fontWeight: 'bold',
  },
  alternativeContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  alternativeButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginVertical: 5,
  },
  alternativeText: {
    color: '#FFFFFF',
    fontSize: 16,
    textDecorationLine: 'underline',
  },
  securityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    maxWidth: '90%',
  },
  securityText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginLeft: 8,
    textAlign: 'center',
    flex: 1,
    lineHeight: 16,
  },
});

export default BiometricLoginScreen;
