import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { placesService } from '../../services/placesService';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../hooks/useLocation';
import { Place, PlaceFilters } from '../../types/places.types';
import { colors, typography, spacing } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface CoffeeShopsScreenProps {}

const CoffeeShopsScreen: React.FC<CoffeeShopsScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { location, requestLocation } = useLocation();
  const queryClient = useQueryClient();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [filters, setFilters] = useState<PlaceFilters>({
    category: 'coffee',
    rating: undefined,
    distance: 10,
    priceRange: undefined,
    features: [],
  });

  // Загрузка кофеен
  const {
    data: coffeeShops,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['places', 'coffee', searchQuery, filters, location],
    queryFn: () => placesService.getCoffeeShops(searchQuery, filters, location),
    enabled: !!location,
  });

  // Мутация для добавления в избранное
  const toggleFavoriteMutation = useMutation({
    mutationFn: (placeId: string) => placesService.toggleFavorite(placeId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['places'] });
      queryClient.invalidateQueries({ queryKey: ['favorites'] });
    },
  });

  // Запрос геолокации при загрузке
  useEffect(() => {
    if (!location) {
      requestLocation();
    }
  }, [location, requestLocation]);

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      if (location) {
        refetch();
      }
    }, [location, refetch])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleToggleFavorite = (placeId: string) => {
    toggleFavoriteMutation.mutate(placeId);
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}м`;
    }
    return `${distance.toFixed(1)}км`;
  };

  const formatPrice = (priceRange: number) => {
    return '₽'.repeat(priceRange);
  };

  const isOpenNow = (hours: any) => {
    if (!hours) return false;
    
    const now = new Date();
    const today = now.getDay();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const todayHours = hours.schedule[today];
    if (!todayHours || todayHours.closed) return false;
    
    const openTime = parseInt(todayHours.open.split(':')[0]) * 60 + parseInt(todayHours.open.split(':')[1]);
    const closeTime = parseInt(todayHours.close.split(':')[0]) * 60 + parseInt(todayHours.close.split(':')[1]);
    
    return currentTime >= openTime && currentTime <= closeTime;
  };

  const filterOptions = [
    { id: 'all', label: 'Все', icon: 'grid-outline' },
    { id: 'open', label: 'Открыто', icon: 'time-outline' },
    { id: 'nearby', label: 'Рядом', icon: 'location-outline' },
    { id: 'rating', label: 'Высокий рейтинг', icon: 'star-outline' },
    { id: 'cheap', label: 'Недорого', icon: 'card-outline' },
  ];

  const renderFilterButton = (filter: typeof filterOptions[0]) => (
    <TouchableOpacity
      key={filter.id}
      style={[
        styles.filterButton,
        selectedFilter === filter.id && styles.filterButtonActive,
      ]}
      onPress={() => setSelectedFilter(filter.id)}
    >
      <Ionicons
        name={filter.icon as any}
        size={18}
        color={selectedFilter === filter.id ? colors.white : colors.text}
      />
      <Text
        style={[
          styles.filterButtonText,
          selectedFilter === filter.id && styles.filterButtonTextActive,
        ]}
      >
        {filter.label}
      </Text>
    </TouchableOpacity>
  );

  const renderCoffeeShopCard = ({ item, index }: { item: Place; index: number }) => {
    const animatedValue = useSharedValue(0);

    useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateY: interpolate(animatedValue.value, [0, 1], [30, 0]),
          },
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.95, 1]),
          },
        ],
      };
    });

    const isOpen = isOpenNow(item.hours);

    return (
      <Animated.View style={[styles.coffeeShopCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('PlaceDetail', { placeId: item.id })}
          activeOpacity={0.8}
        >
          <Image source={{ uri: item.photos[0] }} style={styles.coffeeShopImage} />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.coffeeShopImageGradient}
          />
          
          {/* Кнопка избранного */}
          <TouchableOpacity
            style={styles.favoriteButton}
            onPress={() => handleToggleFavorite(item.id)}
            disabled={toggleFavoriteMutation.isPending}
          >
            <Ionicons
              name={item.isFavorite ? 'heart' : 'heart-outline'}
              size={24}
              color={item.isFavorite ? colors.error : colors.white}
            />
          </TouchableOpacity>
          
          {/* Статус работы */}
          <View style={[styles.statusBadge, { backgroundColor: isOpen ? colors.success : colors.error }]}>
            <Text style={styles.statusText}>{isOpen ? 'Открыто' : 'Закрыто'}</Text>
          </View>
          
          {/* Рейтинг */}
          <View style={styles.ratingBadge}>
            <Ionicons name="star" size={14} color={colors.warning} />
            <Text style={styles.ratingText}>{item.rating}</Text>
          </View>
          
          <View style={styles.coffeeShopInfo}>
            <Text style={styles.coffeeShopName}>{item.name}</Text>
            <Text style={styles.coffeeShopAddress}>{item.address}</Text>
            
            <View style={styles.coffeeShopDetails}>
              <Text style={styles.coffeeShopDistance}>
                <Ionicons name="location-outline" size={14} color={colors.textSecondary} />
                {' '}{formatDistance(item.distance)}
              </Text>
              
              {item.priceRange && (
                <Text style={styles.priceRange}>
                  {formatPrice(item.priceRange)}
                </Text>
              )}
            </View>
            
            {item.features.length > 0 && (
              <View style={styles.featuresContainer}>
                {item.features.slice(0, 3).map((feature, idx) => (
                  <View key={idx} style={styles.featureTag}>
                    <Text style={styles.featureText}>{feature}</Text>
                  </View>
                ))}
                {item.features.length > 3 && (
                  <Text style={styles.moreFeatures}>+{item.features.length - 3}</Text>
                )}
              </View>
            )}
          </View>
        </TouchableOpacity>
        
        <View style={styles.coffeeShopActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('PlaceDetail', { placeId: item.id })}
          >
            <Ionicons name="information-circle-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Подробнее</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('ARNavigation', { placeId: item.id })}
          >
            <Ionicons name="navigate-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Маршрут</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('CreateMeeting', { placeId: item.id })}
          >
            <Ionicons name="calendar-outline" size={20} color={colors.primary} />
            <Text style={styles.actionButtonText}>Встреча</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  if (isLoading && !coffeeShops) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Ищем лучшие кофейни...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="cafe-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить список кофеен
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Кофейни</Text>
        
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('PlaceFilters', { category: 'coffee' })}
        >
          <Ionicons name="options-outline" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Поиск */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search-outline" size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск кофеен..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Фильтры */}
      <View style={styles.filtersContainer}>
        <FlatList
          data={filterOptions}
          renderItem={({ item }) => renderFilterButton(item)}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersList}
        />
      </View>

      {/* Список кофеен */}
      <FlatList
        data={coffeeShops}
        renderItem={renderCoffeeShopCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="cafe-outline" size={64} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>Кофейни не найдены</Text>
            <Text style={styles.emptyText}>
              Попробуйте изменить фильтры или поисковый запрос
            </Text>
          </View>
        }
        ListHeaderComponent={
          coffeeShops && coffeeShops.length > 0 ? (
            <View style={styles.listHeader}>
              <Text style={styles.resultsCount}>
                Найдено {coffeeShops.length} {coffeeShops.length === 1 ? 'кофейня' : 'кофеен'}
              </Text>
            </View>
          ) : null
        }
      />

      {/* Плавающая кнопка карты */}
      <TouchableOpacity
        style={styles.mapButton}
        onPress={() => navigation.navigate('CoffeeShopsMap')}
      >
        <Ionicons name="map" size={24} color={colors.white} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    gap: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: colors.text,
  },
  filtersContainer: {
    paddingVertical: spacing.sm,
  },
  filtersList: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    gap: spacing.xs,
  },
  filterButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  filterButtonTextActive: {
    color: colors.white,
  },
  listContainer: {
    padding: spacing.md,
  },
  listHeader: {
    paddingHorizontal: spacing.sm,
    paddingBottom: spacing.md,
  },
  resultsCount: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium,
  },
  coffeeShopCard: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    marginBottom: spacing.md,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  coffeeShopImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  coffeeShopImageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  favoriteButton: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  ratingBadge: {
    position: 'absolute',
    top: spacing.md + 40,
    left: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  ratingText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  coffeeShopInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.md,
  },
  coffeeShopName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  coffeeShopAddress: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  coffeeShopDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  coffeeShopDistance: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  priceRange: {
    fontSize: typography.sizes.sm,
    color: colors.success,
    fontWeight: typography.weights.bold,
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  featureTag: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  featureText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  moreFeatures: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    fontWeight: typography.weights.medium,
  },
  coffeeShopActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.surface,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    backgroundColor: colors.background,
    gap: spacing.xs,
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.primary,
  },
  mapButton: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default CoffeeShopsScreen;
