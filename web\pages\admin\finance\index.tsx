import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Tabs,
  Tab,
  Divider,
} from '@mui/material';
import {
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  CreditCard as CardIcon,
  Receipt as ReceiptIcon,
  Refresh as RefreshIcon,
  Download as ExportIcon,
  Visibility as ViewIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Warning as WarningIcon,
  AccountBalance as BankIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { AdminLayout } from '../../../components/admin/AdminLayout';
import { useAuth } from '../../../hooks/useAuth';
import { adminService } from '../../../services/adminService';
import styles from '../../../styles/admin/Finance.module.css';

interface Transaction {
  id: string;
  userId: string;
  userName: string;
  type: 'subscription' | 'boost' | 'gift' | 'refund';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: string;
  subscriptionType?: 'basic' | 'premium' | 'vip';
  createdAt: string;
  completedAt?: string;
  description: string;
}

interface FinanceStats {
  totalRevenue: number;
  monthlyRevenue: number;
  dailyRevenue: number;
  pendingAmount: number;
  refundedAmount: number;
  subscriptionRevenue: number;
  boostRevenue: number;
  giftRevenue: number;
  revenueGrowth: number;
  transactionCount: number;
  averageTransactionValue: number;
  topPaymentMethods: Array<{ method: string; amount: number; count: number }>;
  revenueChart: Array<{ date: string; amount: number }>;
  subscriptionChart: Array<{ type: string; count: number; revenue: number }>;
}

const FinancePage: React.FC = () => {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const queryClient = useQueryClient();

  const [activeTab, setActiveTab] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(20);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [dateRange, setDateRange] = useState<{ from: string; to: string }>({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    to: new Date().toISOString().split('T')[0],
  });

  // Проверка прав доступа
  useEffect(() => {
    if (!isAdmin) {
      router.push('/');
    }
  }, [isAdmin, router]);

  // Загрузка финансовой статистики
  const {
    data: financeStats,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useQuery<FinanceStats>({
    queryKey: ['admin', 'finance', 'stats', dateRange],
    queryFn: async () => {
      // Здесь будет вызов API для получения финансовой статистики
      // Пока используем моковые данные
      return {
        totalRevenue: 2450000,
        monthlyRevenue: 185000,
        dailyRevenue: 6200,
        pendingAmount: 12500,
        refundedAmount: 8900,
        subscriptionRevenue: 1980000,
        boostRevenue: 320000,
        giftRevenue: 150000,
        revenueGrowth: 12.5,
        transactionCount: 15420,
        averageTransactionValue: 159,
        topPaymentMethods: [
          { method: 'Карта', amount: 1850000, count: 12500 },
          { method: 'Apple Pay', amount: 380000, count: 2100 },
          { method: 'Google Pay', amount: 220000, count: 820 },
        ],
        revenueChart: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          amount: Math.floor(Math.random() * 10000) + 5000,
        })),
        subscriptionChart: [
          { type: 'Premium', count: 8500, revenue: 1275000 },
          { type: 'VIP', count: 2100, revenue: 705000 },
          { type: 'Basic', count: 0, revenue: 0 },
        ],
      };
    },
    enabled: isAdmin,
  });

  // Загрузка транзакций
  const {
    data: transactionsData,
    isLoading: transactionsLoading,
    error: transactionsError,
  } = useQuery<{ transactions: Transaction[]; total: number }>({
    queryKey: ['admin', 'finance', 'transactions', page, rowsPerPage, statusFilter, typeFilter, dateRange],
    queryFn: async () => {
      // Здесь будет вызов API для получения транзакций
      // Пока используем моковые данные
      const mockTransactions: Transaction[] = Array.from({ length: 100 }, (_, i) => ({
        id: `txn_${i + 1}`,
        userId: `user_${Math.floor(Math.random() * 1000)}`,
        userName: `Пользователь ${i + 1}`,
        type: ['subscription', 'boost', 'gift', 'refund'][Math.floor(Math.random() * 4)] as any,
        amount: Math.floor(Math.random() * 500) + 50,
        currency: 'RUB',
        status: ['pending', 'completed', 'failed', 'refunded'][Math.floor(Math.random() * 4)] as any,
        paymentMethod: ['Карта', 'Apple Pay', 'Google Pay'][Math.floor(Math.random() * 3)],
        subscriptionType: Math.random() > 0.5 ? ['premium', 'vip'][Math.floor(Math.random() * 2)] as any : undefined,
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        completedAt: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString() : undefined,
        description: `Транзакция ${i + 1}`,
      }));

      return {
        transactions: mockTransactions.slice(page * rowsPerPage, (page + 1) * rowsPerPage),
        total: mockTransactions.length,
      };
    },
    enabled: isAdmin,
  });

  const formatCurrency = (amount: number, currency: string = 'RUB') => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getStatusChip = (status: Transaction['status']) => {
    const statusConfig = {
      pending: { label: 'Ожидает', color: 'warning' as const, icon: <WarningIcon /> },
      completed: { label: 'Завершена', color: 'success' as const, icon: <CheckCircle /> },
      failed: { label: 'Ошибка', color: 'error' as const, icon: <Cancel /> },
      refunded: { label: 'Возврат', color: 'info' as const, icon: <RefreshIcon /> },
    };

    const config = statusConfig[status];
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        icon={config.icon}
      />
    );
  };

  const getTypeLabel = (type: Transaction['type']) => {
    const typeLabels = {
      subscription: 'Подписка',
      boost: 'Буст',
      gift: 'Подарок',
      refund: 'Возврат',
    };
    return typeLabels[type];
  };

  const revenueChartData = {
    labels: financeStats?.revenueChart?.map(item => new Date(item.date).toLocaleDateString()) || [],
    datasets: [
      {
        label: 'Доход',
        data: financeStats?.revenueChart?.map(item => item.amount) || [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1,
      },
    ],
  };

  const subscriptionChartData = {
    labels: financeStats?.subscriptionChart?.map(item => item.type) || [],
    datasets: [
      {
        data: financeStats?.subscriptionChart?.map(item => item.revenue) || [],
        backgroundColor: [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 205, 86, 0.8)',
        ],
      },
    ],
  };

  const paymentMethodsData = {
    labels: financeStats?.topPaymentMethods?.map(item => item.method) || [],
    datasets: [
      {
        label: 'Сумма',
        data: financeStats?.topPaymentMethods?.map(item => item.amount) || [],
        backgroundColor: 'rgba(153, 102, 255, 0.8)',
        borderColor: 'rgba(153, 102, 255, 1)',
        borderWidth: 1,
      },
    ],
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Финансы - Админ-панель</title>
        <meta name="description" content="Финансовая статистика и управление транзакциями" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <AdminLayout>
        <Box className={styles.container}>
          {/* Заголовок */}
          <Box className={styles.header}>
            <Box>
              <Typography variant="h4" component="h1" className={styles.title}>
                <MoneyIcon className={styles.titleIcon} />
                Финансы и подписки
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Период: {new Date(dateRange.from).toLocaleDateString()} - {new Date(dateRange.to).toLocaleDateString()}
              </Typography>
            </Box>
            <Box className={styles.headerActions}>
              <Button
                variant="outlined"
                startIcon={<ExportIcon />}
                className={styles.actionButton}
              >
                Экспорт
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => refetchStats()}
                className={styles.actionButton}
              >
                Обновить
              </Button>
            </Box>
          </Box>

          {(statsLoading || transactionsLoading) && <LinearProgress className={styles.loader} />}

          {(statsError || transactionsError) && (
            <Alert severity="error" className={styles.errorAlert}>
              Ошибка загрузки данных: {statsError?.message || transactionsError?.message}
            </Alert>
          )}

          {/* Основные метрики */}
          <Grid container spacing={3} className={styles.metricsGrid}>
            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.metricCard}>
                <CardContent>
                  <Box className={styles.metricContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Общий доход
                      </Typography>
                      <Typography variant="h4">
                        {formatCurrency(financeStats?.totalRevenue || 0)}
                      </Typography>
                      <Typography variant="body2" color="success.main">
                        +{financeStats?.revenueGrowth || 0}% к прошлому месяцу
                      </Typography>
                    </Box>
                    <MoneyIcon className={styles.metricIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.metricCard}>
                <CardContent>
                  <Box className={styles.metricContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Месячный доход
                      </Typography>
                      <Typography variant="h4">
                        {formatCurrency(financeStats?.monthlyRevenue || 0)}
                      </Typography>
                      <Typography variant="body2" color="info.main">
                        Средний чек: {formatCurrency(financeStats?.averageTransactionValue || 0)}
                      </Typography>
                    </Box>
                    <TrendingUpIcon className={styles.metricIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.metricCard}>
                <CardContent>
                  <Box className={styles.metricContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Транзакции
                      </Typography>
                      <Typography variant="h4">
                        {formatNumber(financeStats?.transactionCount || 0)}
                      </Typography>
                      <Typography variant="body2" color="warning.main">
                        Ожидают: {formatCurrency(financeStats?.pendingAmount || 0)}
                      </Typography>
                    </Box>
                    <ReceiptIcon className={styles.metricIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card className={styles.metricCard}>
                <CardContent>
                  <Box className={styles.metricContent}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Возвраты
                      </Typography>
                      <Typography variant="h4">
                        {formatCurrency(financeStats?.refundedAmount || 0)}
                      </Typography>
                      <Typography variant="body2" color="error.main">
                        {((financeStats?.refundedAmount || 0) / (financeStats?.totalRevenue || 1) * 100).toFixed(1)}% от общего дохода
                      </Typography>
                    </Box>
                    <BankIcon className={styles.metricIcon} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Вкладки */}
          <Card className={styles.tabsCard}>
            <Box className={styles.tabsHeader}>
              <Tabs
                value={activeTab}
                onChange={(_, newValue) => setActiveTab(newValue)}
                className={styles.tabs}
              >
                <Tab label="Обзор" />
                <Tab label="Транзакции" />
                <Tab label="Подписки" />
                <Tab label="Платежные методы" />
              </Tabs>
            </Box>

            <CardContent>
              {/* Вкладка обзора */}
              {activeTab === 0 && (
                <Grid container spacing={3}>
                  <Grid item xs={12} lg={8}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Динамика доходов
                      </Typography>
                      <Box className={styles.chartContainer}>
                        <Line data={revenueChartData} options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'top' as const,
                            },
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              ticks: {
                                callback: function(value) {
                                  return formatCurrency(Number(value));
                                },
                              },
                            },
                          },
                        }} />
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} lg={4}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Доходы по типам
                      </Typography>
                      <Box className={styles.statsContainer}>
                        <Box className={styles.statItem}>
                          <Typography variant="body2" color="text.secondary">
                            Подписки
                          </Typography>
                          <Typography variant="h6" color="primary">
                            {formatCurrency(financeStats?.subscriptionRevenue || 0)}
                          </Typography>
                        </Box>
                        <Box className={styles.statItem}>
                          <Typography variant="body2" color="text.secondary">
                            Бусты
                          </Typography>
                          <Typography variant="h6" color="secondary">
                            {formatCurrency(financeStats?.boostRevenue || 0)}
                          </Typography>
                        </Box>
                        <Box className={styles.statItem}>
                          <Typography variant="body2" color="text.secondary">
                            Подарки
                          </Typography>
                          <Typography variant="h6" color="success.main">
                            {formatCurrency(financeStats?.giftRevenue || 0)}
                          </Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              )}

              {/* Вкладка транзакций */}
              {activeTab === 1 && (
                <Box>
                  {/* Фильтры транзакций */}
                  <Grid container spacing={2} className={styles.filtersGrid}>
                    <Grid item xs={12} md={3}>
                      <FormControl fullWidth>
                        <InputLabel>Статус</InputLabel>
                        <Select
                          value={statusFilter}
                          onChange={(e) => setStatusFilter(e.target.value)}
                        >
                          <MenuItem value="">Все</MenuItem>
                          <MenuItem value="pending">Ожидает</MenuItem>
                          <MenuItem value="completed">Завершена</MenuItem>
                          <MenuItem value="failed">Ошибка</MenuItem>
                          <MenuItem value="refunded">Возврат</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormControl fullWidth>
                        <InputLabel>Тип</InputLabel>
                        <Select
                          value={typeFilter}
                          onChange={(e) => setTypeFilter(e.target.value)}
                        >
                          <MenuItem value="">Все</MenuItem>
                          <MenuItem value="subscription">Подписка</MenuItem>
                          <MenuItem value="boost">Буст</MenuItem>
                          <MenuItem value="gift">Подарок</MenuItem>
                          <MenuItem value="refund">Возврат</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>

                  {/* Таблица транзакций */}
                  <TableContainer component={Paper} className={styles.tableContainer}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>ID</TableCell>
                          <TableCell>Пользователь</TableCell>
                          <TableCell>Тип</TableCell>
                          <TableCell>Сумма</TableCell>
                          <TableCell>Статус</TableCell>
                          <TableCell>Метод оплаты</TableCell>
                          <TableCell>Дата</TableCell>
                          <TableCell>Действия</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {transactionsData?.transactions.map((transaction) => (
                          <TableRow key={transaction.id} className={styles.tableRow}>
                            <TableCell>
                              <Typography variant="body2" fontFamily="monospace">
                                {transaction.id}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" fontWeight="bold">
                                {transaction.userName}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {transaction.userId}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={getTypeLabel(transaction.type)}
                                size="small"
                                variant="outlined"
                              />
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" fontWeight="bold">
                                {formatCurrency(transaction.amount, transaction.currency)}
                              </Typography>
                            </TableCell>
                            <TableCell>{getStatusChip(transaction.status)}</TableCell>
                            <TableCell>{transaction.paymentMethod}</TableCell>
                            <TableCell>
                              {new Date(transaction.createdAt).toLocaleDateString()}
                            </TableCell>
                            <TableCell>
                              <Tooltip title="Просмотр">
                                <IconButton size="small">
                                  <ViewIcon />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {transactionsData && (
                    <TablePagination
                      component="div"
                      count={transactionsData.total}
                      page={page}
                      onPageChange={(_, newPage) => setPage(newPage)}
                      rowsPerPage={rowsPerPage}
                      onRowsPerPageChange={(e) => {
                        setRowsPerPage(parseInt(e.target.value, 10));
                        setPage(0);
                      }}
                      rowsPerPageOptions={[10, 20, 50]}
                      labelRowsPerPage="Транзакций на странице:"
                      labelDisplayedRows={({ from, to, count }) => 
                        `${from}-${to} из ${count !== -1 ? count : `более ${to}`}`
                      }
                    />
                  )}
                </Box>
              )}

              {/* Вкладка подписок */}
              {activeTab === 2 && (
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Доходы по подпискам
                      </Typography>
                      <Box className={styles.chartContainer}>
                        <Doughnut data={subscriptionChartData} options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom' as const,
                            },
                          },
                        }} />
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Статистика подписок
                      </Typography>
                      <Box className={styles.subscriptionStats}>
                        {financeStats?.subscriptionChart?.map((sub, index) => (
                          <Box key={index} className={styles.subscriptionItem}>
                            <Typography variant="h6" color="primary">
                              {sub.type}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Пользователей: {formatNumber(sub.count)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Доход: {formatCurrency(sub.revenue)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              ARPU: {formatCurrency(sub.count > 0 ? sub.revenue / sub.count : 0)}
                            </Typography>
                          </Box>
                        ))}
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              )}

              {/* Вкладка платежных методов */}
              {activeTab === 3 && (
                <Grid container spacing={3}>
                  <Grid item xs={12} md={8}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Популярные методы оплаты
                      </Typography>
                      <Box className={styles.chartContainer}>
                        <Bar data={paymentMethodsData} options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false,
                            },
                          },
                          scales: {
                            y: {
                              beginAtZero: true,
                              ticks: {
                                callback: function(value) {
                                  return formatCurrency(Number(value));
                                },
                              },
                            },
                          },
                        }} />
                      </Box>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Paper className={styles.chartPaper}>
                      <Typography variant="h6" gutterBottom>
                        Детали по методам
                      </Typography>
                      <Box className={styles.paymentMethodsList}>
                        {financeStats?.topPaymentMethods?.map((method, index) => (
                          <Box key={index} className={styles.paymentMethodItem}>
                            <Typography variant="subtitle1" fontWeight="bold">
                              {method.method}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Сумма: {formatCurrency(method.amount)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Транзакций: {formatNumber(method.count)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Средний чек: {formatCurrency(method.amount / method.count)}
                            </Typography>
                          </Box>
                        ))}
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        </Box>
      </AdminLayout>
    </>
  );
};

export default FinancePage;
