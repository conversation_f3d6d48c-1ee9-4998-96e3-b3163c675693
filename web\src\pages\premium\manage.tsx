import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Divider,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Star,
  CheckCircle,
  Cancel,
  Refresh,
  Download,
  CreditCard,
  CalendarToday,
  TrendingUp,
  Settings,
  Warning,
  Info,
  Close,
  Receipt,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { premiumService } from '../../services/premiumService';
import { Subscription, Invoice, UsageStats } from '../../types/premium.types';
import { formatDate, formatDateTime } from '../../utils/dateUtils';
import { formatCurrency } from '../../utils/formatUtils';
import styles from './manage.module.css';

interface PremiumManageProps {}

const PremiumManage: React.FC<PremiumManageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [invoiceDialogOpen, setInvoiceDialogOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);

  // Загрузка информации о подписке
  const {
    data: subscription,
    isLoading: subscriptionLoading,
    error: subscriptionError,
  } = useQuery({
    queryKey: ['premium', 'subscription'],
    queryFn: () => premiumService.getSubscription(),
    enabled: isAuthenticated,
  });

  // Загрузка статистики использования
  const {
    data: usageStats,
    isLoading: usageLoading,
  } = useQuery({
    queryKey: ['premium', 'usage'],
    queryFn: () => premiumService.getUsageStats(),
    enabled: isAuthenticated && subscription?.isActive,
  });

  // Загрузка истории платежей
  const {
    data: invoices,
    isLoading: invoicesLoading,
  } = useQuery({
    queryKey: ['premium', 'invoices'],
    queryFn: () => premiumService.getInvoices(),
    enabled: isAuthenticated,
  });

  // Мутация для отмены подписки
  const cancelSubscriptionMutation = useMutation({
    mutationFn: () => premiumService.cancelSubscription(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['premium', 'subscription'] });
      setCancelDialogOpen(false);
    },
  });

  // Мутация для возобновления подписки
  const resumeSubscriptionMutation = useMutation({
    mutationFn: () => premiumService.resumeSubscription(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['premium', 'subscription'] });
    },
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/premium/manage');
    }
  }, [isAuthenticated, router]);

  const handleCancelSubscription = () => {
    cancelSubscriptionMutation.mutate();
  };

  const handleResumeSubscription = () => {
    resumeSubscriptionMutation.mutate();
  };

  const handleViewInvoice = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setInvoiceDialogOpen(true);
  };

  const handleDownloadInvoice = (invoiceId: string) => {
    premiumService.downloadInvoice(invoiceId);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'cancelled': return 'error';
      case 'expired': return 'warning';
      case 'pending': return 'info';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active': return 'Активна';
      case 'cancelled': return 'Отменена';
      case 'expired': return 'Истекла';
      case 'pending': return 'Ожидает';
      default: return status;
    }
  };

  const getDaysUntilRenewal = (renewalDate: string) => {
    const now = new Date();
    const renewal = new Date(renewalDate);
    const diffTime = renewal.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (!isAuthenticated) {
    return null;
  }

  if (!subscription && !subscriptionLoading) {
    return (
      <Container maxWidth="lg" className={styles.container}>
        <Alert severity="info" className={styles.noSubscriptionAlert}>
          <Typography variant="h6" gutterBottom>
            У вас нет активной Premium подписки
          </Typography>
          <Typography variant="body2" gutterBottom>
            Получите Premium подписку, чтобы открыть все возможности приложения.
          </Typography>
          <Button
            variant="contained"
            onClick={() => router.push('/premium/purchase')}
            startIcon={<Star />}
            sx={{ mt: 2 }}
          >
            Получить Premium
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <>
      <Head>
        <title>Управление подпиской - Likes & Love</title>
        <meta name="description" content="Управляйте своей Premium подпиской, просматривайте статистику использования и историю платежей." />
        <meta name="keywords" content="premium, подписка, управление, статистика" />
        <meta property="og:title" content="Управление подпиской - Likes & Love" />
        <meta property="og:description" content="Управление Premium подпиской" />
        <meta property="og:type" content="website" />
      </Head>

      <Container maxWidth="lg" className={styles.container}>
        {/* Заголовок */}
        <Box className={styles.header}>
          <Typography variant="h3" component="h1" className={styles.title}>
            Управление подпиской
          </Typography>
          <Typography variant="h6" color="text.secondary" className={styles.subtitle}>
            Информация о вашей Premium подписке
          </Typography>
        </Box>

        {subscriptionLoading ? (
          <Box className={styles.loadingContainer}>
            <LinearProgress />
            <Typography>Загрузка информации о подписке...</Typography>
          </Box>
        ) : subscriptionError ? (
          <Alert severity="error" className={styles.errorAlert}>
            Ошибка загрузки информации о подписке
          </Alert>
        ) : subscription ? (
          <>
            {/* Информация о подписке */}
            <Card className={styles.subscriptionCard}>
              <CardContent>
                <Grid container spacing={3} alignItems="center">
                  <Grid item xs={12} md={8}>
                    <Box className={styles.subscriptionInfo}>
                      <Typography variant="h4" className={styles.planName}>
                        {subscription.plan.name}
                        <Chip
                          label={getStatusLabel(subscription.status)}
                          color={getStatusColor(subscription.status) as any}
                          className={styles.statusChip}
                        />
                      </Typography>
                      
                      <Typography variant="h5" className={styles.planPrice}>
                        {formatCurrency(subscription.plan.price)}
                        <Typography component="span" variant="body1" color="text.secondary">
                          /{subscription.billingCycle === 'annual' ? 'год' : 'месяц'}
                        </Typography>
                      </Typography>

                      <Box className={styles.subscriptionDates}>
                        <Typography variant="body2" color="text.secondary">
                          <CalendarToday sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                          Начало: {formatDate(subscription.startDate)}
                        </Typography>
                        
                        {subscription.status === 'active' && (
                          <Typography variant="body2" color="text.secondary">
                            <Refresh sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                            Следующее списание: {formatDate(subscription.nextBillingDate)}
                            {getDaysUntilRenewal(subscription.nextBillingDate) <= 7 && (
                              <Chip
                                label={`${getDaysUntilRenewal(subscription.nextBillingDate)} дней`}
                                color="warning"
                                size="small"
                                sx={{ ml: 1 }}
                              />
                            )}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Box className={styles.subscriptionActions}>
                      {subscription.status === 'active' ? (
                        <Button
                          variant="outlined"
                          color="error"
                          onClick={() => setCancelDialogOpen(true)}
                          startIcon={<Cancel />}
                          fullWidth
                        >
                          Отменить подписку
                        </Button>
                      ) : subscription.status === 'cancelled' ? (
                        <Button
                          variant="contained"
                          onClick={handleResumeSubscription}
                          disabled={resumeSubscriptionMutation.isPending}
                          startIcon={<Refresh />}
                          fullWidth
                        >
                          Возобновить подписку
                        </Button>
                      ) : (
                        <Button
                          variant="contained"
                          onClick={() => router.push('/premium/purchase')}
                          startIcon={<Star />}
                          fullWidth
                        >
                          Обновить подписку
                        </Button>
                      )}
                      
                      <Button
                        variant="outlined"
                        onClick={() => router.push('/premium/purchase')}
                        startIcon={<TrendingUp />}
                        fullWidth
                        sx={{ mt: 1 }}
                      >
                        Изменить план
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Статистика использования */}
            {subscription.status === 'active' && usageStats && (
              <Card className={styles.usageCard}>
                <CardContent>
                  <Typography variant="h5" className={styles.usageTitle}>
                    Статистика использования
                  </Typography>
                  
                  <Grid container spacing={3} className={styles.usageStats}>
                    <Grid item xs={12} sm={6} md={3}>
                      <Box className={styles.usageStat}>
                        <Typography variant="h4" className={styles.usageNumber}>
                          {usageStats.likesUsed}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Лайков использовано
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={(usageStats.likesUsed / usageStats.likesLimit) * 100}
                          className={styles.usageProgress}
                        />
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <Box className={styles.usageStat}>
                        <Typography variant="h4" className={styles.usageNumber}>
                          {usageStats.superLikesUsed}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Суперлайков использовано
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={(usageStats.superLikesUsed / usageStats.superLikesLimit) * 100}
                          className={styles.usageProgress}
                        />
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <Box className={styles.usageStat}>
                        <Typography variant="h4" className={styles.usageNumber}>
                          {usageStats.messagesUsed}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Сообщений отправлено
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={(usageStats.messagesUsed / usageStats.messagesLimit) * 100}
                          className={styles.usageProgress}
                        />
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <Box className={styles.usageStat}>
                        <Typography variant="h4" className={styles.usageNumber}>
                          {usageStats.profileViewsUsed}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Профилей просмотрено
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={(usageStats.profileViewsUsed / usageStats.profileViewsLimit) * 100}
                          className={styles.usageProgress}
                        />
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}

            {/* История платежей */}
            <Card className={styles.invoicesCard}>
              <CardContent>
                <Typography variant="h5" className={styles.invoicesTitle}>
                  История платежей
                </Typography>
                
                {invoicesLoading ? (
                  <LinearProgress />
                ) : invoices?.length === 0 ? (
                  <Alert severity="info">
                    История платежей пуста
                  </Alert>
                ) : (
                  <TableContainer component={Paper} className={styles.invoicesTable}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Дата</TableCell>
                          <TableCell>Описание</TableCell>
                          <TableCell>Сумма</TableCell>
                          <TableCell>Статус</TableCell>
                          <TableCell>Действия</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {invoices?.map((invoice) => (
                          <TableRow key={invoice.id}>
                            <TableCell>
                              {formatDate(invoice.date)}
                            </TableCell>
                            <TableCell>
                              {invoice.description}
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" fontWeight="bold">
                                {formatCurrency(invoice.amount)}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={invoice.status}
                                color={invoice.status === 'paid' ? 'success' : 'warning'}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Tooltip title="Просмотреть">
                                <IconButton
                                  size="small"
                                  onClick={() => handleViewInvoice(invoice)}
                                >
                                  <Receipt />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Скачать">
                                <IconButton
                                  size="small"
                                  onClick={() => handleDownloadInvoice(invoice.id)}
                                >
                                  <Download />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </CardContent>
            </Card>
          </>
        )}

        {/* Диалог отмены подписки */}
        <Dialog
          open={cancelDialogOpen}
          onClose={() => setCancelDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              Отмена подписки
              <IconButton onClick={() => setCancelDialogOpen(false)}>
                <Close />
              </IconButton>
            </Box>
          </DialogTitle>
          
          <DialogContent>
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body1" gutterBottom>
                Вы уверены, что хотите отменить подписку?
              </Typography>
              <Typography variant="body2">
                После отмены вы потеряете доступ к Premium функциям в конце текущего периода.
                Подписку можно будет возобновить в любое время.
              </Typography>
            </Alert>
            
            {subscription && (
              <Typography variant="body2" color="text.secondary">
                Подписка будет активна до: {formatDate(subscription.nextBillingDate)}
              </Typography>
            )}
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setCancelDialogOpen(false)}>
              Оставить подписку
            </Button>
            <Button
              variant="contained"
              color="error"
              onClick={handleCancelSubscription}
              disabled={cancelSubscriptionMutation.isPending}
              startIcon={<Cancel />}
            >
              {cancelSubscriptionMutation.isPending ? 'Отмена...' : 'Отменить подписку'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Диалог просмотра счета */}
        <Dialog
          open={invoiceDialogOpen}
          onClose={() => setInvoiceDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              Счет #{selectedInvoice?.number}
              <IconButton onClick={() => setInvoiceDialogOpen(false)}>
                <Close />
              </IconButton>
            </Box>
          </DialogTitle>
          
          <DialogContent>
            {selectedInvoice && (
              <Box className={styles.invoiceDetails}>
                <Typography variant="body2" gutterBottom>
                  <strong>Дата:</strong> {formatDateTime(selectedInvoice.date)}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Описание:</strong> {selectedInvoice.description}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Сумма:</strong> {formatCurrency(selectedInvoice.amount)}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Статус:</strong> {selectedInvoice.status}
                </Typography>
                {selectedInvoice.paymentMethod && (
                  <Typography variant="body2" gutterBottom>
                    <strong>Способ оплаты:</strong> {selectedInvoice.paymentMethod}
                  </Typography>
                )}
              </Box>
            )}
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setInvoiceDialogOpen(false)}>
              Закрыть
            </Button>
            {selectedInvoice && (
              <Button
                variant="contained"
                onClick={() => handleDownloadInvoice(selectedInvoice.id)}
                startIcon={<Download />}
              >
                Скачать
              </Button>
            )}
          </DialogActions>
        </Dialog>
      </Container>
    </>
  );
};

export default PremiumManage;
