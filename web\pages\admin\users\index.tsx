import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Grid,
  InputAdornment,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Visibility as ViewIcon,
  Block as BlockIcon,
  CheckCircle as VerifyIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Download as ExportIcon,
  Refresh as RefreshIcon,
  PersonAdd as AddUserIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AdminLayout } from '../../../components/admin/AdminLayout';
import { useAuth } from '../../../hooks/useAuth';
import { adminService } from '../../../services/adminService';
import { AdminUser, UserFilters, UserListResponse } from '../../../types/admin.types';
import styles from '../../../styles/admin/Users.module.css';

const UsersManagement: React.FC = () => {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState<UserFilters>({
    search: '',
    gender: undefined,
    ageFrom: undefined,
    ageTo: undefined,
    city: '',
    isVerified: undefined,
    isPremium: undefined,
    isBlocked: undefined,
    sortBy: 'registrationDate',
    sortOrder: 'desc',
  });

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(20);
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [actionDialog, setActionDialog] = useState<{
    open: boolean;
    type: 'block' | 'unblock' | 'verify' | 'delete';
    user: AdminUser | null;
  }>({
    open: false,
    type: 'block',
    user: null,
  });
  const [blockReason, setBlockReason] = useState('');

  // Проверка прав доступа
  useEffect(() => {
    if (!isAdmin) {
      router.push('/');
    }
  }, [isAdmin, router]);

  // Загрузка пользователей
  const {
    data: usersData,
    isLoading,
    error,
    refetch,
  } = useQuery<UserListResponse>({
    queryKey: ['admin', 'users', filters, page, rowsPerPage],
    queryFn: () => adminService.getUsers(filters, { page: page + 1, limit: rowsPerPage }),
    enabled: isAdmin,
  });

  // Мутации для действий с пользователями
  const blockUserMutation = useMutation({
    mutationFn: ({ userId, reason }: { userId: string; reason: string }) =>
      adminService.blockUser(userId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      setActionDialog({ open: false, type: 'block', user: null });
      setBlockReason('');
    },
  });

  const unblockUserMutation = useMutation({
    mutationFn: (userId: string) => adminService.unblockUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      setActionDialog({ open: false, type: 'unblock', user: null });
    },
  });

  const verifyUserMutation = useMutation({
    mutationFn: (userId: string) => adminService.verifyUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      setActionDialog({ open: false, type: 'verify', user: null });
    },
  });

  const deleteUserMutation = useMutation({
    mutationFn: (userId: string) => adminService.deleteUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      setActionDialog({ open: false, type: 'delete', user: null });
    },
  });

  const handleFilterChange = (field: keyof UserFilters, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPage(0);
  };

  const handleClearFilters = () => {
    setFilters({
      search: '',
      gender: undefined,
      ageFrom: undefined,
      ageTo: undefined,
      city: '',
      isVerified: undefined,
      isPremium: undefined,
      isBlocked: undefined,
      sortBy: 'registrationDate',
      sortOrder: 'desc',
    });
    setPage(0);
  };

  const handleAction = (type: typeof actionDialog.type, user: AdminUser) => {
    setActionDialog({ open: true, type, user });
  };

  const handleConfirmAction = () => {
    if (!actionDialog.user) return;

    switch (actionDialog.type) {
      case 'block':
        if (blockReason.trim()) {
          blockUserMutation.mutate({ userId: actionDialog.user.id, reason: blockReason });
        }
        break;
      case 'unblock':
        unblockUserMutation.mutate(actionDialog.user.id);
        break;
      case 'verify':
        verifyUserMutation.mutate(actionDialog.user.id);
        break;
      case 'delete':
        deleteUserMutation.mutate(actionDialog.user.id);
        break;
    }
  };

  const getStatusChip = (user: AdminUser) => {
    if (user.isBlocked) {
      return <Chip label="Заблокирован" color="error" size="small" />;
    }
    if (user.isVerified) {
      return <Chip label="Верифицирован" color="success" size="small" />;
    }
    return <Chip label="Активен" color="default" size="small" />;
  };

  const getSubscriptionChip = (user: AdminUser) => {
    if (user.isPremium) {
      const color = user.subscriptionType === 'vip' ? 'secondary' : 'primary';
      return <Chip label={user.subscriptionType?.toUpperCase()} color={color} size="small" />;
    }
    return <Chip label="Базовый" color="default" size="small" />;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birth = new Date(dateOfBirth);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Управление пользователями - Админ-панель</title>
        <meta name="description" content="Управление пользователями приложения знакомств" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <AdminLayout>
        <Box className={styles.container}>
          {/* Заголовок */}
          <Box className={styles.header}>
            <Typography variant="h4" component="h1" className={styles.title}>
              Управление пользователями
            </Typography>
            <Box className={styles.headerActions}>
              <Button
                variant="outlined"
                startIcon={<ExportIcon />}
                className={styles.actionButton}
              >
                Экспорт
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => refetch()}
                className={styles.actionButton}
              >
                Обновить
              </Button>
              <Button
                variant="contained"
                startIcon={<AddUserIcon />}
                className={styles.primaryButton}
              >
                Добавить пользователя
              </Button>
            </Box>
          </Box>

          {/* Фильтры */}
          <Card className={styles.filtersCard}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    placeholder="Поиск по имени, email, телефону..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Пол</InputLabel>
                    <Select
                      value={filters.gender || ''}
                      onChange={(e) => handleFilterChange('gender', e.target.value || undefined)}
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="male">Мужской</MenuItem>
                      <MenuItem value="female">Женский</MenuItem>
                      <MenuItem value="other">Другой</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6} md={1}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Возраст от"
                    value={filters.ageFrom || ''}
                    onChange={(e) => handleFilterChange('ageFrom', e.target.value ? Number(e.target.value) : undefined)}
                  />
                </Grid>
                <Grid item xs={6} md={1}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Возраст до"
                    value={filters.ageTo || ''}
                    onChange={(e) => handleFilterChange('ageTo', e.target.value ? Number(e.target.value) : undefined)}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Статус</InputLabel>
                    <Select
                      value={filters.isBlocked === undefined ? '' : filters.isBlocked ? 'blocked' : 'active'}
                      onChange={(e) => {
                        const value = e.target.value;
                        handleFilterChange('isBlocked', value === '' ? undefined : value === 'blocked');
                      }}
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="active">Активные</MenuItem>
                      <MenuItem value="blocked">Заблокированные</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Подписка</InputLabel>
                    <Select
                      value={filters.isPremium === undefined ? '' : filters.isPremium ? 'premium' : 'basic'}
                      onChange={(e) => {
                        const value = e.target.value;
                        handleFilterChange('isPremium', value === '' ? undefined : value === 'premium');
                      }}
                    >
                      <MenuItem value="">Все</MenuItem>
                      <MenuItem value="basic">Базовая</MenuItem>
                      <MenuItem value="premium">Премиум</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={1}>
                  <Button
                    variant="outlined"
                    startIcon={<FilterIcon />}
                    onClick={handleClearFilters}
                    fullWidth
                  >
                    Сбросить
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Таблица пользователей */}
          <Card className={styles.tableCard}>
            {isLoading && <LinearProgress />}
            
            {error && (
              <Alert severity="error" className={styles.errorAlert}>
                Ошибка загрузки пользователей: {error.message}
              </Alert>
            )}

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Пользователь</TableCell>
                    <TableCell>Возраст</TableCell>
                    <TableCell>Город</TableCell>
                    <TableCell>Статус</TableCell>
                    <TableCell>Подписка</TableCell>
                    <TableCell>Регистрация</TableCell>
                    <TableCell>Последний вход</TableCell>
                    <TableCell>Действия</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {usersData?.users.map((user) => (
                    <TableRow key={user.id} className={styles.tableRow}>
                      <TableCell>
                        <Box className={styles.userCell}>
                          <Avatar src={user.avatar} alt={user.firstName}>
                            {user.firstName[0]}{user.lastName[0]}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {user.firstName} {user.lastName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {user.email}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>{calculateAge(user.dateOfBirth)}</TableCell>
                      <TableCell>{user.city}</TableCell>
                      <TableCell>{getStatusChip(user)}</TableCell>
                      <TableCell>{getSubscriptionChip(user)}</TableCell>
                      <TableCell>{formatDate(user.registrationDate)}</TableCell>
                      <TableCell>
                        {user.lastLoginDate ? formatDate(user.lastLoginDate) : 'Никогда'}
                      </TableCell>
                      <TableCell>
                        <Box className={styles.actionButtons}>
                          <Tooltip title="Просмотр">
                            <IconButton
                              size="small"
                              onClick={() => router.push(`/admin/users/${user.id}`)}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          
                          {!user.isVerified && (
                            <Tooltip title="Верифицировать">
                              <IconButton
                                size="small"
                                onClick={() => handleAction('verify', user)}
                                color="success"
                              >
                                <VerifyIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          
                          {user.isBlocked ? (
                            <Tooltip title="Разблокировать">
                              <IconButton
                                size="small"
                                onClick={() => handleAction('unblock', user)}
                                color="success"
                              >
                                <CheckCircle />
                              </IconButton>
                            </Tooltip>
                          ) : (
                            <Tooltip title="Заблокировать">
                              <IconButton
                                size="small"
                                onClick={() => handleAction('block', user)}
                                color="error"
                              >
                                <BlockIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          
                          <Tooltip title="Удалить">
                            <IconButton
                              size="small"
                              onClick={() => handleAction('delete', user)}
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            {usersData && (
              <TablePagination
                component="div"
                count={usersData.total}
                page={page}
                onPageChange={(_, newPage) => setPage(newPage)}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={(e) => {
                  setRowsPerPage(parseInt(e.target.value, 10));
                  setPage(0);
                }}
                rowsPerPageOptions={[10, 20, 50, 100]}
                labelRowsPerPage="Строк на странице:"
                labelDisplayedRows={({ from, to, count }) => 
                  `${from}-${to} из ${count !== -1 ? count : `более ${to}`}`
                }
              />
            )}
          </Card>

          {/* Диалог подтверждения действий */}
          <Dialog
            open={actionDialog.open}
            onClose={() => setActionDialog({ open: false, type: 'block', user: null })}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              {actionDialog.type === 'block' && 'Заблокировать пользователя'}
              {actionDialog.type === 'unblock' && 'Разблокировать пользователя'}
              {actionDialog.type === 'verify' && 'Верифицировать пользователя'}
              {actionDialog.type === 'delete' && 'Удалить пользователя'}
            </DialogTitle>
            <DialogContent>
              <Typography variant="body1" gutterBottom>
                {actionDialog.type === 'block' && 'Вы уверены, что хотите заблокировать этого пользователя?'}
                {actionDialog.type === 'unblock' && 'Вы уверены, что хотите разблокировать этого пользователя?'}
                {actionDialog.type === 'verify' && 'Вы уверены, что хотите верифицировать этого пользователя?'}
                {actionDialog.type === 'delete' && 'Вы уверены, что хотите удалить этого пользователя? Это действие нельзя отменить.'}
              </Typography>
              
              {actionDialog.user && (
                <Typography variant="body2" color="text.secondary">
                  {actionDialog.user.firstName} {actionDialog.user.lastName} ({actionDialog.user.email})
                </Typography>
              )}

              {actionDialog.type === 'block' && (
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Причина блокировки"
                  value={blockReason}
                  onChange={(e) => setBlockReason(e.target.value)}
                  margin="normal"
                  required
                />
              )}
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => setActionDialog({ open: false, type: 'block', user: null })}
              >
                Отмена
              </Button>
              <Button
                onClick={handleConfirmAction}
                color={actionDialog.type === 'delete' ? 'error' : 'primary'}
                variant="contained"
                disabled={
                  actionDialog.type === 'block' && !blockReason.trim() ||
                  blockUserMutation.isPending ||
                  unblockUserMutation.isPending ||
                  verifyUserMutation.isPending ||
                  deleteUserMutation.isPending
                }
              >
                {actionDialog.type === 'block' && 'Заблокировать'}
                {actionDialog.type === 'unblock' && 'Разблокировать'}
                {actionDialog.type === 'verify' && 'Верифицировать'}
                {actionDialog.type === 'delete' && 'Удалить'}
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </AdminLayout>
    </>
  );
};

export default UsersManagement;
