import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { eventsService } from '../../services/eventsService';
import { useAuth } from '../../hooks/useAuth';
import { Event } from '../../types/events.types';
import { colors, typography, spacing } from '../../theme';

interface MyEventsScreenProps {}

const MyEventsScreen: React.FC<MyEventsScreenProps> = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'organized' | 'participating' | 'past'>('organized');

  // Загрузка моих событий
  const {
    data: myEvents,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['myEvents', selectedTab],
    queryFn: () => eventsService.getMyEvents(selectedTab),
  });

  // Мутация для отмены события
  const cancelEventMutation = useMutation({
    mutationFn: (eventId: string) => eventsService.cancelEvent(eventId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['myEvents'] });
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  // Мутация для выхода из события
  const leaveEventMutation = useMutation({
    mutationFn: (eventId: string) => eventsService.leaveEvent(eventId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['myEvents'] });
      queryClient.invalidateQueries({ queryKey: ['events'] });
    },
  });

  // Обновление при фокусе экрана
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleCancelEvent = (eventId: string) => {
    Alert.alert(
      'Отменить событие',
      'Вы уверены, что хотите отменить это событие? Это действие нельзя отменить.',
      [
        { text: 'Нет', style: 'cancel' },
        {
          text: 'Отменить событие',
          style: 'destructive',
          onPress: () => cancelEventMutation.mutate(eventId),
        },
      ]
    );
  };

  const handleLeaveEvent = (eventId: string) => {
    Alert.alert(
      'Покинуть событие',
      'Вы уверены, что хотите покинуть это событие?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Покинуть',
          style: 'destructive',
          onPress: () => leaveEventMutation.mutate(eventId),
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Сегодня';
    if (diffInDays === 1) return 'Завтра';
    if (diffInDays === -1) return 'Вчера';
    if (diffInDays > 1 && diffInDays <= 7) return `Через ${diffInDays} дн.`;
    if (diffInDays < -1 && diffInDays >= -7) return `${Math.abs(diffInDays)} дн. назад`;

    return date.toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'short',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getEventStatus = (event: Event) => {
    const now = new Date();
    const eventDate = new Date(event.dateTime);
    
    if (event.status === 'cancelled') return 'Отменено';
    if (eventDate < now) return 'Завершено';
    return 'Предстоящее';
  };

  const getStatusColor = (event: Event) => {
    const now = new Date();
    const eventDate = new Date(event.dateTime);
    
    if (event.status === 'cancelled') return colors.error;
    if (eventDate < now) return colors.textSecondary;
    return colors.success;
  };

  const renderTabButton = (tab: typeof selectedTab, label: string, count?: number) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        selectedTab === tab && styles.tabButtonActive,
      ]}
      onPress={() => setSelectedTab(tab)}
    >
      <Text
        style={[
          styles.tabButtonText,
          selectedTab === tab && styles.tabButtonTextActive,
        ]}
      >
        {label}
      </Text>
      {count !== undefined && count > 0 && (
        <View style={styles.tabBadge}>
          <Text style={styles.tabBadgeText}>{count}</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderEventCard = ({ item, index }: { item: Event; index: number }) => {
    const animatedValue = useSharedValue(0);
    const isOrganizer = item.organizerId === user?.id;
    const isUpcoming = new Date(item.dateTime) > new Date();

    React.useEffect(() => {
      animatedValue.value = withSpring(1, {
        delay: index * 100,
      });
    }, []);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: animatedValue.value,
        transform: [
          {
            translateY: interpolate(animatedValue.value, [0, 1], [30, 0]),
          },
          {
            scale: interpolate(animatedValue.value, [0, 1], [0.95, 1]),
          },
        ],
      };
    });

    return (
      <Animated.View style={[styles.eventCard, animatedStyle]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('EventDetail', { eventId: item.id })}
          activeOpacity={0.8}
        >
          <Image source={{ uri: item.image }} style={styles.eventImage} />
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.eventImageGradient}
          />
          
          {/* Статус события */}
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item) }]}>
            <Text style={styles.statusText}>{getEventStatus(item)}</Text>
          </View>
          
          {/* Роль пользователя */}
          {isOrganizer && (
            <View style={styles.roleBadge}>
              <Ionicons name="star" size={14} color={colors.warning} />
              <Text style={styles.roleText}>Организатор</Text>
            </View>
          )}
          
          <View style={styles.eventInfo}>
            <Text style={styles.eventTitle}>{item.title}</Text>
            
            <View style={styles.eventDetails}>
              <View style={styles.eventDateTime}>
                <Ionicons name="calendar-outline" size={16} color={colors.textSecondary} />
                <Text style={styles.dateTimeText}>
                  {formatDate(item.dateTime)} в {formatTime(item.dateTime)}
                </Text>
              </View>
              
              <View style={styles.eventLocation}>
                <Ionicons name="location-outline" size={16} color={colors.textSecondary} />
                <Text style={styles.locationText}>{item.location.name}</Text>
              </View>
            </View>
            
            <View style={styles.eventParticipants}>
              <View style={styles.participantsAvatars}>
                {item.participants.slice(0, 3).map((participant, idx) => (
                  <Image
                    key={participant.id}
                    source={{ uri: participant.avatar }}
                    style={[
                      styles.participantAvatar,
                      { marginLeft: idx > 0 ? -8 : 0 },
                    ]}
                  />
                ))}
                {item.participants.length > 3 && (
                  <View style={[styles.participantAvatar, styles.moreParticipants]}>
                    <Text style={styles.moreParticipantsText}>
                      +{item.participants.length - 3}
                    </Text>
                  </View>
                )}
              </View>
              
              <Text style={styles.participantsCount}>
                {item.participants.length}/{item.maxParticipants}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
        
        <View style={styles.eventActions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.detailsButton]}
            onPress={() => navigation.navigate('EventDetail', { eventId: item.id })}
          >
            <Ionicons name="information-circle-outline" size={16} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>
              Подробнее
            </Text>
          </TouchableOpacity>
          
          {isUpcoming && (
            <>
              {isOrganizer ? (
                <>
                  <TouchableOpacity
                    style={[styles.actionButton, styles.editButton]}
                    onPress={() => navigation.navigate('EditEvent', { eventId: item.id })}
                  >
                    <Ionicons name="create-outline" size={16} color={colors.primary} />
                    <Text style={[styles.actionButtonText, { color: colors.primary }]}>
                      Изменить
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.actionButton, styles.cancelButton]}
                    onPress={() => handleCancelEvent(item.id)}
                    disabled={cancelEventMutation.isPending}
                  >
                    <Ionicons name="trash-outline" size={16} color={colors.error} />
                    <Text style={[styles.actionButtonText, { color: colors.error }]}>
                      Отменить
                    </Text>
                  </TouchableOpacity>
                </>
              ) : (
                <TouchableOpacity
                  style={[styles.actionButton, styles.leaveButton]}
                  onPress={() => handleLeaveEvent(item.id)}
                  disabled={leaveEventMutation.isPending}
                >
                  <Ionicons name="exit-outline" size={16} color={colors.error} />
                  <Text style={[styles.actionButtonText, { color: colors.error }]}>
                    Покинуть
                  </Text>
                </TouchableOpacity>
              )}
            </>
          )}
        </View>
      </Animated.View>
    );
  };

  if (isLoading && !myEvents) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Загружаем ваши события...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="calendar-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorText}>
            Не удалось загрузить ваши события
          </Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
            <Text style={styles.retryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Заголовок */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Мои события</Text>
        
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => navigation.navigate('CreateEvent')}
        >
          <Ionicons name="add" size={24} color={colors.white} />
        </TouchableOpacity>
      </View>

      {/* Вкладки */}
      <View style={styles.tabsContainer}>
        {renderTabButton('organized', 'Организую', myEvents?.organized?.length)}
        {renderTabButton('participating', 'Участвую', myEvents?.participating?.length)}
        {renderTabButton('past', 'Прошедшие', myEvents?.past?.length)}
      </View>

      {/* Список событий */}
      <FlatList
        data={myEvents?.[selectedTab] || []}
        renderItem={renderEventCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons
              name={
                selectedTab === 'organized'
                  ? 'calendar-outline'
                  : selectedTab === 'participating'
                  ? 'people-outline'
                  : 'time-outline'
              }
              size={64}
              color={colors.textSecondary}
            />
            <Text style={styles.emptyTitle}>
              {selectedTab === 'organized' && 'Нет организованных событий'}
              {selectedTab === 'participating' && 'Нет событий для участия'}
              {selectedTab === 'past' && 'Нет прошедших событий'}
            </Text>
            <Text style={styles.emptyText}>
              {selectedTab === 'organized' && 'Создайте свое первое событие'}
              {selectedTab === 'participating' && 'Присоединитесь к интересным событиям'}
              {selectedTab === 'past' && 'История ваших событий появится здесь'}
            </Text>
            {selectedTab === 'organized' && (
              <TouchableOpacity
                style={styles.createEventButton}
                onPress={() => navigation.navigate('CreateEvent')}
              >
                <Text style={styles.createEventButtonText}>Создать событие</Text>
              </TouchableOpacity>
            )}
          </View>
        }
      />

      {/* Индикатор загрузки действий */}
      {(cancelEventMutation.isPending || leaveEventMutation.isPending) && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
  },
  createButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 20,
    marginHorizontal: spacing.xs,
    gap: spacing.xs,
  },
  tabButtonActive: {
    backgroundColor: colors.primary,
  },
  tabButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.text,
  },
  tabButtonTextActive: {
    color: colors.white,
  },
  tabBadge: {
    backgroundColor: colors.error,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  tabBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  listContainer: {
    padding: spacing.md,
  },
  eventCard: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    marginBottom: spacing.md,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  eventImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  eventImageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  statusBadge: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.md,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  roleBadge: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 193, 7, 0.9)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    gap: spacing.xs,
  },
  roleText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  eventInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: spacing.md,
  },
  eventTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  eventDetails: {
    marginBottom: spacing.sm,
  },
  eventDateTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
    gap: spacing.xs,
  },
  dateTimeText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  eventLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  locationText: {
    fontSize: typography.sizes.sm,
    color: colors.textSecondary,
  },
  eventParticipants: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  participantsAvatars: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  participantAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.white,
  },
  moreParticipants: {
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: -8,
  },
  moreParticipantsText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  participantsCount: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontWeight: typography.weights.medium,
  },
  eventActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: colors.surface,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    backgroundColor: colors.background,
    gap: spacing.xs,
  },
  detailsButton: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  editButton: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  cancelButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  leaveButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
  },
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: typography.sizes.lg,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  errorTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  createEventButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  createEventButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.white,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MyEventsScreen;
