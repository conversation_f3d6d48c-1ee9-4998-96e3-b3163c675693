import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  StatusBar,
  Animated,
  ScrollView,
  Image,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { launchImageLibrary, launchCamera, ImagePickerResponse } from 'react-native-image-picker';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface PhotoUploadScreenProps {
  navigation: any;
  route: any;
}

interface Photo {
  id: string;
  uri: string;
  type?: string;
  fileName?: string;
}

const PhotoUploadScreen: React.FC<PhotoUploadScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { uploadPhotos } = useAuth();
  const { basicInfo, interests } = route.params || {};

  const [photos, setPhotos] = useState<Photo[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Анимации
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);
  const progressAnim = new Animated.Value(0);

  useEffect(() => {
    // Анимация появления
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(progressAnim, {
        toValue: 0.6, // 60% прогресса (3 из 5 шагов)
        duration: 1000,
        useNativeDriver: false,
      }),
    ]).start();
  }, []);

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Разрешение на использование камеры',
            message: 'Приложению нужен доступ к камере для создания фотографий',
            buttonNeutral: 'Спросить позже',
            buttonNegative: 'Отмена',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  const showImagePicker = () => {
    Alert.alert(
      'Выберите фото',
      'Откуда вы хотите добавить фотографию?',
      [
        { text: 'Отмена', style: 'cancel' },
        { text: 'Галерея', onPress: openImageLibrary },
        { text: 'Камера', onPress: openCamera },
      ]
    );
  };

  const openImageLibrary = () => {
    const options = {
      mediaType: 'photo' as const,
      quality: 0.8,
      maxWidth: 1024,
      maxHeight: 1024,
    };

    launchImageLibrary(options, handleImageResponse);
  };

  const openCamera = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert('Ошибка', 'Нет разрешения на использование камеры');
      return;
    }

    const options = {
      mediaType: 'photo' as const,
      quality: 0.8,
      maxWidth: 1024,
      maxHeight: 1024,
    };

    launchCamera(options, handleImageResponse);
  };

  const handleImageResponse = (response: ImagePickerResponse) => {
    if (response.didCancel || response.errorMessage) {
      return;
    }

    if (response.assets && response.assets[0]) {
      const asset = response.assets[0];
      const newPhoto: Photo = {
        id: Date.now().toString(),
        uri: asset.uri || '',
        type: asset.type,
        fileName: asset.fileName,
      };

      setPhotos(prev => [...prev, newPhoto]);
    }
  };

  const removePhoto = (photoId: string) => {
    setPhotos(prev => prev.filter(photo => photo.id !== photoId));
  };

  const handleNext = async () => {
    if (photos.length === 0) {
      Alert.alert(
        'Добавьте фото',
        'Добавьте хотя бы одну фотографию, чтобы другие пользователи могли вас узнать',
        [
          { text: 'Пропустить', onPress: () => proceedToNext() },
          { text: 'Добавить фото', style: 'cancel' },
        ]
      );
      return;
    }

    setIsLoading(true);

    try {
      const result = await uploadPhotos(photos);

      if (result.success) {
        proceedToNext();
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось загрузить фотографии');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при загрузке фотографий');
    } finally {
      setIsLoading(false);
    }
  };

  const proceedToNext = () => {
    navigation.navigate('PreferencesScreen', {
      basicInfo,
      interests,
      photos,
    });
  };

  const renderPhotoSlot = (index: number) => {
    const photo = photos[index];
    const isMainPhoto = index === 0;

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.photoSlot,
          isMainPhoto && styles.mainPhotoSlot,
          photo && styles.photoSlotFilled,
        ]}
        onPress={photo ? () => removePhoto(photo.id) : showImagePicker}
      >
        {photo ? (
          <>
            <Image source={{ uri: photo.uri }} style={styles.photoImage} />
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => removePhoto(photo.id)}
            >
              <Icon name="close" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            {isMainPhoto && (
              <View style={styles.mainPhotoBadge}>
                <Text style={styles.mainPhotoText}>Главное</Text>
              </View>
            )}
          </>
        ) : (
          <View style={styles.addPhotoContent}>
            <Icon
              name="add-a-photo"
              size={isMainPhoto ? 40 : 30}
              color="rgba(255, 255, 255, 0.7)"
            />
            <Text style={styles.addPhotoText}>
              {isMainPhoto ? 'Главное фото' : 'Добавить'}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Прогресс бар */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>Шаг 3 из 5</Text>
        </View>

        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Заголовок */}
            <View style={styles.headerContainer}>
              <Icon name="photo-camera" size={60} color="#FFFFFF" />
              <Text style={styles.title}>Добавьте фотографии</Text>
              <Text style={styles.subtitle}>
                Загрузите свои лучшие фото. Профили с фотографиями получают в 10 раз больше внимания
              </Text>
              <Text style={styles.counter}>
                Фотографий: {photos.length}/6
              </Text>
            </View>

            {/* Сетка фотографий */}
            <View style={styles.photosGrid}>
              <View style={styles.photosRow}>
                {renderPhotoSlot(0)}
                <View style={styles.photosColumn}>
                  {renderPhotoSlot(1)}
                  {renderPhotoSlot(2)}
                </View>
              </View>
              <View style={styles.photosRow}>
                {renderPhotoSlot(3)}
                {renderPhotoSlot(4)}
                {renderPhotoSlot(5)}
              </View>
            </View>

            {/* Советы */}
            <View style={styles.tipsContainer}>
              <Text style={styles.tipsTitle}>Советы для лучших фото:</Text>
              <View style={styles.tip}>
                <Icon name="check-circle" size={16} color="#FFFFFF" />
                <Text style={styles.tipText}>Улыбайтесь и смотрите в камеру</Text>
              </View>
              <View style={styles.tip}>
                <Icon name="check-circle" size={16} color="#FFFFFF" />
                <Text style={styles.tipText}>Используйте хорошее освещение</Text>
              </View>
              <View style={styles.tip}>
                <Icon name="check-circle" size={16} color="#FFFFFF" />
                <Text style={styles.tipText}>Покажите свои увлечения</Text>
              </View>
              <View style={styles.tip}>
                <Icon name="check-circle" size={16} color="#FFFFFF" />
                <Text style={styles.tipText}>Избегайте групповых фото</Text>
              </View>
            </View>
          </Animated.View>
        </ScrollView>

        {/* Кнопки */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
            <Text style={styles.backButtonText}>Назад</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.nextButton}
            onPress={handleNext}
            disabled={isLoading}
          >
            <View style={styles.nextButtonContent}>
              {isLoading ? (
                <Text style={styles.nextButtonText}>Загрузка...</Text>
              ) : (
                <>
                  <Text style={styles.nextButtonText}>
                    {photos.length > 0 ? 'Далее' : 'Пропустить'}
                  </Text>
                  <Icon name="arrow-forward" size={20} color="#FF6B9D" />
                </>
              )}
            </View>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  progressContainer: {
    paddingHorizontal: 30,
    paddingTop: 60,
    paddingBottom: 20,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
  progressText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 120,
  },
  content: {
    paddingHorizontal: 30,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 15,
  },
  counter: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  photosGrid: {
    marginBottom: 30,
  },
  photosRow: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  photosColumn: {
    flex: 1,
    marginLeft: 10,
  },
  photoSlot: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    overflow: 'hidden',
  },
  mainPhotoSlot: {
    width: (width - 80) / 2,
    height: (width - 80) / 2 * 1.3,
  },
  photoSlotFilled: {
    borderStyle: 'solid',
    borderColor: '#FFFFFF',
  },
  photoImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainPhotoBadge: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    backgroundColor: 'rgba(255, 107, 157, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  mainPhotoText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  addPhotoContent: {
    alignItems: 'center',
    padding: 20,
  },
  addPhotoText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  tipsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 15,
    padding: 20,
    marginBottom: 40,
  },
  tipsTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tipText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    marginLeft: 10,
    flex: 1,
  },
  buttonsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingBottom: 40,
    paddingTop: 20,
    backgroundColor: 'rgba(255, 107, 157, 0.1)',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
  nextButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingVertical: 16,
    paddingHorizontal: 30,
  },
  nextButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nextButtonText: {
    color: '#FF6B9D',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
});

export default PhotoUploadScreen;
