import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  FlatList,
  Image,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');
const CARD_WIDTH = (width - 60) / 2;

interface MutualLikesScreenProps {
  navigation: any;
}

interface MutualLike {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  age: number;
  city: string;
  mainPhoto: string;
  isOnline: boolean;
  isVerified: boolean;
  isPremium: boolean;
  mutualLikeAt: string;
  mutualFriends: number;
  commonInterests: string[];
  distance: number;
  myLikeType: 'regular' | 'super';
  theirLikeType: 'regular' | 'super';
  hasMatched: boolean;
  lastMessage?: {
    text: string;
    timestamp: string;
    isRead: boolean;
  };
  compatibility: number; // процент совместимости
}

const MutualLikesScreen: React.FC<MutualLikesScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { getMutualLikes, startChat } = useAuth();

  const [mutualLikes, setMutualLikes] = useState<MutualLike[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [sortBy, setSortBy] = useState<'recent' | 'compatibility' | 'distance'>('recent');

  useEffect(() => {
    loadMutualLikes();
  }, [sortBy]);

  const loadMutualLikes = async (pageNum = 1, refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      const result = await getMutualLikes({
        page: pageNum,
        limit: 20,
        sortBy,
      });

      if (result.success) {
        const newMutualLikes = result.mutualLikes || [];
        
        if (refresh || pageNum === 1) {
          setMutualLikes(newMutualLikes);
        } else {
          setMutualLikes(prev => [...prev, ...newMutualLikes]);
        }

        setHasMore(newMutualLikes.length === 20);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('Error loading mutual likes:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = useCallback(() => {
    loadMutualLikes(1, true);
  }, [sortBy]);

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      loadMutualLikes(page + 1);
    }
  };

  const handleUserPress = (mutualLike: MutualLike) => {
    navigation.navigate('UserProfileScreen', { 
      userId: mutualLike.userId,
      source: 'mutual_likes'
    });
  };

  const handleStartChat = async (mutualLike: MutualLike) => {
    try {
      const result = await startChat(mutualLike.userId);
      if (result.success) {
        navigation.navigate('ChatScreen', { 
          userId: mutualLike.userId,
          userName: `${mutualLike.firstName} ${mutualLike.lastName}`,
          userPhoto: mutualLike.mainPhoto,
        });
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось начать чат');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при создании чата');
    }
  };

  const formatMutualLikeTime = (mutualLikeAt: string) => {
    const now = new Date();
    const mutual = new Date(mutualLikeAt);
    const diffInHours = Math.floor((now.getTime() - mutual.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  const getCompatibilityColor = (compatibility: number) => {
    if (compatibility >= 80) return '#4CAF50';
    if (compatibility >= 60) return '#FF9800';
    return '#F44336';
  };

  const renderMutualLike = ({ item }: { item: MutualLike }) => (
    <TouchableOpacity
      style={styles.likeCard}
      onPress={() => handleUserPress(item)}
    >
      <View style={styles.imageContainer}>
        <Image source={{ uri: item.mainPhoto }} style={styles.userImage} />
        
        {/* Онлайн статус */}
        {item.isOnline && (
          <View style={styles.onlineIndicator} />
        )}

        {/* Верификация */}
        {item.isVerified && (
          <View style={styles.verifiedBadge}>
            <Icon name="verified" size={14} color="#4CAF50" />
          </View>
        )}

        {/* Премиум */}
        {item.isPremium && (
          <View style={styles.premiumBadge}>
            <Icon name="star" size={14} color="#FFD700" />
          </View>
        )}

        {/* Супер-лайки */}
        {(item.myLikeType === 'super' || item.theirLikeType === 'super') && (
          <View style={styles.superLikeBadge}>
            <Icon name="star" size={16} color="#FFFFFF" />
            {item.myLikeType === 'super' && item.theirLikeType === 'super' && (
              <Icon name="star" size={12} color="#FFFFFF" style={styles.doubleStar} />
            )}
          </View>
        )}

        {/* Совместимость */}
        <View style={[styles.compatibilityBadge, { backgroundColor: getCompatibilityColor(item.compatibility) }]}>
          <Text style={styles.compatibilityText}>{item.compatibility}%</Text>
        </View>

        {/* Градиент снизу */}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.imageGradient}
        />

        {/* Информация о пользователе */}
        <View style={styles.userInfo}>
          <Text style={styles.userName}>
            {item.firstName}, {item.age}
          </Text>
          <View style={styles.userLocation}>
            <Icon name="location-on" size={12} color="#FFFFFF" />
            <Text style={styles.locationText}>{item.distance} км</Text>
          </View>
        </View>
      </View>

      {/* Дополнительная информация */}
      <View style={styles.cardContent}>
        <View style={styles.mutualInfo}>
          <Text style={styles.mutualTime}>{formatMutualLikeTime(item.mutualLikeAt)}</Text>
          <View style={styles.heartsContainer}>
            <Icon name="favorite" size={16} color="#FF6B9D" />
            <Icon name="favorite" size={16} color="#FF6B9D" style={styles.secondHeart} />
          </View>
        </View>
        
        {/* Общие интересы */}
        {item.commonInterests.length > 0 && (
          <View style={styles.commonInterests}>
            <Icon name="favorite" size={12} color="#FF6B9D" />
            <Text style={styles.commonInterestsText}>
              {item.commonInterests.slice(0, 2).join(', ')}
              {item.commonInterests.length > 2 && ` +${item.commonInterests.length - 2}`}
            </Text>
          </View>
        )}

        {/* Взаимные друзья */}
        {item.mutualFriends > 0 && (
          <View style={styles.mutualFriends}>
            <Icon name="group" size={12} color="#4ECDC4" />
            <Text style={styles.mutualFriendsText}>
              {item.mutualFriends} общих знакомых
            </Text>
          </View>
        )}

        {/* Последнее сообщение */}
        {item.lastMessage && (
          <View style={styles.lastMessage}>
            <Text style={styles.lastMessageText} numberOfLines={1}>
              {item.lastMessage.text}
            </Text>
            <View style={[
              styles.messageIndicator,
              !item.lastMessage.isRead && styles.unreadIndicator
            ]} />
          </View>
        )}
      </View>

      {/* Действия */}
      <View style={styles.cardActions}>
        <TouchableOpacity
          style={styles.profileButton}
          onPress={() => handleUserPress(item)}
        >
          <Icon name="person" size={16} color="#666" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.chatButton}
          onPress={() => handleStartChat(item)}
        >
          <Icon name="chat" size={16} color="#FFFFFF" />
          <Text style={styles.chatButtonText}>Написать</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={styles.emptyHeartsContainer}>
        <Icon name="favorite" size={40} color="rgba(255, 255, 255, 0.3)" />
        <Icon name="favorite" size={40} color="rgba(255, 255, 255, 0.5)" style={styles.emptySecondHeart} />
      </View>
      <Text style={styles.emptyTitle}>Пока нет взаимных лайков</Text>
      <Text style={styles.emptySubtitle}>
        Когда кто-то ответит взаимностью на ваш лайк, они появятся здесь
      </Text>
      <TouchableOpacity
        style={styles.startLikingButton}
        onPress={() => navigation.navigate('SearchScreen')}
      >
        <Text style={styles.startLikingText}>Начать ставить лайки</Text>
      </TouchableOpacity>
    </View>
  );

  const renderFooter = () => {
    if (!isLoading || isRefreshing) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator color="#FFFFFF" size="small" />
      </View>
    );
  };

  const renderSortOptions = () => (
    <View style={styles.sortContainer}>
      <Text style={styles.sortLabel}>Сортировка:</Text>
      {[
        { key: 'recent', label: 'Недавние' },
        { key: 'compatibility', label: 'Совместимость' },
        { key: 'distance', label: 'Расстояние' },
      ].map((option) => (
        <TouchableOpacity
          key={option.key}
          style={[
            styles.sortOption,
            sortBy === option.key && styles.sortOptionActive,
          ]}
          onPress={() => setSortBy(option.key as any)}
        >
          <Text
            style={[
              styles.sortOptionText,
              sortBy === option.key && styles.sortOptionTextActive,
            ]}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Взаимные лайки</Text>
          <View style={styles.headerRight}>
            <Text style={styles.countText}>{mutualLikes.length}</Text>
          </View>
        </View>

        {/* Сортировка */}
        {renderSortOptions()}

        {/* Список взаимных лайков */}
        <FlatList
          data={mutualLikes}
          renderItem={renderMutualLike}
          keyExtractor={(item) => item.id}
          numColumns={2}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor="#FFFFFF"
              colors={['#FFFFFF']}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={!isLoading ? renderEmptyState : null}
        />
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  countText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sortLabel: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 10,
  },
  sortOption: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  sortOptionActive: {
    backgroundColor: '#FFFFFF',
  },
  sortOptionText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 12,
    fontWeight: '600',
  },
  sortOptionTextActive: {
    color: '#FF6B9D',
  },
  listContainer: {
    padding: 20,
  },
  likeCard: {
    width: CARD_WIDTH,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    margin: 5,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  imageContainer: {
    position: 'relative',
    height: CARD_WIDTH * 1.2,
  },
  userImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  onlineIndicator: {
    position: 'absolute',
    top: 8,
    left: 8,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  verifiedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    padding: 2,
  },
  premiumBadge: {
    position: 'absolute',
    top: 30,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    padding: 2,
  },
  superLikeBadge: {
    position: 'absolute',
    top: 8,
    left: 30,
    backgroundColor: '#4ECDC4',
    borderRadius: 12,
    padding: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  doubleStar: {
    marginLeft: -4,
  },
  compatibilityBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  compatibilityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  imageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  userInfo: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 50,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  userLocation: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 12,
    color: '#FFFFFF',
    marginLeft: 2,
  },
  cardContent: {
    padding: 12,
  },
  mutualInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  mutualTime: {
    fontSize: 12,
    color: '#666',
  },
  heartsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  secondHeart: {
    marginLeft: -6,
  },
  commonInterests: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  commonInterestsText: {
    fontSize: 11,
    color: '#FF6B9D',
    marginLeft: 4,
    flex: 1,
  },
  mutualFriends: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  mutualFriendsText: {
    fontSize: 11,
    color: '#4ECDC4',
    marginLeft: 4,
  },
  lastMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginTop: 4,
  },
  lastMessageText: {
    fontSize: 11,
    color: '#333',
    flex: 1,
  },
  messageIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#DDD',
    marginLeft: 4,
  },
  unreadIndicator: {
    backgroundColor: '#FF6B9D',
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingBottom: 12,
  },
  profileButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flex: 1,
    marginLeft: 8,
    justifyContent: 'center',
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyHeartsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  emptySecondHeart: {
    marginLeft: -15,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  startLikingButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
  },
  startLikingText: {
    color: '#FF6B9D',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

export default MutualLikesScreen;
