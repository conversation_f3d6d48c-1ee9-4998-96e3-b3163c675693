import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface SuperLikeScreenProps {
  navigation: any;
}

interface SuperLikePackage {
  id: string;
  name: string;
  count: number;
  price: number;
  originalPrice?: number;
  discount?: number;
  isPopular: boolean;
  description: string;
  features: string[];
}

interface SuperLikeStats {
  totalSent: number;
  totalReceived: number;
  matchRate: number;
  remaining: number;
  nextRefill?: string;
}

const SuperLikeScreen: React.FC<SuperLikeScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const { getSuperLikeStats, getSuperLikePackages, purchaseSuperLikes } = useAuth();

  const [stats, setStats] = useState<SuperLikeStats | null>(null);
  const [packages, setPackages] = useState<SuperLikePackage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPackage, setSelectedPackage] = useState<SuperLikePackage | null>(null);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [statsResult, packagesResult] = await Promise.all([
        getSuperLikeStats(),
        getSuperLikePackages(),
      ]);

      if (statsResult.success) {
        setStats(statsResult.stats);
      }

      if (packagesResult.success) {
        setPackages(packagesResult.packages || []);
      }
    } catch (error) {
      console.error('Error loading super like data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePackageSelect = (pkg: SuperLikePackage) => {
    setSelectedPackage(pkg);
    setShowPurchaseModal(true);
  };

  const handlePurchase = async () => {
    if (!selectedPackage) return;

    setIsPurchasing(true);
    try {
      const result = await purchaseSuperLikes(selectedPackage.id);
      if (result.success) {
        Alert.alert(
          'Покупка успешна!',
          `Вы получили ${selectedPackage.count} супер-лайков`,
          [
            {
              text: 'OK',
              onPress: () => {
                setShowPurchaseModal(false);
                loadData(); // Обновляем статистику
              },
            },
          ]
        );
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось совершить покупку');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при покупке');
    } finally {
      setIsPurchasing(false);
    }
  };

  const formatNextRefill = (nextRefill: string) => {
    const now = new Date();
    const refill = new Date(nextRefill);
    const diffInHours = Math.floor((refill.getTime() - now.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Менее часа';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч.`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн.`;
    }
  };

  const renderStats = () => {
    if (!stats) return null;

    return (
      <View style={styles.statsContainer}>
        <Text style={styles.statsTitle}>Ваша статистика супер-лайков</Text>
        
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <Icon name="star" size={24} color="#4ECDC4" />
            </View>
            <Text style={styles.statNumber}>{stats.remaining}</Text>
            <Text style={styles.statLabel}>Осталось</Text>
          </View>

          <View style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <Icon name="send" size={24} color="#FF6B9D" />
            </View>
            <Text style={styles.statNumber}>{stats.totalSent}</Text>
            <Text style={styles.statLabel}>Отправлено</Text>
          </View>

          <View style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <Icon name="favorite" size={24} color="#4CAF50" />
            </View>
            <Text style={styles.statNumber}>{stats.matchRate}%</Text>
            <Text style={styles.statLabel}>Матчи</Text>
          </View>

          <View style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <Icon name="inbox" size={24} color="#FF9800" />
            </View>
            <Text style={styles.statNumber}>{stats.totalReceived}</Text>
            <Text style={styles.statLabel}>Получено</Text>
          </View>
        </View>

        {stats.nextRefill && (
          <View style={styles.refillInfo}>
            <Icon name="schedule" size={16} color="#666" />
            <Text style={styles.refillText}>
              Следующий бесплатный супер-лайк через {formatNextRefill(stats.nextRefill)}
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderPackages = () => {
    if (!packages.length) return null;

    return (
      <View style={styles.packagesContainer}>
        <Text style={styles.packagesTitle}>Купить супер-лайки</Text>
        <Text style={styles.packagesSubtitle}>
          Супер-лайки увеличивают ваши шансы на взаимность в 3 раза
        </Text>

        {packages.map((pkg) => (
          <TouchableOpacity
            key={pkg.id}
            style={[
              styles.packageCard,
              pkg.isPopular && styles.popularPackageCard,
            ]}
            onPress={() => handlePackageSelect(pkg)}
          >
            {pkg.isPopular && (
              <View style={styles.popularBadge}>
                <Text style={styles.popularBadgeText}>ПОПУЛЯРНЫЙ</Text>
              </View>
            )}

            <View style={styles.packageHeader}>
              <View style={styles.packageInfo}>
                <Text style={styles.packageName}>{pkg.name}</Text>
                <Text style={styles.packageDescription}>{pkg.description}</Text>
              </View>

              <View style={styles.packagePricing}>
                <View style={styles.priceContainer}>
                  <Text style={styles.packagePrice}>{pkg.price} ₽</Text>
                  {pkg.originalPrice && (
                    <Text style={styles.originalPrice}>{pkg.originalPrice} ₽</Text>
                  )}
                </View>
                {pkg.discount && (
                  <View style={styles.discountBadge}>
                    <Text style={styles.discountText}>-{pkg.discount}%</Text>
                  </View>
                )}
              </View>
            </View>

            <View style={styles.packageContent}>
              <View style={styles.superLikesCount}>
                <Icon name="star" size={20} color="#4ECDC4" />
                <Text style={styles.superLikesCountText}>{pkg.count} супер-лайков</Text>
              </View>

              <View style={styles.packageFeatures}>
                {pkg.features.map((feature, index) => (
                  <View key={index} style={styles.featureItem}>
                    <Icon name="check" size={16} color="#4CAF50" />
                    <Text style={styles.featureText}>{feature}</Text>
                  </View>
                ))}
              </View>
            </View>

            <View style={styles.packageFooter}>
              <Text style={styles.pricePerLike}>
                {Math.round(pkg.price / pkg.count)} ₽ за супер-лайк
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderHowItWorks = () => (
    <View style={styles.howItWorksContainer}>
      <Text style={styles.howItWorksTitle}>Как работают супер-лайки?</Text>
      
      <View style={styles.howItWorksSteps}>
        <View style={styles.stepItem}>
          <View style={styles.stepNumber}>
            <Text style={styles.stepNumberText}>1</Text>
          </View>
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Выделяетесь среди других</Text>
            <Text style={styles.stepDescription}>
              Ваш профиль показывается первым и с особой отметкой
            </Text>
          </View>
        </View>

        <View style={styles.stepItem}>
          <View style={styles.stepNumber}>
            <Text style={styles.stepNumberText}>2</Text>
          </View>
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Увеличиваете шансы</Text>
            <Text style={styles.stepDescription}>
              Супер-лайки получают ответ в 3 раза чаще обычных лайков
            </Text>
          </View>
        </View>

        <View style={styles.stepItem}>
          <View style={styles.stepNumber}>
            <Text style={styles.stepNumberText}>3</Text>
          </View>
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>Получаете уведомление</Text>
            <Text style={styles.stepDescription}>
              Человек сразу узнает, что вы поставили ему супер-лайк
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4ECDC4" />
        <Text style={styles.loadingText}>Загрузка...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#4ECDC4', '#44A08D']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Супер-лайки</Text>
          <TouchableOpacity
            style={styles.helpButton}
            onPress={() => {/* Открыть справку */}}
          >
            <Icon name="help" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {renderStats()}
          {renderPackages()}
          {renderHowItWorks()}
        </ScrollView>
      </LinearGradient>

      {/* Модальное окно покупки */}
      <Modal
        visible={showPurchaseModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPurchaseModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Подтверждение покупки</Text>
              <TouchableOpacity
                onPress={() => setShowPurchaseModal(false)}
                style={styles.modalCloseButton}
              >
                <Icon name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            {selectedPackage && (
              <View style={styles.modalBody}>
                <View style={styles.purchaseInfo}>
                  <Icon name="star" size={40} color="#4ECDC4" />
                  <Text style={styles.purchaseTitle}>{selectedPackage.name}</Text>
                  <Text style={styles.purchaseDescription}>
                    {selectedPackage.count} супер-лайков за {selectedPackage.price} ₽
                  </Text>
                </View>

                <View style={styles.purchaseFeatures}>
                  {selectedPackage.features.map((feature, index) => (
                    <View key={index} style={styles.purchaseFeatureItem}>
                      <Icon name="check" size={16} color="#4CAF50" />
                      <Text style={styles.purchaseFeatureText}>{feature}</Text>
                    </View>
                  ))}
                </View>

                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowPurchaseModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Отмена</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.purchaseButton}
                    onPress={handlePurchase}
                    disabled={isPurchasing}
                  >
                    {isPurchasing ? (
                      <ActivityIndicator color="#FFFFFF" size="small" />
                    ) : (
                      <>
                        <Icon name="payment" size={20} color="#FFFFFF" />
                        <Text style={styles.purchaseButtonText}>
                          Купить за {selectedPackage.price} ₽
                        </Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    flex: 1,
    textAlign: 'center',
  },
  helpButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  statsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    margin: 20,
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 20,
  },
  statIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  refillInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 15,
    padding: 12,
    marginTop: 10,
  },
  refillText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  packagesContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  packagesTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  packagesSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 20,
  },
  packageCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 15,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  popularPackageCard: {
    borderWidth: 2,
    borderColor: '#4ECDC4',
  },
  popularBadge: {
    position: 'absolute',
    top: -10,
    left: 20,
    backgroundColor: '#4ECDC4',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  popularBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  packageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  packageInfo: {
    flex: 1,
  },
  packageName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  packageDescription: {
    fontSize: 14,
    color: '#666',
  },
  packagePricing: {
    alignItems: 'flex-end',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  packagePrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4ECDC4',
  },
  originalPrice: {
    fontSize: 16,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
  discountBadge: {
    backgroundColor: '#FF6B9D',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginTop: 4,
  },
  discountText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  packageContent: {
    marginBottom: 15,
  },
  superLikesCount: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  superLikesCountText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4ECDC4',
    marginLeft: 8,
  },
  packageFeatures: {
    marginLeft: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  featureText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  packageFooter: {
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    paddingTop: 12,
    alignItems: 'center',
  },
  pricePerLike: {
    fontSize: 14,
    color: '#999',
  },
  howItWorksContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    margin: 20,
    borderRadius: 20,
    padding: 20,
    marginBottom: 40,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  howItWorksTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  howItWorksSteps: {
    marginTop: 10,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  stepNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#4ECDC4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  stepNumberText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: '100%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalBody: {
    padding: 20,
  },
  purchaseInfo: {
    alignItems: 'center',
    marginBottom: 20,
  },
  purchaseTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 10,
    marginBottom: 5,
  },
  purchaseDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  purchaseFeatures: {
    marginBottom: 30,
  },
  purchaseFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  purchaseFeatureText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: 'bold',
  },
  purchaseButton: {
    flex: 2,
    backgroundColor: '#4ECDC4',
    borderRadius: 25,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  purchaseButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default SuperLikeScreen;
