import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  TextField,
  MenuItem,
  Pagination,
} from '@mui/material';
import {
  Diamond,
  ShoppingCart,
  History,
  TrendingUp,
  Star,
  LocalOffer,
  Close,
  Receipt,
  Download,
  FilterList,
  DateRange,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { diamondsService } from '../../services/diamondsService';
import { DiamondPackage, DiamondTransaction } from '../../types/diamonds.types';
import { formatDate, formatDateTime } from '../../utils/dateUtils';
import { formatCurrency } from '../../utils/formatUtils';
import styles from './diamonds.module.css';

interface DiamondsPageProps {}

const DiamondsPage: React.FC<DiamondsPageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  const [selectedPackage, setSelectedPackage] = useState<DiamondPackage | null>(null);
  const [purchaseDialogOpen, setPurchaseDialogOpen] = useState(false);
  const [filters, setFilters] = useState({
    type: 'all',
    dateRange: 'all',
  });
  const [page, setPage] = useState(1);

  // Загрузка пакетов алмазов
  const {
    data: diamondPackages,
    isLoading: packagesLoading,
    error: packagesError,
  } = useQuery({
    queryKey: ['diamonds', 'packages'],
    queryFn: () => diamondsService.getDiamondPackages(),
  });

  // Загрузка истории транзакций
  const {
    data: transactionsData,
    isLoading: transactionsLoading,
  } = useQuery({
    queryKey: ['diamonds', 'transactions', filters, page],
    queryFn: () => diamondsService.getTransactions({ ...filters, page, limit: 10 }),
    enabled: isAuthenticated,
  });

  // Мутация для покупки алмазов
  const purchaseDiamondsMutation = useMutation({
    mutationFn: (packageId: string) => diamondsService.purchaseDiamonds(packageId),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['user', 'profile'] });
      queryClient.invalidateQueries({ queryKey: ['diamonds', 'transactions'] });
      setPurchaseDialogOpen(false);
      
      if (data.redirectUrl) {
        window.location.href = data.redirectUrl;
      }
    },
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/premium/diamonds');
    }
  }, [isAuthenticated, router]);

  const handlePurchasePackage = (pkg: DiamondPackage) => {
    setSelectedPackage(pkg);
    setPurchaseDialogOpen(true);
  };

  const handleConfirmPurchase = () => {
    if (selectedPackage) {
      purchaseDiamondsMutation.mutate(selectedPackage.id);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'purchase': return 'success';
      case 'gift': return 'info';
      case 'bonus': return 'warning';
      case 'refund': return 'error';
      default: return 'default';
    }
  };

  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'purchase': return 'Покупка';
      case 'gift': return 'Подарок';
      case 'bonus': return 'Бонус';
      case 'refund': return 'Возврат';
      default: return type;
    }
  };

  const typeOptions = [
    { value: 'all', label: 'Все типы' },
    { value: 'purchase', label: 'Покупки' },
    { value: 'gift', label: 'Подарки' },
    { value: 'bonus', label: 'Бонусы' },
    { value: 'refund', label: 'Возвраты' },
  ];

  const dateRangeOptions = [
    { value: 'all', label: 'За все время' },
    { value: 'today', label: 'Сегодня' },
    { value: 'week', label: 'За неделю' },
    { value: 'month', label: 'За месяц' },
    { value: 'year', label: 'За год' },
  ];

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Алмазы - Likes & Love</title>
        <meta name="description" content="Покупайте алмазы для отправки подарков и использования премиум функций." />
        <meta name="keywords" content="алмазы, покупка, подарки, премиум" />
        <meta property="og:title" content="Алмазы - Likes & Love" />
        <meta property="og:description" content="Покупайте алмазы для подарков" />
        <meta property="og:type" content="website" />
      </Head>

      <Container maxWidth="lg" className={styles.container}>
        {/* Заголовок */}
        <Box className={styles.header}>
          <Typography variant="h3" component="h1" className={styles.title}>
            Алмазы
          </Typography>
          <Typography variant="h6" color="text.secondary" className={styles.subtitle}>
            Покупайте алмазы для отправки подарков и использования премиум функций
          </Typography>
        </Box>

        {/* Текущий баланс */}
        <Card className={styles.balanceCard}>
          <CardContent>
            <Box className={styles.balanceContent}>
              <Box className={styles.balanceInfo}>
                <Diamond sx={{ fontSize: 48, color: 'warning.main' }} />
                <Box>
                  <Typography variant="h4" className={styles.balanceAmount}>
                    {user?.balance || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Алмазов на балансе
                  </Typography>
                </Box>
              </Box>
              
              <Button
                variant="contained"
                size="large"
                onClick={() => document.getElementById('packages')?.scrollIntoView({ behavior: 'smooth' })}
                startIcon={<ShoppingCart />}
                className={styles.buyButton}
              >
                Купить алмазы
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Пакеты алмазов */}
        <Box id="packages" className={styles.packagesSection}>
          <Typography variant="h4" className={styles.sectionTitle}>
            Пакеты алмазов
          </Typography>
          
          {packagesLoading ? (
            <Box className={styles.loadingContainer}>
              <LinearProgress />
              <Typography>Загрузка пакетов...</Typography>
            </Box>
          ) : packagesError ? (
            <Alert severity="error" className={styles.errorAlert}>
              Ошибка загрузки пакетов алмазов
            </Alert>
          ) : (
            <Grid container spacing={3} className={styles.packagesGrid}>
              {diamondPackages?.map((pkg) => (
                <Grid item xs={12} sm={6} md={4} key={pkg.id}>
                  <Card 
                    className={`${styles.packageCard} ${pkg.isPopular ? styles.popularPackage : ''}`}
                  >
                    {pkg.isPopular && (
                      <Chip
                        label="Популярный"
                        color="primary"
                        className={styles.popularBadge}
                      />
                    )}
                    
                    {pkg.bonusPercentage > 0 && (
                      <Chip
                        label={`+${pkg.bonusPercentage}% бонус`}
                        color="success"
                        className={styles.bonusBadge}
                      />
                    )}
                    
                    <CardContent className={styles.packageContent}>
                      <Box className={styles.packageIcon}>
                        <Diamond sx={{ fontSize: 64, color: 'warning.main' }} />
                      </Box>
                      
                      <Typography variant="h5" className={styles.packageAmount}>
                        {pkg.amount.toLocaleString()}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" className={styles.packageDescription}>
                        {pkg.description}
                      </Typography>
                      
                      <Typography variant="h4" className={styles.packagePrice}>
                        {formatCurrency(pkg.price)}
                      </Typography>
                      
                      {pkg.bonusAmount > 0 && (
                        <Typography variant="body2" className={styles.bonusAmount}>
                          +{pkg.bonusAmount} бонусных алмазов
                        </Typography>
                      )}
                      
                      <Button
                        variant={pkg.isPopular ? 'contained' : 'outlined'}
                        size="large"
                        fullWidth
                        onClick={() => handlePurchasePackage(pkg)}
                        className={styles.purchaseButton}
                        startIcon={<ShoppingCart />}
                      >
                        Купить
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>

        {/* История транзакций */}
        <Box className={styles.historySection}>
          <Typography variant="h4" className={styles.sectionTitle}>
            История транзакций
          </Typography>
          
          {/* Фильтры */}
          <Card className={styles.filtersCard}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    select
                    fullWidth
                    label="Тип транзакции"
                    value={filters.type}
                    onChange={(e) => handleFilterChange('type', e.target.value)}
                    size="small"
                  >
                    {typeOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    select
                    fullWidth
                    label="Период"
                    value={filters.dateRange}
                    onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                    size="small"
                  >
                    {dateRangeOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
          
          {transactionsLoading ? (
            <LinearProgress />
          ) : transactionsData?.transactions?.length === 0 ? (
            <Alert severity="info" className={styles.emptyAlert}>
              История транзакций пуста
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper} className={styles.transactionsTable}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Дата</TableCell>
                      <TableCell>Тип</TableCell>
                      <TableCell>Описание</TableCell>
                      <TableCell>Количество</TableCell>
                      <TableCell>Сумма</TableCell>
                      <TableCell>Статус</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {transactionsData?.transactions?.map((transaction: DiamondTransaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          {formatDateTime(transaction.date)}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getTransactionTypeLabel(transaction.type)}
                            color={getTransactionTypeColor(transaction.type) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {transaction.description}
                        </TableCell>
                        <TableCell>
                          <Box className={styles.diamondAmount}>
                            <Diamond sx={{ fontSize: 16, color: 'warning.main' }} />
                            <Typography variant="body2" fontWeight="bold">
                              {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          {transaction.price ? formatCurrency(transaction.price) : '-'}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={transaction.status}
                            color={transaction.status === 'completed' ? 'success' : 'warning'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              
              {/* Пагинация */}
              {transactionsData?.totalPages > 1 && (
                <Box className={styles.paginationContainer}>
                  <Pagination
                    count={transactionsData.totalPages}
                    page={page}
                    onChange={(_, newPage) => setPage(newPage)}
                    color="primary"
                    size="large"
                  />
                </Box>
              )}
            </>
          )}
        </Box>

        {/* Диалог покупки */}
        <Dialog
          open={purchaseDialogOpen}
          onClose={() => setPurchaseDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              Покупка алмазов
              <IconButton onClick={() => setPurchaseDialogOpen(false)}>
                <Close />
              </IconButton>
            </Box>
          </DialogTitle>
          
          <DialogContent>
            {selectedPackage && (
              <Box className={styles.purchaseContent}>
                <Box className={styles.selectedPackage}>
                  <Diamond sx={{ fontSize: 48, color: 'warning.main' }} />
                  <Box>
                    <Typography variant="h5">
                      {selectedPackage.amount.toLocaleString()} алмазов
                    </Typography>
                    {selectedPackage.bonusAmount > 0 && (
                      <Typography variant="body2" color="success.main">
                        +{selectedPackage.bonusAmount} бонусных алмазов
                      </Typography>
                    )}
                    <Typography variant="h4" color="primary">
                      {formatCurrency(selectedPackage.price)}
                    </Typography>
                  </Box>
                </Box>
                
                <Alert severity="info" sx={{ mt: 2 }}>
                  Алмазы будут зачислены на ваш баланс сразу после успешной оплаты.
                </Alert>
              </Box>
            )}
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setPurchaseDialogOpen(false)}>
              Отмена
            </Button>
            <Button
              variant="contained"
              onClick={handleConfirmPurchase}
              disabled={purchaseDiamondsMutation.isPending}
              startIcon={<ShoppingCart />}
            >
              {purchaseDiamondsMutation.isPending ? 'Обработка...' : 'Купить'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </>
  );
};

export default DiamondsPage;
