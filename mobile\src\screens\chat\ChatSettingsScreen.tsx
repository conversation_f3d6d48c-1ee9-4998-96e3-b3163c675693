import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  ScrollView,
  Switch,
  Alert,
  Modal,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface ChatSettingsScreenProps {
  navigation: any;
  route: any;
}

interface ChatSettings {
  notifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    preview: boolean;
  };
  privacy: {
    readReceipts: boolean;
    lastSeen: boolean;
    typing: boolean;
    onlineStatus: boolean;
  };
  security: {
    endToEndEncryption: boolean;
    disappearingMessages: boolean;
    disappearingTime: number; // в часах
    blockScreenshots: boolean;
  };
  chat: {
    fontSize: 'small' | 'medium' | 'large';
    theme: 'light' | 'dark' | 'auto';
    wallpaper: string;
    autoDownload: {
      photos: boolean;
      videos: boolean;
      files: boolean;
    };
  };
}

const ChatSettingsScreen: React.FC<ChatSettingsScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { 
    getChatSettings, 
    updateChatSettings, 
    clearChatHistory, 
    blockUser, 
    unblockUser,
    exportChatHistory 
  } = useAuth();
  
  const { chatId, userId, userName } = route.params;

  const [settings, setSettings] = useState<ChatSettings>({
    notifications: {
      enabled: true,
      sound: true,
      vibration: true,
      preview: true,
    },
    privacy: {
      readReceipts: true,
      lastSeen: true,
      typing: true,
      onlineStatus: true,
    },
    security: {
      endToEndEncryption: true,
      disappearingMessages: false,
      disappearingTime: 24,
      blockScreenshots: false,
    },
    chat: {
      fontSize: 'medium',
      theme: 'auto',
      wallpaper: '',
      autoDownload: {
        photos: true,
        videos: false,
        files: false,
      },
    },
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [showClearHistoryModal, setShowClearHistoryModal] = useState(false);
  const [showDisappearingModal, setShowDisappearingModal] = useState(false);

  useEffect(() => {
    loadChatSettings();
  }, [chatId]);

  const loadChatSettings = async () => {
    setIsLoading(true);
    try {
      const result = await getChatSettings(chatId);
      if (result.success) {
        setSettings(result.settings);
        setIsBlocked(result.isBlocked || false);
      }
    } catch (error) {
      console.error('Error loading chat settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (newSettings: ChatSettings) => {
    setIsSaving(true);
    try {
      const result = await updateChatSettings(chatId, newSettings);
      if (result.success) {
        setSettings(newSettings);
      } else {
        Alert.alert('Ошибка', 'Не удалось сохранить настройки');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при сохранении настроек');
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (section: keyof ChatSettings, key: string, value: any) => {
    const newSettings = {
      ...settings,
      [section]: {
        ...settings[section],
        [key]: value,
      },
    };
    saveSettings(newSettings);
  };

  const updateNestedSetting = (section: keyof ChatSettings, subsection: string, key: string, value: any) => {
    const newSettings = {
      ...settings,
      [section]: {
        ...settings[section],
        [subsection]: {
          ...(settings[section] as any)[subsection],
          [key]: value,
        },
      },
    };
    saveSettings(newSettings);
  };

  const handleClearHistory = () => {
    Alert.alert(
      'Очистить историю',
      'Вы уверены, что хотите удалить всю историю сообщений? Это действие нельзя отменить.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Очистить',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await clearChatHistory(chatId);
              if (result.success) {
                Alert.alert('Успешно', 'История чата очищена');
                setShowClearHistoryModal(false);
              } else {
                Alert.alert('Ошибка', result.message || 'Не удалось очистить историю');
              }
            } catch (error) {
              Alert.alert('Ошибка', 'Произошла ошибка при очистке истории');
            }
          },
        },
      ]
    );
  };

  const handleBlockUser = async () => {
    const action = isBlocked ? 'разблокировать' : 'заблокировать';
    
    Alert.alert(
      `${action.charAt(0).toUpperCase() + action.slice(1)} пользователя`,
      `Вы уверены, что хотите ${action} ${userName}?`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: action.charAt(0).toUpperCase() + action.slice(1),
          style: isBlocked ? 'default' : 'destructive',
          onPress: async () => {
            try {
              const result = isBlocked 
                ? await unblockUser(userId)
                : await blockUser(userId);
                
              if (result.success) {
                setIsBlocked(!isBlocked);
                Alert.alert('Успешно', `Пользователь ${isBlocked ? 'разблокирован' : 'заблокирован'}`);
              } else {
                Alert.alert('Ошибка', result.message || `Не удалось ${action} пользователя`);
              }
            } catch (error) {
              Alert.alert('Ошибка', `Произошла ошибка при попытке ${action} пользователя`);
            }
          },
        },
      ]
    );
  };

  const handleExportHistory = async () => {
    try {
      const result = await exportChatHistory(chatId);
      if (result.success) {
        Alert.alert('Успешно', 'История чата экспортирована');
      } else {
        Alert.alert('Ошибка', result.message || 'Не удалось экспортировать историю');
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Произошла ошибка при экспорте истории');
    }
  };

  const renderSettingItem = (
    title: string,
    subtitle: string,
    value: boolean,
    onValueChange: (value: boolean) => void,
    icon: string
  ) => (
    <View style={styles.settingItem}>
      <View style={styles.settingIcon}>
        <Icon name={icon} size={20} color="#4ECDC4" />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingSubtitle}>{subtitle}</Text>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#E0E0E0', true: '#4ECDC4' }}
        thumbColor={value ? '#FFFFFF' : '#FFFFFF'}
      />
    </View>
  );

  const renderSelectItem = (
    title: string,
    subtitle: string,
    value: string,
    options: { label: string; value: string }[],
    onSelect: (value: string) => void,
    icon: string
  ) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={() => {
        Alert.alert(
          title,
          'Выберите опцию:',
          [
            ...options.map(option => ({
              text: option.label,
              onPress: () => onSelect(option.value),
            })),
            { text: 'Отмена', style: 'cancel' },
          ]
        );
      }}
    >
      <View style={styles.settingIcon}>
        <Icon name={icon} size={20} color="#4ECDC4" />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingSubtitle}>{subtitle}</Text>
      </View>
      <View style={styles.settingValue}>
        <Text style={styles.settingValueText}>
          {options.find(o => o.value === value)?.label || value}
        </Text>
        <Icon name="chevron-right" size={20} color="#999" />
      </View>
    </TouchableOpacity>
  );

  const renderActionItem = (
    title: string,
    subtitle: string,
    onPress: () => void,
    icon: string,
    color: string = '#333'
  ) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingIcon}>
        <Icon name={icon} size={20} color={color} />
      </View>
      <View style={styles.settingContent}>
        <Text style={[styles.settingTitle, { color }]}>{title}</Text>
        <Text style={styles.settingSubtitle}>{subtitle}</Text>
      </View>
      <Icon name="chevron-right" size={20} color="#999" />
    </TouchableOpacity>
  );

  const renderDisappearingModal = () => (
    <Modal
      visible={showDisappearingModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowDisappearingModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Исчезающие сообщения</Text>
            <TouchableOpacity onPress={() => setShowDisappearingModal(false)}>
              <Icon name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalBody}>
            <Text style={styles.modalDescription}>
              Сообщения будут автоматически удаляться через выбранное время после отправки.
            </Text>

            {[
              { label: '1 час', value: 1 },
              { label: '24 часа', value: 24 },
              { label: '7 дней', value: 168 },
              { label: '30 дней', value: 720 },
            ].map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.timeOption,
                  settings.security.disappearingTime === option.value && styles.timeOptionSelected,
                ]}
                onPress={() => {
                  updateSetting('security', 'disappearingTime', option.value);
                  setShowDisappearingModal(false);
                }}
              >
                <Text
                  style={[
                    styles.timeOptionText,
                    settings.security.disappearingTime === option.value && styles.timeOptionTextSelected,
                  ]}
                >
                  {option.label}
                </Text>
                {settings.security.disappearingTime === option.value && (
                  <Icon name="check" size={20} color="#4ECDC4" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF6B9D" />
        <Text style={styles.loadingText}>Загрузка настроек...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.background}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Заголовок */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Настройки чата</Text>
          <Text style={styles.headerSubtitle}>{userName}</Text>
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Уведомления */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Уведомления</Text>
            
            {renderSettingItem(
              'Уведомления',
              'Получать уведомления от этого чата',
              settings.notifications.enabled,
              (value) => updateSetting('notifications', 'enabled', value),
              'notifications'
            )}

            {settings.notifications.enabled && (
              <>
                {renderSettingItem(
                  'Звук',
                  'Звуковые уведомления',
                  settings.notifications.sound,
                  (value) => updateSetting('notifications', 'sound', value),
                  'volume-up'
                )}

                {renderSettingItem(
                  'Вибрация',
                  'Вибрация при получении сообщений',
                  settings.notifications.vibration,
                  (value) => updateSetting('notifications', 'vibration', value),
                  'vibration'
                )}

                {renderSettingItem(
                  'Предпросмотр',
                  'Показывать текст сообщения в уведомлении',
                  settings.notifications.preview,
                  (value) => updateSetting('notifications', 'preview', value),
                  'preview'
                )}
              </>
            )}
          </View>

          {/* Приватность */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Приватность</Text>
            
            {renderSettingItem(
              'Отчеты о прочтении',
              'Отправлять уведомления о прочтении сообщений',
              settings.privacy.readReceipts,
              (value) => updateSetting('privacy', 'readReceipts', value),
              'done-all'
            )}

            {renderSettingItem(
              'Время последнего посещения',
              'Показывать когда вы были в сети',
              settings.privacy.lastSeen,
              (value) => updateSetting('privacy', 'lastSeen', value),
              'schedule'
            )}

            {renderSettingItem(
              'Индикатор печати',
              'Показывать когда вы печатаете',
              settings.privacy.typing,
              (value) => updateSetting('privacy', 'typing', value),
              'edit'
            )}

            {renderSettingItem(
              'Статус онлайн',
              'Показывать когда вы в сети',
              settings.privacy.onlineStatus,
              (value) => updateSetting('privacy', 'onlineStatus', value),
              'circle'
            )}
          </View>

          {/* Безопасность */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Безопасность</Text>
            
            {renderSettingItem(
              'Сквозное шифрование',
              'Защита сообщений от перехвата',
              settings.security.endToEndEncryption,
              (value) => updateSetting('security', 'endToEndEncryption', value),
              'security'
            )}

            {renderSettingItem(
              'Исчезающие сообщения',
              'Автоматическое удаление сообщений',
              settings.security.disappearingMessages,
              (value) => updateSetting('security', 'disappearingMessages', value),
              'timer'
            )}

            {settings.security.disappearingMessages && (
              <TouchableOpacity
                style={styles.settingItem}
                onPress={() => setShowDisappearingModal(true)}
              >
                <View style={styles.settingIcon}>
                  <Icon name="schedule" size={20} color="#4ECDC4" />
                </View>
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>Время удаления</Text>
                  <Text style={styles.settingSubtitle}>
                    Сообщения удаляются через {settings.security.disappearingTime} ч.
                  </Text>
                </View>
                <Icon name="chevron-right" size={20} color="#999" />
              </TouchableOpacity>
            )}

            {renderSettingItem(
              'Блокировка скриншотов',
              'Запретить создание скриншотов чата',
              settings.security.blockScreenshots,
              (value) => updateSetting('security', 'blockScreenshots', value),
              'block'
            )}
          </View>

          {/* Настройки чата */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Оформление</Text>
            
            {renderSelectItem(
              'Размер шрифта',
              'Размер текста в сообщениях',
              settings.chat.fontSize,
              [
                { label: 'Маленький', value: 'small' },
                { label: 'Средний', value: 'medium' },
                { label: 'Большой', value: 'large' },
              ],
              (value) => updateSetting('chat', 'fontSize', value),
              'text-fields'
            )}

            {renderSelectItem(
              'Тема',
              'Цветовая схема чата',
              settings.chat.theme,
              [
                { label: 'Светлая', value: 'light' },
                { label: 'Темная', value: 'dark' },
                { label: 'Авто', value: 'auto' },
              ],
              (value) => updateSetting('chat', 'theme', value),
              'palette'
            )}
          </View>

          {/* Автозагрузка */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Автозагрузка медиа</Text>
            
            {renderSettingItem(
              'Фотографии',
              'Автоматически загружать фото',
              settings.chat.autoDownload.photos,
              (value) => updateNestedSetting('chat', 'autoDownload', 'photos', value),
              'photo'
            )}

            {renderSettingItem(
              'Видео',
              'Автоматически загружать видео',
              settings.chat.autoDownload.videos,
              (value) => updateNestedSetting('chat', 'autoDownload', 'videos', value),
              'videocam'
            )}

            {renderSettingItem(
              'Файлы',
              'Автоматически загружать документы',
              settings.chat.autoDownload.files,
              (value) => updateNestedSetting('chat', 'autoDownload', 'files', value),
              'insert-drive-file'
            )}
          </View>

          {/* Действия */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Действия</Text>
            
            {renderActionItem(
              'Медиафайлы',
              'Просмотреть все фото, видео и файлы',
              () => navigation.navigate('MediaGalleryScreen', { chatId, chatName: userName }),
              'perm-media'
            )}

            {renderActionItem(
              'Экспорт истории',
              'Сохранить историю чата',
              handleExportHistory,
              'file-download'
            )}

            {renderActionItem(
              'Очистить историю',
              'Удалить все сообщения',
              handleClearHistory,
              'delete-sweep',
              '#F44336'
            )}

            {renderActionItem(
              isBlocked ? 'Разблокировать' : 'Заблокировать',
              isBlocked ? 'Разблокировать пользователя' : 'Заблокировать пользователя',
              handleBlockUser,
              isBlocked ? 'person' : 'block',
              isBlocked ? '#4CAF50' : '#F44336'
            )}
          </View>
        </ScrollView>

        {/* Индикатор сохранения */}
        {isSaving && (
          <View style={styles.savingIndicator}>
            <ActivityIndicator color="#FFFFFF" size="small" />
            <Text style={styles.savingText}>Сохранение...</Text>
          </View>
        )}
      </LinearGradient>

      {/* Модальные окна */}
      {renderDisappearingModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    position: 'absolute',
    left: 20,
    top: 68,
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginHorizontal: 10,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#F5F5F5',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingIcon: {
    width: 40,
    alignItems: 'center',
  },
  settingContent: {
    flex: 1,
    marginLeft: 15,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValueText: {
    fontSize: 14,
    color: '#4ECDC4',
    marginRight: 5,
  },
  savingIndicator: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    marginHorizontal: 50,
    paddingVertical: 10,
    borderRadius: 20,
  },
  savingText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontSize: 14,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: '100%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalBody: {
    padding: 20,
  },
  modalDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 20,
  },
  timeOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 15,
    borderRadius: 10,
    marginBottom: 10,
    backgroundColor: '#F5F5F5',
  },
  timeOptionSelected: {
    backgroundColor: 'rgba(78, 205, 196, 0.1)',
    borderWidth: 1,
    borderColor: '#4ECDC4',
  },
  timeOptionText: {
    fontSize: 16,
    color: '#333',
  },
  timeOptionTextSelected: {
    color: '#4ECDC4',
    fontWeight: '600',
  },
});

export default ChatSettingsScreen;
