import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Image,
  Animated,
  Alert,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';

const { width, height } = Dimensions.get('window');

interface VideoCallScreenProps {
  navigation: any;
  route: any;
}

interface CallState {
  status: 'connecting' | 'ringing' | 'connected' | 'ended' | 'failed';
  duration: number;
  isMuted: boolean;
  isVideoOn: boolean;
  isSpeakerOn: boolean;
  isFrontCamera: boolean;
  isIncoming: boolean;
  callId: string;
  userId: string;
  userName: string;
  userPhoto: string;
  remoteVideoEnabled: boolean;
}

const VideoCallScreen: React.FC<VideoCallScreenProps> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { 
    startVideoCall, 
    endCall, 
    toggleMute, 
    toggleVideo, 
    toggleSpeaker, 
    switchCamera,
    answerCall, 
    rejectCall 
  } = useAuth();
  
  const { userId, userName, userPhoto, isIncoming = false, callId } = route.params;

  const [callState, setCallState] = useState<CallState>({
    status: isIncoming ? 'ringing' : 'connecting',
    duration: 0,
    isMuted: false,
    isVideoOn: true,
    isSpeakerOn: true,
    isFrontCamera: true,
    isIncoming,
    callId: callId || '',
    userId,
    userName,
    userPhoto,
    remoteVideoEnabled: true,
  });

  const [controlsVisible, setControlsVisible] = useState(true);
  const controlsOpacity = useRef(new Animated.Value(1)).current;
  const durationInterval = useRef<NodeJS.Timeout | null>(null);
  const hideControlsTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    requestVideoPermissions();
    
    if (!isIncoming) {
      initiateCall();
    }

    startHideControlsTimer();

    return () => {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
      if (hideControlsTimeout.current) {
        clearTimeout(hideControlsTimeout.current);
      }
    };
  }, []);

  useEffect(() => {
    if (callState.status === 'connected') {
      startDurationTimer();
    } else if (callState.status === 'ended' || callState.status === 'failed') {
      stopDurationTimer();
      setTimeout(() => {
        navigation.goBack();
      }, 2000);
    }
  }, [callState.status]);

  const requestVideoPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.CAMERA,
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          PermissionsAndroid.PERMISSIONS.MODIFY_AUDIO_SETTINGS,
        ]);

        const allPermissionsGranted = Object.values(granted).every(
          permission => permission === PermissionsAndroid.RESULTS.GRANTED
        );

        if (!allPermissionsGranted) {
          Alert.alert(
            'Разрешения не предоставлены',
            'Для видеозвонков необходимы разрешения на использование камеры и микрофона',
            [{ text: 'OK', onPress: () => navigation.goBack() }]
          );
        }
      } catch (error) {
        console.error('Error requesting permissions:', error);
      }
    }
  };

  const initiateCall = async () => {
    try {
      const result = await startVideoCall(userId);
      if (result.success) {
        setCallState(prev => ({
          ...prev,
          callId: result.callId,
          status: 'ringing',
        }));
      } else {
        setCallState(prev => ({ ...prev, status: 'failed' }));
        Alert.alert('Ошибка', 'Не удалось начать видеозвонок');
      }
    } catch (error) {
      setCallState(prev => ({ ...prev, status: 'failed' }));
      Alert.alert('Ошибка', 'Произошла ошибка при видеозвонке');
    }
  };

  const startDurationTimer = () => {
    durationInterval.current = setInterval(() => {
      setCallState(prev => ({
        ...prev,
        duration: prev.duration + 1,
      }));
    }, 1000);
  };

  const stopDurationTimer = () => {
    if (durationInterval.current) {
      clearInterval(durationInterval.current);
      durationInterval.current = null;
    }
  };

  const startHideControlsTimer = () => {
    if (hideControlsTimeout.current) {
      clearTimeout(hideControlsTimeout.current);
    }

    hideControlsTimeout.current = setTimeout(() => {
      if (callState.status === 'connected') {
        hideControls();
      }
    }, 5000);
  };

  const showControls = () => {
    setControlsVisible(true);
    Animated.timing(controlsOpacity, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
    startHideControlsTimer();
  };

  const hideControls = () => {
    Animated.timing(controlsOpacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setControlsVisible(false);
    });
  };

  const handleScreenTap = () => {
    if (callState.status === 'connected') {
      if (controlsVisible) {
        hideControls();
      } else {
        showControls();
      }
    }
  };

  const handleAnswer = async () => {
    try {
      const result = await answerCall(callState.callId);
      if (result.success) {
        setCallState(prev => ({ ...prev, status: 'connected' }));
      } else {
        setCallState(prev => ({ ...prev, status: 'failed' }));
      }
    } catch (error) {
      setCallState(prev => ({ ...prev, status: 'failed' }));
    }
  };

  const handleReject = async () => {
    try {
      await rejectCall(callState.callId);
      setCallState(prev => ({ ...prev, status: 'ended' }));
    } catch (error) {
      setCallState(prev => ({ ...prev, status: 'ended' }));
    }
  };

  const handleEndCall = async () => {
    try {
      await endCall(callState.callId);
      setCallState(prev => ({ ...prev, status: 'ended' }));
    } catch (error) {
      setCallState(prev => ({ ...prev, status: 'ended' }));
    }
  };

  const handleToggleMute = async () => {
    try {
      await toggleMute(callState.callId);
      setCallState(prev => ({ ...prev, isMuted: !prev.isMuted }));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить состояние микрофона');
    }
  };

  const handleToggleVideo = async () => {
    try {
      await toggleVideo(callState.callId);
      setCallState(prev => ({ ...prev, isVideoOn: !prev.isVideoOn }));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить состояние камеры');
    }
  };

  const handleToggleSpeaker = async () => {
    try {
      await toggleSpeaker(callState.callId);
      setCallState(prev => ({ ...prev, isSpeakerOn: !prev.isSpeakerOn }));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить режим динамика');
    }
  };

  const handleSwitchCamera = async () => {
    try {
      await switchCamera(callState.callId);
      setCallState(prev => ({ ...prev, isFrontCamera: !prev.isFrontCamera }));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось переключить камеру');
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusText = () => {
    switch (callState.status) {
      case 'connecting':
        return 'Соединение...';
      case 'ringing':
        return callState.isIncoming ? 'Входящий видеозвонок' : 'Видеозвонок...';
      case 'connected':
        return formatDuration(callState.duration);
      case 'ended':
        return 'Звонок завершен';
      case 'failed':
        return 'Не удалось соединиться';
      default:
        return '';
    }
  };

  const renderVideoView = () => {
    if (callState.status === 'connected') {
      return (
        <TouchableOpacity
          style={styles.videoContainer}
          activeOpacity={1}
          onPress={handleScreenTap}
        >
          {/* Удаленное видео */}
          <View style={styles.remoteVideoContainer}>
            {callState.remoteVideoEnabled ? (
              <View style={styles.remoteVideo}>
                {/* Здесь будет компонент удаленного видео */}
                <Text style={styles.videoPlaceholder}>Видео собеседника</Text>
              </View>
            ) : (
              <View style={styles.videoDisabled}>
                <Image source={{ uri: callState.userPhoto }} style={styles.remoteAvatar} />
                <Text style={styles.videoDisabledText}>Камера выключена</Text>
              </View>
            )}
          </View>

          {/* Локальное видео */}
          <View style={styles.localVideoContainer}>
            {callState.isVideoOn ? (
              <View style={styles.localVideo}>
                {/* Здесь будет компонент локального видео */}
                <Text style={styles.localVideoPlaceholder}>Ваше видео</Text>
              </View>
            ) : (
              <View style={styles.localVideoDisabled}>
                <Icon name="videocam-off" size={30} color="#FFFFFF" />
              </View>
            )}
          </View>
        </TouchableOpacity>
      );
    }

    return (
      <View style={styles.preCallContainer}>
        <Image source={{ uri: callState.userPhoto }} style={styles.preCallAvatar} />
        <Text style={styles.preCallName}>{callState.userName}</Text>
        <Text style={styles.preCallStatus}>{getStatusText()}</Text>
      </View>
    );
  };

  const renderControls = () => {
    if (!controlsVisible && callState.status === 'connected') {
      return null;
    }

    return (
      <Animated.View style={[styles.controlsContainer, { opacity: controlsOpacity }]}>
        {/* Верхние элементы управления */}
        <View style={styles.topControls}>
          <TouchableOpacity
            style={styles.minimizeButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="keyboard-arrow-down" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          
          <View style={styles.callInfo}>
            <Text style={styles.callInfoName}>{callState.userName}</Text>
            <Text style={styles.callInfoStatus}>{getStatusText()}</Text>
          </View>

          <TouchableOpacity
            style={styles.switchCameraButton}
            onPress={handleSwitchCamera}
          >
            <Icon name="flip-camera-ios" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Нижние элементы управления */}
        <View style={styles.bottomControls}>
          {callState.status === 'ringing' && callState.isIncoming ? (
            <View style={styles.incomingActions}>
              <TouchableOpacity
                style={[styles.callAction, styles.rejectAction]}
                onPress={handleReject}
              >
                <Icon name="call-end" size={30} color="#FFFFFF" />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.callAction, styles.answerAction]}
                onPress={handleAnswer}
              >
                <Icon name="videocam" size={30} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          ) : callState.status === 'connected' ? (
            <View style={styles.connectedActions}>
              <TouchableOpacity
                style={[styles.controlButton, callState.isMuted && styles.activeControlButton]}
                onPress={handleToggleMute}
              >
                <Icon
                  name={callState.isMuted ? 'mic-off' : 'mic'}
                  size={24}
                  color="#FFFFFF"
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.controlButton, !callState.isVideoOn && styles.activeControlButton]}
                onPress={handleToggleVideo}
              >
                <Icon
                  name={callState.isVideoOn ? 'videocam' : 'videocam-off'}
                  size={24}
                  color="#FFFFFF"
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.controlButton, callState.isSpeakerOn && styles.activeControlButton]}
                onPress={handleToggleSpeaker}
              >
                <Icon
                  name={callState.isSpeakerOn ? 'volume-up' : 'volume-down'}
                  size={24}
                  color="#FFFFFF"
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.callAction, styles.endCallAction]}
                onPress={handleEndCall}
              >
                <Icon name="call-end" size={30} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.defaultActions}>
              <TouchableOpacity
                style={[styles.callAction, styles.endCallAction]}
                onPress={handleEndCall}
              >
                <Icon name="call-end" size={30} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      <View style={styles.background}>
        {renderVideoView()}
        {renderControls()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  background: {
    flex: 1,
  },
  videoContainer: {
    flex: 1,
  },
  remoteVideoContainer: {
    flex: 1,
  },
  remoteVideo: {
    flex: 1,
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoPlaceholder: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  videoDisabled: {
    flex: 1,
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  remoteAvatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 20,
  },
  videoDisabledText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  localVideoContainer: {
    position: 'absolute',
    top: 100,
    right: 20,
    width: 120,
    height: 160,
    borderRadius: 15,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  localVideo: {
    flex: 1,
    backgroundColor: '#555',
    justifyContent: 'center',
    alignItems: 'center',
  },
  localVideoPlaceholder: {
    color: '#FFFFFF',
    fontSize: 12,
    textAlign: 'center',
  },
  localVideoDisabled: {
    flex: 1,
    backgroundColor: '#333',
    justifyContent: 'center',
    alignItems: 'center',
  },
  preCallContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
    paddingHorizontal: 40,
  },
  preCallAvatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginBottom: 30,
  },
  preCallName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  preCallStatus: {
    fontSize: 18,
    color: '#CCCCCC',
    textAlign: 'center',
  },
  controlsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  minimizeButton: {
    padding: 8,
  },
  callInfo: {
    flex: 1,
    alignItems: 'center',
  },
  callInfoName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  callInfoStatus: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 2,
  },
  switchCameraButton: {
    padding: 8,
  },
  bottomControls: {
    paddingHorizontal: 40,
    paddingBottom: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  incomingActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  connectedActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  defaultActions: {
    alignItems: 'center',
  },
  callAction: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  answerAction: {
    backgroundColor: '#4CAF50',
  },
  rejectAction: {
    backgroundColor: '#F44336',
  },
  endCallAction: {
    backgroundColor: '#F44336',
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeControlButton: {
    backgroundColor: '#F44336',
  },
});

export default VideoCallScreen;
