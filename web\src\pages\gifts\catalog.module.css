.container {
  padding: 2rem 0;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  background: linear-gradient(45deg, #e91e63, #9c27b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.subtitle {
  margin-bottom: 2rem;
}

.filtersCard {
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.balanceInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  background: linear-gradient(45deg, #fff3e0, #fce4ec);
  border-radius: 8px;
}

.giftsGrid {
  margin-bottom: 2rem;
}

.giftCard {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.giftCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.giftImageContainer {
  position: relative;
  overflow: hidden;
}

.giftImage {
  transition: transform 0.3s ease;
}

.giftCard:hover .giftImage {
  transform: scale(1.05);
}

.popularBadge {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 1;
}

.newBadge {
  position: absolute;
  top: 8px;
  right: 48px;
  z-index: 1;
}

.favoriteButton {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  z-index: 1;
}

.favoriteButton:hover {
  background: rgba(255, 255, 255, 1);
}

.giftContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.giftName {
  font-weight: 600;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.giftDescription {
  flex: 1;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.giftFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.giftPrice {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 600;
}

.sendButton {
  border-radius: 20px;
  text-transform: none;
  font-weight: 600;
  padding: 0.5rem 1rem;
}

.categoryChip {
  align-self: flex-start;
  background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
  color: #5e35b1;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.errorAlert,
.emptyAlert {
  margin: 2rem 0;
  border-radius: 12px;
}

.sendGiftContent {
  padding: 1rem 0;
}

.selectedGift {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(45deg, #f8f9fa, #fff);
  border-radius: 12px;
  border: 2px solid #e3f2fd;
}

.selectedGiftImage {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
}

.recipientCard {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  border: 2px solid transparent;
}

.recipientCard:hover {
  border-color: #e3f2fd;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.selectedRecipient {
  border-color: #2196f3 !important;
  background: linear-gradient(45deg, #e3f2fd, #fff);
}

.recipientContent {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0;
  }
  
  .header {
    margin-bottom: 1.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .filtersCard {
    margin-bottom: 1.5rem;
  }
  
  .balanceInfo {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .giftFooter {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
  
  .sendButton {
    width: 100%;
  }
  
  .selectedGift {
    flex-direction: column;
    text-align: center;
  }
  
  .recipientContent {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.75rem;
  }
  
  .giftCard {
    margin-bottom: 1rem;
  }
  
  .paginationContainer {
    margin-top: 2rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .giftCard,
  .giftImage,
  .recipientCard {
    transition: none;
  }
  
  .giftCard:hover {
    transform: none;
  }
  
  .giftCard:hover .giftImage {
    transform: none;
  }
  
  .recipientCard:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .giftCard {
    border: 2px solid #000;
  }
  
  .selectedRecipient {
    border-color: #000 !important;
  }
  
  .popularBadge,
  .newBadge {
    border: 1px solid #000;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .balanceInfo {
    background: linear-gradient(45deg, #2d2d2d, #3d3d3d);
  }
  
  .selectedGift {
    background: linear-gradient(45deg, #2d2d2d, #3d3d3d);
    border-color: #555;
  }
  
  .selectedRecipient {
    background: linear-gradient(45deg, #2d2d2d, #3d3d3d);
  }
  
  .categoryChip {
    background: linear-gradient(45deg, #2d2d2d, #3d3d3d);
    color: #bb86fc;
  }
}
