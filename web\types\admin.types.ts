// Основные типы для админ-панели

export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  newUsersToday: number;
  matchesToday: number;
  matchesGrowth: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  userGrowth: UserGrowthData[];
  matches: MatchesStats;
  revenue: RevenueData[];
}

export interface UserGrowthData {
  date: string;
  newUsers: number;
  activeUsers: number;
}

export interface MatchesStats {
  successful: number;
  rejected: number;
  pending: number;
}

export interface RevenueData {
  month: string;
  amount: number;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  message: string;
  server: ServiceStatus;
  database: ServiceStatus;
  redis: ServiceStatus;
  storage: ServiceStatus;
}

export interface ServiceStatus {
  status: 'healthy' | 'warning' | 'critical';
  responseTime?: number;
  uptime?: number;
  message?: string;
}

export interface RecentActivity {
  id: string;
  type: 'user_registration' | 'user_login' | 'match_created' | 'report_submitted' | 'payment_processed';
  description: string;
  timestamp: string;
  userId?: string;
  metadata?: Record<string, any>;
}

// Типы для управления пользователями
export interface AdminUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  avatar?: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  city: string;
  isVerified: boolean;
  isPremium: boolean;
  isBlocked: boolean;
  blockReason?: string;
  registrationDate: string;
  lastLoginDate?: string;
  profileCompleteness: number;
  photosCount: number;
  matchesCount: number;
  reportsCount: number;
  subscriptionType?: 'basic' | 'premium' | 'vip';
  subscriptionExpiry?: string;
}

export interface UserFilters {
  search?: string;
  gender?: 'male' | 'female' | 'other';
  ageFrom?: number;
  ageTo?: number;
  city?: string;
  isVerified?: boolean;
  isPremium?: boolean;
  isBlocked?: boolean;
  registrationDateFrom?: string;
  registrationDateTo?: string;
  lastLoginDateFrom?: string;
  lastLoginDateTo?: string;
  sortBy?: 'registrationDate' | 'lastLoginDate' | 'matchesCount' | 'reportsCount';
  sortOrder?: 'asc' | 'desc';
}

export interface UserListResponse {
  users: AdminUser[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Типы для отчетов и жалоб
export interface Report {
  id: string;
  reporterId: string;
  reporterName: string;
  reportedUserId: string;
  reportedUserName: string;
  type: 'inappropriate_content' | 'fake_profile' | 'harassment' | 'spam' | 'other';
  reason: string;
  description?: string;
  evidence?: string[];
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  resolution?: string;
  actionTaken?: 'warning' | 'temporary_ban' | 'permanent_ban' | 'content_removal' | 'no_action';
}

export interface ReportFilters {
  search?: string;
  type?: Report['type'];
  status?: Report['status'];
  priority?: Report['priority'];
  assignedTo?: string;
  createdDateFrom?: string;
  createdDateTo?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'priority';
  sortOrder?: 'asc' | 'desc';
}

export interface ReportListResponse {
  reports: Report[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Типы для модерации контента
export interface ModerationItem {
  id: string;
  type: 'photo' | 'profile' | 'message';
  userId: string;
  userName: string;
  content: string | string[];
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
  priority: 'low' | 'medium' | 'high';
  autoModerationScore?: number;
  flags: string[];
}

export interface ModerationFilters {
  type?: ModerationItem['type'];
  status?: ModerationItem['status'];
  priority?: ModerationItem['priority'];
  submittedDateFrom?: string;
  submittedDateTo?: string;
  autoModerationScoreFrom?: number;
  autoModerationScoreTo?: number;
  sortBy?: 'submittedAt' | 'priority' | 'autoModerationScore';
  sortOrder?: 'asc' | 'desc';
}

export interface ModerationListResponse {
  items: ModerationItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Типы для аналитики
export interface AnalyticsData {
  userMetrics: UserMetrics;
  engagementMetrics: EngagementMetrics;
  revenueMetrics: RevenueMetrics;
  performanceMetrics: PerformanceMetrics;
}

export interface UserMetrics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  retentionRate: number;
  churnRate: number;
  userGrowthChart: ChartData[];
  usersByAge: ChartData[];
  usersByGender: ChartData[];
  usersByCity: ChartData[];
}

export interface EngagementMetrics {
  dailyActiveUsers: number;
  averageSessionDuration: number;
  messagesPerDay: number;
  matchesPerDay: number;
  likesPerDay: number;
  engagementChart: ChartData[];
  popularFeatures: ChartData[];
}

export interface RevenueMetrics {
  totalRevenue: number;
  monthlyRevenue: number;
  averageRevenuePerUser: number;
  subscriptionConversionRate: number;
  revenueChart: ChartData[];
  subscriptionTypes: ChartData[];
}

export interface PerformanceMetrics {
  averageResponseTime: number;
  errorRate: number;
  uptime: number;
  serverLoad: number;
  performanceChart: ChartData[];
}

export interface ChartData {
  label: string;
  value: number;
  date?: string;
}

// Типы для настроек системы
export interface SystemSettings {
  general: GeneralSettings;
  security: SecuritySettings;
  notifications: NotificationSettings;
  moderation: ModerationSettings;
  payments: PaymentSettings;
}

export interface GeneralSettings {
  appName: string;
  appDescription: string;
  supportEmail: string;
  maxPhotosPerUser: number;
  minAge: number;
  maxAge: number;
  maxDistanceKm: number;
  maintenanceMode: boolean;
  maintenanceMessage?: string;
}

export interface SecuritySettings {
  passwordMinLength: number;
  passwordRequireSpecialChars: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireUppercase: boolean;
  twoFactorAuthRequired: boolean;
  sessionTimeoutMinutes: number;
  maxLoginAttempts: number;
  lockoutDurationMinutes: number;
}

export interface NotificationSettings {
  emailNotificationsEnabled: boolean;
  pushNotificationsEnabled: boolean;
  smsNotificationsEnabled: boolean;
  marketingEmailsEnabled: boolean;
  notificationRetentionDays: number;
}

export interface ModerationSettings {
  autoModerationEnabled: boolean;
  autoModerationThreshold: number;
  requirePhotoModeration: boolean;
  requireProfileModeration: boolean;
  requireMessageModeration: boolean;
  moderationQueueLimit: number;
}

export interface PaymentSettings {
  stripeEnabled: boolean;
  paypalEnabled: boolean;
  applePay: boolean;
  googlePay: boolean;
  subscriptionPrices: SubscriptionPrice[];
  refundPolicy: string;
}

export interface SubscriptionPrice {
  type: 'basic' | 'premium' | 'vip';
  duration: 'monthly' | 'quarterly' | 'yearly';
  price: number;
  currency: string;
  features: string[];
}

// Общие типы
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface SortParams {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export interface DateRange {
  from: string;
  to: string;
}
